import { BrowserRouter as Router, Routes, Route, useLocation } from 'react-router-dom';

// Layout Components
import { MainLayout } from './components/layout';
import routeConfig from './AppRoutes';

function AppContent() {
  const location = useLocation();
  const isLandingPage = location.pathname === '/';

  return (
    <MainLayout
      showHeader={!isLandingPage}
      showFooter={!isLandingPage}
      showTrustedCompanies={!isLandingPage}
    >
      <Routes>
        {routeConfig.map(({ path, element }) => (
          <Route key={path} path={path} element={element} />
        ))}
      </Routes>
    </MainLayout>
  );
}

function App() {
  return (
    <Router>
      <AppContent />
    </Router>
  );
}

export default App;