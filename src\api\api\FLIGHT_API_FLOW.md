# Flight API Service - Detailed Flow Documentation

## 🔄 API Call Flow Architecture

### 1. **Base API Service Flow**

```
┌─────────────────────────────────────────────────────────────┐
│                    Base API Service                         │
├─────────────────────────────────────────────────────────────┤
│ ┌─────────────────┐    ┌──────────────────┐                │
│ │ Token Manager   │    │ Axios Instance   │                │
│ │ - JWT Storage   │    │ - Interceptors   │                │
│ │ - Validation    │    │ - <PERSON><PERSON><PERSON> Handling │                │
│ │ - Auto Refresh  │    │ - Retry Logic    │                │
│ └─────────────────┘    └──────────────────┘                │
└─────────────────────────────────────────────────────────────┘
                              │
                              ▼
┌─────────────────────────────────────────────────────────────┐
│              Flight API Functions                          │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐          │
│  │  Airport    │ │   Search    │ │  Booking    │          │
│  │ Functions   │ │ Functions   │ │ Functions   │          │
│  └─────────────┘ └─────────────┘ └─────────────┘          │
└─────────────────────────────────────────────────────────────┘
```

### 2. **Flight Search Workflow**

```
User Input
    │
    ▼
┌─────────────────────────────────────────────────────────────┐
│                Search Method Selection                      │
├─────────────────────────────────────────────────────────────┤
│                                                             │
│  🎯 callUnifiedSearch()     ← RECOMMENDED                  │
│  │                                                         │
│  ├─ Has TUI? ──Yes──→ search_list/ ──→ Results            │
│  │     │                                                   │
│  │     No                                                  │
│  │     ▼                                                   │
│  └─ search/ ──→ Get TUI ──→ search_list/ ──→ Results      │
│                                                             │
│  🔄 callSeamlessRefreshSearch()                            │
│  │                                                         │
│  ├─ search_refresh/ ──Success──→ Results                   │
│  │     │                                                   │
│  │   Failed                                                │
│  │     ▼                                                   │
│  └─ callUnifiedSearch() ──→ Results                        │
│                                                             │
│  ⏱️ callPollingSearch()                                     │
│  │                                                         │
│  ├─ callUnifiedSearch() ──→ Initial Response               │
│  │     │                                                   │
│  │   Complete? ──Yes──→ Return Results                     │
│  │     │                                                   │
│  │     No                                                  │
│  │     ▼                                                   │
│  └─ Poll search_list/ ──→ Until Complete ──→ Results       │
│                                                             │
└─────────────────────────────────────────────────────────────┘
```

### 3. **Request/Response Flow**

```
Function Call
    │
    ▼
┌─────────────────────────────────────────────────────────────┐
│                Request Interceptor                         │
├─────────────────────────────────────────────────────────────┤
│ 1. Add timestamp metadata                                  │
│ 2. Check if auth token needed                              │
│ 3. Add Bearer token if required                            │
│ 4. Set standard headers                                     │
└─────────────────────────────────────────────────────────────┘
    │
    ▼
┌─────────────────────────────────────────────────────────────┐
│                   API Call                                 │
├─────────────────────────────────────────────────────────────┤
│ POST/GET to TripJack API                                   │
│ - search/                                                  │
│ - search_list/                                             │
│ - search_refresh/                                          │
│ - details/                                                 │
│ - airports/                                                │
│ - etc.                                                     │
└─────────────────────────────────────────────────────────────┘
    │
    ▼
┌─────────────────────────────────────────────────────────────┐
│               Response Interceptor                         │
├─────────────────────────────────────────────────────────────┤
│ 1. Calculate response time                                  │
│ 2. Add metadata (timestamp, cache hit, etc.)               │
│ 3. Handle 401 (clear token)                                │
│ 4. Retry logic for 5xx errors                              │
│ 5. Return processed response                                │
└─────────────────────────────────────────────────────────────┘
    │
    ▼
Function Returns Result
```

### 4. **Airport Search Flow**

```
searchAirport({ search_text: "Delhi" })
    │
    ▼
┌─────────────────────────────────────────────────────────────┐
│                Airport Search Logic                        │
├─────────────────────────────────────────────────────────────┤
│ 1. Build URLSearchParams                                   │
│    - query: "Delhi"                                        │
│    - limit: "10"                                           │
│                                                             │
│ 2. GET airports/search?query=Delhi&limit=10                │
│                                                             │
│ 3. Extract results array                                    │
│                                                             │
│ 4. Return AirportList[]                                     │
│    [{ code: "DEL", city_name: "Delhi", name: "..." }]      │
└─────────────────────────────────────────────────────────────┘
```

### 5. **Booking Flow**

```
Complete Booking Process
    │
    ▼
┌─────────────────────────────────────────────────────────────┐
│                  Step 1: Search                            │
├─────────────────────────────────────────────────────────────┤
│ searchAirport() → Select Origin/Destination                │
│ callUnifiedSearch() → Get Available Flights                │
└─────────────────────────────────────────────────────────────┘
    │
    ▼
┌─────────────────────────────────────────────────────────────┐
│                Step 2: Flight Details                      │
├─────────────────────────────────────────────────────────────┤
│ getFlightInfo() → Detailed Flight Information              │
│ getFlightSSR() → Special Services (Optional)               │
│ getFlightFareRule() → Fare Rules (Optional)                │
└─────────────────────────────────────────────────────────────┘
    │
    ▼
┌─────────────────────────────────────────────────────────────┐
│                 Step 3: Booking                            │
├─────────────────────────────────────────────────────────────┤
│ bookNowApi() → Create Booking                              │
│ │                                                           │
│ ├─ Validate Enhanced Index                                  │
│ ├─ Extract Fare ID                                          │
│ ├─ POST create-booking/                                     │
│ └─ Return Booking Confirmation                              │
└─────────────────────────────────────────────────────────────┘
    │
    ▼
┌─────────────────────────────────────────────────────────────┐
│               Step 4: Booking Management                   │
├─────────────────────────────────────────────────────────────┤
│ getBookingList() → View All Bookings                       │
│ getBookingDetails() → Detailed Booking Info                │
└─────────────────────────────────────────────────────────────┘
```

### 6. **Error Handling Flow**

```
API Function Call
    │
    ▼
┌─────────────────────────────────────────────────────────────┐
│                  Try Block                                  │
├─────────────────────────────────────────────────────────────┤
│ Execute API call with common axios instance                │
└─────────────────────────────────────────────────────────────┘
    │
    ▼
┌─────────────────────────────────────────────────────────────┐
│                Error Occurred?                             │
├─────────────────────────────────────────────────────────────┤
│                                                             │
│ ┌─ 401 Unauthorized ──→ Clear Token ──→ Throw Error       │
│ │                                                           │
│ ├─ 5xx Server Error ──→ Retry Logic ──→ Success/Fail      │
│ │                                                           │
│ ├─ Network Error ──→ Retry Logic ──→ Success/Fail          │
│ │                                                           │
│ └─ Other Errors ──→ Throw Error                            │
│                                                             │
└─────────────────────────────────────────────────────────────┘
    │
    ▼
┌─────────────────────────────────────────────────────────────┐
│                 Catch Block                                 │
├─────────────────────────────────────────────────────────────┤
│ throw error; // Clean error propagation                    │
└─────────────────────────────────────────────────────────────┘
```

### 7. **Authentication Flow**

```
API Function Call
    │
    ▼
┌─────────────────────────────────────────────────────────────┐
│            Check if Auth Required                          │
├─────────────────────────────────────────────────────────────┤
│                                                             │
│ Public Endpoints (No Auth):                                │
│ ├─ search/, search_list/, search_refresh/                  │
│ ├─ airports/, airports/search                              │
│ ├─ details/, service_req/, rules/                          │
│ └─ setup/, pricing/, assets/                               │
│                                                             │
│ Protected Endpoints (Auth Required):                       │
│ ├─ create-booking/                                          │
│ ├─ user-bookings/                                           │
│ └─ get-booking/                                             │
│                                                             │
└─────────────────────────────────────────────────────────────┘
    │
    ▼
┌─────────────────────────────────────────────────────────────┐
│              Add Auth Header                                │
├─────────────────────────────────────────────────────────────┤
│ IF auth required:                                           │
│   token = tokenManager.getToken()                          │
│   IF token exists:                                          │
│     headers.Authorization = `Bearer ${token}`              │
└─────────────────────────────────────────────────────────────┘
```

### 8. **Data Flow Example**

```
// Complete search to booking flow
searchAirport({ search_text: "Delhi" })
    ↓ Returns: [{ code: "DEL", city_name: "Delhi" }]

callUnifiedSearch({
  Trips: [{ Origin: "DEL", Destination: "BOM", DepartureDate: "2024-12-25" }]
})
    ↓ Returns: { TUI: "abc123", Trips: [{ Journey: [{ Index: "xyz789" }] }] }

getFlightInfo({
  TUI: "abc123",
  Index: "xyz789"
})
    ↓ Returns: { FlightDetails: { ... }, FareDetails: { ... } }

bookNowApi({
  flight_booking: { TUI: "abc123", Trips: [{ Journey: [{ Index: "xyz789" }] }] },
  passenger_details: [{ ... }]
})
    ↓ Returns: { BookingReference: "BOOK123", Status: "Confirmed" }
```

## 🔧 Technical Implementation

### Common Axios Instance
```typescript
// Single instance shared across all functions
const api = baseApiService.getAxiosInstance();

// All functions use this instance
export const searchAirport = async (body) => {
  const response = await api.get(`airports/search?${params}`);
  return response.data.results;
};
```

### Function Structure
```typescript
export const functionName = async (parameters): Promise<ReturnType> => {
  try {
    const response = await api.method('endpoint', data);
    return response.data;
  } catch (error) {
    throw error; // Clean error propagation
  }
};
```

This architecture ensures **clean, individual callable functions** with a **shared axios instance** for optimal performance and consistency.
