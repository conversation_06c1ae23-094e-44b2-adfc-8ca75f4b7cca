import { baseApiService } from './base-api.service';
import type { FLightExpressSearchBody, FlightScheduleBody, FLightSearchListBody, WebSettings } from '../../models/flight/flight-search.model';
import type { FlightResponse } from '../../models/flight/flight-list.model';
import type { FlightFareRuleBody, FlightFareRuleResponse, FlightInfoBody, FlightInfoResponse, FlightSsrBody, FlightSsrResponse, TripJackApiDataInfo } from '../../models/flight/flight-details.model';
import EnhancedIndexHelper from '../../helper/flight/index-helper';
import type { BookingList, BookNowBody, DetailedBookingResponse, FlightBookingListResponse } from '../../models/flight/flight-booking.model';

// Common axios instance from base service
const api = baseApiService.getAxiosInstance();


// ==================== FLIGHT SEARCH OPERATIONS ====================

/**
 * Unified search method implementing correct API workflow
 */
export const callUnifiedSearch = async (body: FLightExpressSearchBody | FlightScheduleBody): Promise<FlightResponse> => {
  try {
    if ('TUI' in body && body.TUI) {
      const listResponse = await api.post<FlightResponse>('search_list/', {
        ClientID: body.ClientID || '',
        TUI: body.TUI
      });
      return listResponse.data;
    }

    const searchResponse = await api.post<FlightResponse>('search/', body);
    const searchData = searchResponse.data;

    if (!searchData.TUI) {
      throw new Error('No TUI received from search endpoint');
    }

    if (searchData.Completed && searchData.Trips && searchData.Trips.length > 0) {
      return searchData;
    }

    const listResponse = await api.post<FlightResponse>('search_list/', {
      ClientID: '',
      TUI: searchData.TUI
    });

    const listData = listResponse.data;
    listData.TUI = searchData.TUI;
    listData.CacheHit = searchData.CacheHit || listData.CacheHit;
    listData.DataSource = searchData.DataSource || listData.DataSource;
    listData.ResponseTimeMS = searchData.ResponseTimeMS || listData.ResponseTimeMS;

    return listData;
  } catch (error) {
    throw error;
  }
};

/**
 * Seamless refresh search with cache optimization
 * Implements exact cache detection pattern: cache_hit === true || CacheHit === true || sh_price === false
 */
export const callSeamlessRefreshSearch = async (body: FLightExpressSearchBody | FlightScheduleBody): Promise<FlightResponse> => {
  try {
    console.log('🔄 callSeamlessRefreshSearch: Starting search_refresh call');
    const startTime = Date.now();
    const response = await api.post<FlightResponse>('search_refresh/', body);
    const responseData = response.data;
    const responseTimeMs = Date.now() - startTime;

    console.log('✅ search_refresh response received:', {
      completed: responseData?.Completed,
      sh_price: responseData?.sh_price,
      hasTUI: !!responseData?.TUI,
      tripsCount: responseData?.Trips?.length || 0,
      hasTrips: !!(responseData?.Trips && responseData.Trips.length > 0)
    });

    if (responseData) {
      // Set default metadata
      responseData.DataSource = responseData.DataSource || 'refresh';
      responseData.ResponseTimeMS = responseTimeMs;

      // Exact cache detection pattern as specified
      const isCacheHit = responseData.cache_hit === true ||
                        responseData.CacheHit === true ||
                        responseData.sh_price === false;

      responseData.CacheHit = isCacheHit;

      // Store TUI in sessionStorage as specified
      if (responseData.TUI) {
        try {
          sessionStorage.setItem('dySearchTuiID', responseData.TUI);
        } catch (e) {
          console.warn('Failed to store TUI in sessionStorage:', e);
        }
      }
    }

    // Check if data needs search_list call: incomplete OR sh_price is false
    const needsSearchList = (!responseData.Completed || responseData.sh_price === false) && responseData.TUI;

    if (needsSearchList) {
      console.log('🔄 search_refresh returned incomplete data or sh_price=false, calling search_list with TUI:', responseData.TUI);

      const listResponse = await api.post<FlightResponse>('search_list/', {
        ClientID: '',
        TUI: responseData.TUI
      });

      const listData = listResponse.data;

      console.log('✅ search_list response received:', {
        completed: listData?.Completed,
        tripsCount: listData?.Trips?.length || 0,
        hasTrips: !!(listData?.Trips && listData.Trips.length > 0),
        hasTUI: !!listData?.TUI
      });

      // Preserve cache metadata from refresh call
      listData.CacheHit = responseData.CacheHit;
      listData.DataSource = responseData.DataSource;
      listData.ResponseTimeMS = responseData.ResponseTimeMS;
      listData.TUI = responseData.TUI;

      console.log('✅ search_list call completed, returning combined data with', listData?.Trips?.length || 0, 'trips');
      return listData;
    }

    console.log('✅ search_refresh data was complete, returning directly with', responseData?.Trips?.length || 0, 'trips');
    return responseData;
  } catch (error) {
    console.warn('callSeamlessRefreshSearch failed, falling back to callUnifiedSearch:', error);
    return callUnifiedSearch(body);
  }
};

/**
 * Polling search to ensure users always get results
 */
export const callPollingSearch = async (
  body: FLightExpressSearchBody | FlightScheduleBody,
  maxAttempts: number = 10,
  pollInterval: number = 1000
): Promise<FlightResponse> => {
  try {
    const initialResponse = await callUnifiedSearch(body);

    if (initialResponse.Completed) {
      return initialResponse;
    }

    if (!initialResponse.TUI) {
      throw new Error('No TUI received for polling');
    }

    let attempts = 0;
    while (attempts < maxAttempts) {
      attempts++;
      await new Promise(resolve => setTimeout(resolve, pollInterval));

      try {
        const pollResponse = await api.post<FlightResponse>('search_list/', {
          ClientID: '',
          TUI: initialResponse.TUI
        });

        const pollData = pollResponse.data;
        pollData.TUI = initialResponse.TUI;
        pollData.CacheHit = initialResponse.CacheHit || pollData.CacheHit;
        pollData.DataSource = initialResponse.DataSource || pollData.DataSource;

        if (pollData.Completed) {
          return pollData;
        }

        if (pollData.Trips && pollData.Trips.length > 0) {
          const hasFlights = pollData.Trips.some((trip: any) =>
            trip.Journey && trip.Journey.length > 0
          );

          if (hasFlights) {
            return pollData;
          }
        }
      } catch (pollError) {
        if (attempts >= maxAttempts) {
          throw pollError;
        }
      }
    }

    return initialResponse;
  } catch (error) {
    throw error;
  }
};

/**
 * Get search results using existing TUI
 */
export const getExpressSearch = async (body: FLightSearchListBody): Promise<FlightResponse> => {
  if (!body.TUI) {
    throw new Error('TUI is required for search_list endpoint');
  }

  const response = await api.post<FlightResponse>('search_list/', body);
  return response.data;
};

/**
 * Legacy method for backward compatibility
 */
export const callExpressSearch = async (body: FLightExpressSearchBody | FlightScheduleBody): Promise<FlightResponse> => {
  return callUnifiedSearch(body);
};

// ==================== CACHE MANAGEMENT UTILITIES ====================

interface CacheMetadata {
  hit: boolean;
  source: 'memory' | 'api' | 'refresh';
  ttl: number;
  timestamp: number;
  responseTimeMs: number;
  dataFreshness: 'fresh' | 'stale' | 'expired';
}

interface CacheConfig {
  ttl: number;
  maxAge: number;
  staleWhileRevalidate: boolean;
  forceRefresh: boolean;
}

const DEFAULT_CACHE_CONFIG: CacheConfig = {
  ttl: 5 * 60 * 1000, // 5 minutes
  maxAge: 15 * 60 * 1000, // 15 minutes
  staleWhileRevalidate: true,
  forceRefresh: false
};

// In-memory cache for flight details
const flightDetailsCache = new Map<string, {
  data: any;
  metadata: CacheMetadata;
  expiresAt: number;
}>();

/**
 * Generate cache key for flight details
 */
const generateCacheKey = (body: FlightInfoBody | FlightSsrBody | FlightFareRuleBody): string => {
  const keyData = {
    TUI: body.TUI,
    Index: body.Index,
    ClientID: body.ClientID,
    type: 'TripType' in body ? body.TripType : 'details'
  };
  return btoa(JSON.stringify(keyData)).replace(/[^a-zA-Z0-9]/g, '');
};

/**
 * Check cache freshness and determine if refresh is needed
 */
const evaluateCacheStatus = (cacheEntry: any, config: CacheConfig): {
  canUse: boolean;
  needsRefresh: boolean;
  freshness: 'fresh' | 'stale' | 'expired';
} => {
  if (!cacheEntry) {
    return { canUse: false, needsRefresh: true, freshness: 'expired' };
  }

  const now = Date.now();
  const age = now - cacheEntry.metadata.timestamp;

  if (config.forceRefresh || age > config.maxAge) {
    return { canUse: false, needsRefresh: true, freshness: 'expired' };
  }

  if (age > config.ttl) {
    return {
      canUse: config.staleWhileRevalidate,
      needsRefresh: true,
      freshness: 'stale'
    };
  }

  return { canUse: true, needsRefresh: false, freshness: 'fresh' };
};

/**
 * Set cache headers for optimized requests
 */
const setCacheHeaders = (config: CacheConfig) => {
  const headers: Record<string, string> = {
    'Cache-Control': `max-age=${Math.floor(config.ttl / 1000)}`,
    'X-Cache-TTL': config.ttl.toString(),
    'X-Cache-Strategy': config.staleWhileRevalidate ? 'stale-while-revalidate' : 'must-revalidate'
  };

  if (config.forceRefresh) {
    headers['Cache-Control'] = 'no-cache, must-revalidate';
    headers['X-Force-Refresh'] = 'true';
  }

  return headers;
};

// ==================== ENHANCED FLIGHT DETAILS OPERATIONS ====================

/**
 * Enhanced flight information with cache optimization
 */
export const getFlightInfo = async (
  body: FlightInfoBody,
  cacheConfig: Partial<CacheConfig> = {}
): Promise<FlightInfoResponse & { _cache: CacheMetadata }> => {
  const config = { ...DEFAULT_CACHE_CONFIG, ...cacheConfig };
  const cacheKey = generateCacheKey(body);
  const startTime = Date.now();

  try {
    // Check cache first
    const cacheEntry = flightDetailsCache.get(cacheKey);
    const cacheStatus = evaluateCacheStatus(cacheEntry, config);

    // Return cached data if available and fresh
    if (cacheStatus.canUse && cacheEntry) {
      console.log('🎯 getFlightInfo: Cache hit, returning cached data');
      return {
        ...cacheEntry.data,
        _cache: {
          ...cacheEntry.metadata,
          hit: true,
          source: 'memory',
          dataFreshness: cacheStatus.freshness
        }
      };
    }

    // Set cache headers
    const headers = setCacheHeaders(config);

    console.log('🔄 getFlightInfo: Making API call with cache headers');
    const response = await api.post<FlightInfoResponse>('details/', body, { headers });
    const responseData = response.data;
    const responseTime = Date.now() - startTime;

    // Detect API-level cache hit
    const apiCacheHit = response.headers['x-cache-hit'] === 'true' ||
                       responseData.cache_hit === true ||
                       responseData.CacheHit === true;

    // Create cache metadata
    const cacheMetadata: CacheMetadata = {
      hit: apiCacheHit,
      source: apiCacheHit ? 'api' : 'refresh',
      ttl: config.ttl,
      timestamp: Date.now(),
      responseTimeMs: responseTime,
      dataFreshness: 'fresh'
    };

    // Cache the response
    flightDetailsCache.set(cacheKey, {
      data: responseData,
      metadata: cacheMetadata,
      expiresAt: Date.now() + config.maxAge
    });

    // Clean up expired cache entries
    setTimeout(() => {
      for (const [key, entry] of flightDetailsCache.entries()) {
        if (Date.now() > entry.expiresAt) {
          flightDetailsCache.delete(key);
        }
      }
    }, 1000);

    return {
      ...responseData,
      _cache: cacheMetadata
    };

  } catch (error) {
    // Return stale data if available during error
    if (cacheEntry && config.staleWhileRevalidate) {
      console.warn('⚠️ getFlightInfo: API error, returning stale cache data');
      return {
        ...cacheEntry.data,
        _cache: {
          ...cacheEntry.metadata,
          hit: true,
          source: 'memory',
          dataFreshness: 'stale'
        }
      };
    }
    throw error;
  }
};

/**
 * Enhanced flight SSR with cache optimization and conditional fetching
 */
export const getFlightSSR = async (
  body: FlightSsrBody,
  cacheConfig: Partial<CacheConfig> = {}
): Promise<FlightSsrResponse & { _cache: CacheMetadata }> => {
  const config = { ...DEFAULT_CACHE_CONFIG, ...cacheConfig };
  const cacheKey = `ssr_${generateCacheKey(body)}`;
  const startTime = Date.now();

  try {
    // Check cache first
    const cacheEntry = flightDetailsCache.get(cacheKey);
    const cacheStatus = evaluateCacheStatus(cacheEntry, config);

    // Return cached data if available and fresh
    if (cacheStatus.canUse && cacheEntry) {
      console.log('🎯 getFlightSSR: Cache hit, returning cached data');
      return {
        ...cacheEntry.data,
        _cache: {
          ...cacheEntry.metadata,
          hit: true,
          source: 'memory',
          dataFreshness: cacheStatus.freshness
        }
      };
    }

    // Set cache headers for conditional fetching
    const headers = setCacheHeaders(config);

    // Add conditional headers based on data freshness
    if (cacheEntry && cacheStatus.freshness === 'stale') {
      headers['If-Modified-Since'] = new Date(cacheEntry.metadata.timestamp).toUTCString();
      headers['X-Cache-Key'] = cacheKey;
    }

    console.log('🔄 getFlightSSR: Making API call with cache headers');
    const response = await api.post('service_req/', body, { headers });
    const responseData = response.data;
    const responseTime = Date.now() - startTime;

    // Handle 304 Not Modified response
    if (response.status === 304 && cacheEntry) {
      console.log('📋 getFlightSSR: Data not modified, refreshing cache TTL');
      const refreshedMetadata = {
        ...cacheEntry.metadata,
        timestamp: Date.now(),
        responseTimeMs: responseTime,
        dataFreshness: 'fresh' as const
      };

      flightDetailsCache.set(cacheKey, {
        ...cacheEntry,
        metadata: refreshedMetadata,
        expiresAt: Date.now() + config.maxAge
      });

      return {
        ...cacheEntry.data,
        _cache: refreshedMetadata
      };
    }

    // Detect API-level cache hit
    const apiCacheHit = response.headers['x-cache-hit'] === 'true' ||
                       responseData.cache_hit === true ||
                       responseData.CacheHit === true;

    // Create cache metadata
    const cacheMetadata: CacheMetadata = {
      hit: apiCacheHit,
      source: apiCacheHit ? 'api' : 'refresh',
      ttl: config.ttl,
      timestamp: Date.now(),
      responseTimeMs: responseTime,
      dataFreshness: 'fresh'
    };

    // Cache the response
    flightDetailsCache.set(cacheKey, {
      data: responseData,
      metadata: cacheMetadata,
      expiresAt: Date.now() + config.maxAge
    });

    return {
      ...responseData,
      _cache: cacheMetadata
    };

  } catch (error) {
    // Return stale data if available during error
    if (cacheEntry && config.staleWhileRevalidate) {
      console.warn('⚠️ getFlightSSR: API error, returning stale cache data');
      return {
        ...cacheEntry.data,
        _cache: {
          ...cacheEntry.metadata,
          hit: true,
          source: 'memory',
          dataFreshness: 'stale'
        }
      };
    }
    throw error;
  }
};

/**
 * Enhanced flight fare rules with TripJack format optimization and cache indicators
 */
export const getFlightFareRule = async (
  body: FlightFareRuleBody,
  cacheConfig: Partial<CacheConfig> = {}
): Promise<FlightFareRuleResponse & { _cache: CacheMetadata }> => {
  const config = { ...DEFAULT_CACHE_CONFIG, ...cacheConfig };
  const cacheKey = `rules_${generateCacheKey(body)}`;
  const startTime = Date.now();

  try {
    // Check cache first
    const cacheEntry = flightDetailsCache.get(cacheKey);
    const cacheStatus = evaluateCacheStatus(cacheEntry, config);

    // Return cached data if available and fresh
    if (cacheStatus.canUse && cacheEntry) {
      console.log('🎯 getFlightFareRule: Cache hit, returning cached data');
      return {
        ...cacheEntry.data,
        _cache: {
          ...cacheEntry.metadata,
          hit: true,
          source: 'memory',
          dataFreshness: cacheStatus.freshness
        }
      };
    }

    // Set cache headers with TripJack optimization
    const headers = {
      ...setCacheHeaders(config),
      'X-TripJack-Format': 'true',
      'Accept': 'application/json',
      'X-Fare-Rule-Version': '2.0'
    };

    // Add performance tracking headers
    if (cacheEntry && cacheStatus.freshness === 'stale') {
      headers['If-Modified-Since'] = new Date(cacheEntry.metadata.timestamp).toUTCString();
      headers['X-Performance-Track'] = 'true';
    }

    console.log('🔄 getFlightFareRule: Making API call with TripJack optimization');
    const response = await api.post('rules/', body, { headers });
    const responseData = response.data;
    const responseTime = Date.now() - startTime;

    // Handle TripJack fare rule format transformation
    let processedData = responseData;
    if (responseData && typeof responseData === 'object') {
      // Handle both array-based and object-based fare rule responses
      if (Array.isArray(responseData.fareRuleInformation)) {
        processedData = {
          ...responseData,
          fareRules: responseData.fareRuleInformation.map((rule: any) => ({
            ruleType: rule.ruleType || rule.type,
            description: rule.description || rule.text,
            penalty: rule.penalty || rule.charge,
            category: rule.category || 'general'
          }))
        };
      } else if (responseData.tfr && Array.isArray(responseData.tfr)) {
        processedData = {
          ...responseData,
          fareRules: responseData.tfr.map((rule: any) => ({
            ruleType: rule.ruleType,
            description: rule.description,
            penalty: rule.penalty,
            category: 'tripjack'
          }))
        };
      }
    }

    // Detect API-level cache hit
    const apiCacheHit = response.headers['x-cache-hit'] === 'true' ||
                       responseData.cache_hit === true ||
                       responseData.CacheHit === true;

    // Create cache metadata with performance tracking
    const cacheMetadata: CacheMetadata = {
      hit: apiCacheHit,
      source: apiCacheHit ? 'api' : 'refresh',
      ttl: config.ttl,
      timestamp: Date.now(),
      responseTimeMs: responseTime,
      dataFreshness: 'fresh'
    };

    // Cache the processed response
    flightDetailsCache.set(cacheKey, {
      data: processedData,
      metadata: cacheMetadata,
      expiresAt: Date.now() + config.maxAge
    });

    return {
      ...processedData,
      _cache: cacheMetadata
    };

  } catch (error) {
    // Return stale data if available during error
    if (cacheEntry && config.staleWhileRevalidate) {
      console.warn('⚠️ getFlightFareRule: API error, returning stale cache data');
      return {
        ...cacheEntry.data,
        _cache: {
          ...cacheEntry.metadata,
          hit: true,
          source: 'memory',
          dataFreshness: 'stale'
        }
      };
    }
    throw error;
  }
};

// ==================== COMPREHENSIVE FLIGHT DETAILS ENDPOINT ====================

interface ComprehensiveFlightDetailsResponse {
  flightInfo: FlightInfoResponse;
  ssrData: FlightSsrResponse;
  fareRules: FlightFareRuleResponse;
  _cache: {
    overall: CacheMetadata;
    breakdown: {
      flightInfo: CacheMetadata;
      ssrData: CacheMetadata;
      fareRules: CacheMetadata;
    };
    consolidationTimeMs: number;
  };
}

/**
 * Comprehensive flight details endpoint that consolidates all flight data
 * with intelligent caching and loading states
 */
export const getComprehensiveFlightDetails = async (
  body: FlightInfoBody,
  options: {
    cacheConfig?: Partial<CacheConfig>;
    includeSSR?: boolean;
    includeFareRules?: boolean;
    parallelFetch?: boolean;
  } = {}
): Promise<ComprehensiveFlightDetailsResponse> => {
  const {
    cacheConfig = {},
    includeSSR = true,
    includeFareRules = true,
    parallelFetch = true
  } = options;

  const config = { ...DEFAULT_CACHE_CONFIG, ...cacheConfig };
  const consolidationStartTime = Date.now();

  try {
    console.log('🚀 getComprehensiveFlightDetails: Starting comprehensive fetch');

    if (parallelFetch) {
      // Parallel fetch for better performance
      const promises = [
        getFlightInfo(body, config)
      ];

      if (includeSSR) {
        promises.push(getFlightSSR(body, config));
      }

      if (includeFareRules) {
        promises.push(getFlightFareRule(body, config));
      }

      const results = await Promise.allSettled(promises);

      // Process results
      const flightInfoResult = results[0];
      const ssrResult = includeSSR ? results[1] : null;
      const fareRulesResult = includeFareRules ? results[includeSSR ? 2 : 1] : null;

      // Handle failed requests gracefully
      const flightInfo = flightInfoResult.status === 'fulfilled'
        ? flightInfoResult.value
        : null;

      const ssrData = ssrResult?.status === 'fulfilled'
        ? ssrResult.value
        : null;

      const fareRules = fareRulesResult?.status === 'fulfilled'
        ? fareRulesResult.value
        : null;

      if (!flightInfo) {
        throw new Error('Failed to fetch essential flight information');
      }

      // Calculate overall cache metadata
      const cacheMetadatas = [flightInfo._cache];
      if (ssrData?._cache) cacheMetadatas.push(ssrData._cache);
      if (fareRules?._cache) cacheMetadatas.push(fareRules._cache);

      const overallCacheHit = cacheMetadatas.every(meta => meta.hit);
      const avgResponseTime = cacheMetadatas.reduce((sum, meta) => sum + meta.responseTimeMs, 0) / cacheMetadatas.length;
      const consolidationTime = Date.now() - consolidationStartTime;

      const overallCacheMetadata: CacheMetadata = {
        hit: overallCacheHit,
        source: overallCacheHit ? 'memory' : 'api',
        ttl: config.ttl,
        timestamp: Date.now(),
        responseTimeMs: avgResponseTime,
        dataFreshness: cacheMetadatas.every(meta => meta.dataFreshness === 'fresh') ? 'fresh' : 'stale'
      };

      console.log(`✅ getComprehensiveFlightDetails: Completed in ${consolidationTime}ms`);

      return {
        flightInfo: flightInfo,
        ssrData: ssrData || {} as FlightSsrResponse,
        fareRules: fareRules || {} as FlightFareRuleResponse,
        _cache: {
          overall: overallCacheMetadata,
          breakdown: {
            flightInfo: flightInfo._cache,
            ssrData: ssrData?._cache || { hit: false, source: 'api', ttl: 0, timestamp: 0, responseTimeMs: 0, dataFreshness: 'expired' },
            fareRules: fareRules?._cache || { hit: false, source: 'api', ttl: 0, timestamp: 0, responseTimeMs: 0, dataFreshness: 'expired' }
          },
          consolidationTimeMs: consolidationTime
        }
      };

    } else {
      // Sequential fetch for better error handling
      console.log('🔄 getComprehensiveFlightDetails: Using sequential fetch');

      const flightInfo = await getFlightInfo(body, config);

      let ssrData: any = {};
      let fareRules: any = {};

      if (includeSSR) {
        try {
          ssrData = await getFlightSSR(body, config);
        } catch (error) {
          console.warn('⚠️ SSR fetch failed, continuing without SSR data:', error);
        }
      }

      if (includeFareRules) {
        try {
          fareRules = await getFlightFareRule(body, config);
        } catch (error) {
          console.warn('⚠️ Fare rules fetch failed, continuing without fare rules:', error);
        }
      }

      const consolidationTime = Date.now() - consolidationStartTime;
      const cacheMetadatas = [flightInfo._cache];
      if (ssrData._cache) cacheMetadatas.push(ssrData._cache);
      if (fareRules._cache) cacheMetadatas.push(fareRules._cache);

      const overallCacheHit = cacheMetadatas.every(meta => meta.hit);
      const avgResponseTime = cacheMetadatas.reduce((sum, meta) => sum + meta.responseTimeMs, 0) / cacheMetadatas.length;

      const overallCacheMetadata: CacheMetadata = {
        hit: overallCacheHit,
        source: overallCacheHit ? 'memory' : 'api',
        ttl: config.ttl,
        timestamp: Date.now(),
        responseTimeMs: avgResponseTime,
        dataFreshness: cacheMetadatas.every(meta => meta.dataFreshness === 'fresh') ? 'fresh' : 'stale'
      };

      console.log(`✅ getComprehensiveFlightDetails: Sequential fetch completed in ${consolidationTime}ms`);

      return {
        flightInfo,
        ssrData,
        fareRules,
        _cache: {
          overall: overallCacheMetadata,
          breakdown: {
            flightInfo: flightInfo._cache,
            ssrData: ssrData._cache || { hit: false, source: 'api', ttl: 0, timestamp: 0, responseTimeMs: 0, dataFreshness: 'expired' },
            fareRules: fareRules._cache || { hit: false, source: 'api', ttl: 0, timestamp: 0, responseTimeMs: 0, dataFreshness: 'expired' }
          },
          consolidationTimeMs: consolidationTime
        }
      };
    }

  } catch (error) {
    console.error('❌ getComprehensiveFlightDetails: Failed to fetch comprehensive data:', error);
    throw error;
  }
};

// ==================== CACHE MANAGEMENT AND UTILITIES ====================

/**
 * Cache invalidation utilities
 */
export const flightDetailsCacheUtils = {
  /**
   * Clear all cached flight details
   */
  clearAll: (): void => {
    flightDetailsCache.clear();
    console.log('🧹 Flight details cache cleared');
  },

  /**
   * Clear cache for specific TUI
   */
  clearByTUI: (tui: string): void => {
    for (const [key, entry] of flightDetailsCache.entries()) {
      if (key.includes(btoa(JSON.stringify({ TUI: tui })).substring(0, 10))) {
        flightDetailsCache.delete(key);
      }
    }
    console.log(`🧹 Cache cleared for TUI: ${tui}`);
  },

  /**
   * Clear expired cache entries
   */
  clearExpired: (): void => {
    const now = Date.now();
    let clearedCount = 0;

    for (const [key, entry] of flightDetailsCache.entries()) {
      if (now > entry.expiresAt) {
        flightDetailsCache.delete(key);
        clearedCount++;
      }
    }

    if (clearedCount > 0) {
      console.log(`🧹 Cleared ${clearedCount} expired cache entries`);
    }
  },

  /**
   * Get cache statistics
   */
  getStats: () => {
    const entries = Array.from(flightDetailsCache.values());
    const now = Date.now();

    const stats = {
      totalEntries: entries.length,
      freshEntries: entries.filter(e => e.metadata.dataFreshness === 'fresh').length,
      staleEntries: entries.filter(e => e.metadata.dataFreshness === 'stale').length,
      expiredEntries: entries.filter(e => now > e.expiresAt).length,
      cacheHitRate: entries.length > 0
        ? entries.filter(e => e.metadata.hit).length / entries.length
        : 0,
      avgResponseTime: entries.length > 0
        ? entries.reduce((sum, e) => sum + e.metadata.responseTimeMs, 0) / entries.length
        : 0,
      memoryUsage: JSON.stringify(Array.from(flightDetailsCache.entries())).length
    };

    return stats;
  },

  /**
   * Force refresh cache for specific key
   */
  forceRefresh: async (body: FlightInfoBody): Promise<void> => {
    const cacheKey = generateCacheKey(body);
    flightDetailsCache.delete(cacheKey);
    flightDetailsCache.delete(`ssr_${cacheKey}`);
    flightDetailsCache.delete(`rules_${cacheKey}`);

    console.log(`🔄 Forced cache refresh for key: ${cacheKey}`);
  },

  /**
   * Preload flight details data
   */
  preload: async (bodies: FlightInfoBody[], options: Partial<CacheConfig> = {}): Promise<void> => {
    console.log(`🚀 Preloading ${bodies.length} flight details entries`);

    const promises = bodies.map(body =>
      getComprehensiveFlightDetails(body, {
        cacheConfig: options,
        parallelFetch: true
      }).catch(error => {
        console.warn('⚠️ Preload failed for body:', body, error);
        return null;
      })
    );

    await Promise.allSettled(promises);
    console.log('✅ Preload completed');
  }
};

/**
 * Performance monitoring for flight details API
 */
export const flightDetailsPerformance = {
  /**
   * Track API performance metrics
   */
  trackMetrics: (metadata: CacheMetadata, endpoint: string): void => {
    // Log performance metrics for monitoring
    if (typeof window !== 'undefined' && (window as any).gtag) {
      (window as any).gtag('event', 'flight_details_api_call', {
        endpoint,
        cache_hit: metadata.hit,
        response_time: metadata.responseTimeMs,
        data_freshness: metadata.dataFreshness,
        cache_source: metadata.source
      });
    }

    // Console logging for development
    if (process.env.NODE_ENV === 'development') {
      console.log(`📊 ${endpoint} Performance:`, {
        cacheHit: metadata.hit,
        responseTime: `${metadata.responseTimeMs}ms`,
        dataFreshness: metadata.dataFreshness,
        source: metadata.source
      });
    }
  },

  /**
   * Get performance summary
   */
  getSummary: () => {
    const stats = flightDetailsCacheUtils.getStats();
    return {
      ...stats,
      performanceGrade: stats.cacheHitRate > 0.8 ? 'A' :
                       stats.cacheHitRate > 0.6 ? 'B' :
                       stats.cacheHitRate > 0.4 ? 'C' : 'D',
      recommendations: [
        stats.cacheHitRate < 0.5 && 'Consider increasing cache TTL',
        stats.avgResponseTime > 2000 && 'API response times are high',
        stats.expiredEntries > stats.totalEntries * 0.3 && 'Clean up expired cache entries'
      ].filter(Boolean)
    };
  }
};

/**
 * Enhanced comprehensive flight details with TripJack integration
 * This replaces the existing getComprehensiveFlightDetails with cache optimization
 */
export const getEnhancedComprehensiveFlightDetails = async (
  body: FlightInfoBody,
  options: {
    cacheConfig?: Partial<CacheConfig>;
    includeSSR?: boolean;
    includeFareRules?: boolean;
    useTripJackFormat?: boolean;
    parallelFetch?: boolean;
  } = {}
): Promise<TripJackApiDataInfo & { _cache: CacheMetadata }> => {
  const {
    cacheConfig = {},
    includeSSR = true,
    includeFareRules = true,
    useTripJackFormat = true,
    parallelFetch = true
  } = options;

  const config = { ...DEFAULT_CACHE_CONFIG, ...cacheConfig };
  const cacheKey = `comprehensive_${generateCacheKey(body)}`;
  const startTime = Date.now();

  try {
    // Check cache first
    const cacheEntry = flightDetailsCache.get(cacheKey);
    const cacheStatus = evaluateCacheStatus(cacheEntry, config);

    if (cacheStatus.canUse && cacheEntry) {
      console.log('🎯 getEnhancedComprehensiveFlightDetails: Cache hit, returning cached data');
      return {
        ...cacheEntry.data,
        _cache: {
          ...cacheEntry.metadata,
          hit: true,
          source: 'memory',
          dataFreshness: cacheStatus.freshness
        }
      };
    }

    // Set enhanced headers for TripJack integration
    const headers = {
      ...setCacheHeaders(config),
      'X-TripJack-Integration': 'true',
      'X-Include-SSR': includeSSR.toString(),
      'X-Include-Fare-Rules': includeFareRules.toString(),
      'X-Parallel-Fetch': parallelFetch.toString(),
      'Accept': 'application/json',
      'X-Response-Format': useTripJackFormat ? 'tripjack' : 'legacy'
    };

    console.log('🚀 getEnhancedComprehensiveFlightDetails: Making enhanced API call');

    // Try the comprehensive endpoint first
    try {
      const response = await api.post<TripJackApiDataInfo>('comprehensive-flight-details/', body, { headers });
      const responseData = response.data;
      const responseTime = Date.now() - startTime;

      // Detect cache hit from API response
      const apiCacheHit = response.headers['x-cache-hit'] === 'true' ||
                         responseData.cache_hit === true ||
                         responseData.CacheHit === true;

      const cacheMetadata: CacheMetadata = {
        hit: apiCacheHit,
        source: apiCacheHit ? 'api' : 'refresh',
        ttl: config.ttl,
        timestamp: Date.now(),
        responseTimeMs: responseTime,
        dataFreshness: 'fresh'
      };

      // Cache the response
      flightDetailsCache.set(cacheKey, {
        data: responseData,
        metadata: cacheMetadata,
        expiresAt: Date.now() + config.maxAge
      });

      // Track performance
      flightDetailsPerformance.trackMetrics(cacheMetadata, 'comprehensive-flight-details');

      return {
        ...responseData,
        _cache: cacheMetadata
      };

    } catch (comprehensiveError) {
      console.warn('⚠️ Comprehensive endpoint failed, falling back to individual calls:', comprehensiveError);

      // Fallback to individual API calls
      const fallbackResult = await getComprehensiveFlightDetails(body, {
        cacheConfig: config,
        includeSSR,
        includeFareRules,
        parallelFetch
      });

      // Transform to TripJack format if needed
      if (useTripJackFormat) {
        const transformedData: TripJackApiDataInfo = {
          Trips: [{
            Journey: [{
              Provider: 'Fallback',
              Segments: [] // Transform from fallbackResult.flightInfo if needed
            }]
          }],
          extra: {
            tripjack_response: {
              searchQuery: {
                paxInfo: {
                  ADULT: fallbackResult.flightInfo.ADT || 1,
                  CHILD: fallbackResult.flightInfo.CHD || 0,
                  INFANT: fallbackResult.flightInfo.INF || 0
                }
              }
            }
          }
        };

        const fallbackCacheMetadata: CacheMetadata = {
          hit: fallbackResult._cache.overall.hit,
          source: 'api',
          ttl: config.ttl,
          timestamp: Date.now(),
          responseTimeMs: fallbackResult._cache.consolidationTimeMs,
          dataFreshness: 'fresh'
        };

        // Cache the transformed result
        flightDetailsCache.set(cacheKey, {
          data: transformedData,
          metadata: fallbackCacheMetadata,
          expiresAt: Date.now() + config.maxAge
        });

        return {
          ...transformedData,
          _cache: fallbackCacheMetadata
        };
      }

      throw comprehensiveError;
    }

  } catch (error) {
    // Return stale data if available during error
    if (cacheEntry && config.staleWhileRevalidate) {
      console.warn('⚠️ getEnhancedComprehensiveFlightDetails: API error, returning stale cache data');
      return {
        ...cacheEntry.data,
        _cache: {
          ...cacheEntry.metadata,
          hit: true,
          source: 'memory',
          dataFreshness: 'stale'
        }
      };
    }
    throw error;
  }
};

/**
 * Intelligent loading state manager for flight details
 */
export const createFlightDetailsLoadingManager = () => {
  let loadingState = {
    isLoading: false,
    error: null as string | null,
    loadingStage: 'initial' as 'initial' | 'fetching' | 'processing' | 'complete',
    retryCount: 0,
    cacheStatus: undefined as { hit: boolean; source: string; freshness: string } | undefined
  };

  const updateLoadingState = (updates: Partial<typeof loadingState>) => {
    loadingState = { ...loadingState, ...updates };
    return loadingState;
  };

  const fetchWithLoadingStates = async (
    body: FlightInfoBody,
    options: Parameters<typeof getEnhancedComprehensiveFlightDetails>[1] = {}
  ) => {
    updateLoadingState({
      isLoading: true,
      error: null,
      loadingStage: 'fetching',
      cacheStatus: undefined
    });

    try {
      const result = await getEnhancedComprehensiveFlightDetails(body, options);

      updateLoadingState({
        isLoading: false,
        loadingStage: 'complete',
        cacheStatus: {
          hit: result._cache.hit,
          source: result._cache.source,
          freshness: result._cache.dataFreshness
        }
      });

      return result;

    } catch (error) {
      updateLoadingState({
        isLoading: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        loadingStage: 'initial',
        retryCount: loadingState.retryCount + 1
      });
      throw error;
    }
  };

  return {
    getState: () => loadingState,
    updateState: updateLoadingState,
    fetchWithStates: fetchWithLoadingStates,
    reset: () => {
      loadingState = {
        isLoading: false,
        error: null,
        loadingStage: 'initial',
        retryCount: 0,
        cacheStatus: undefined
      };
    }
  };
};

// Auto cleanup expired cache entries every 5 minutes
if (typeof window !== 'undefined') {
  setInterval(() => {
    flightDetailsCacheUtils.clearExpired();
  }, 5 * 60 * 1000);
}

// ==================== CONFIGURATION OPERATIONS ====================

/**
 * Get web settings configuration
 */
export const getWebSettings = async (): Promise<WebSettings> => {
  try {
    const body = { "ClientID": "", "TUI": "" };
    const response = await api.post<WebSettings>('setup/', body);
    return response.data;
  } catch (error) {
    throw error;
  }
};

// ==================== PRICING OPERATIONS ====================

/**
 * Get trip values for smart pricing
 */
export const getTripValues = async (): Promise<any> => {
  try {
    const response = await api.get('assets/jsons/flightSmartPricer.json');
    return response.data;
  } catch (error) {
    throw error;
  }
};

/**
 * Call smart pricer API
 */
export const callSmartPricer = async (body: any): Promise<any> => {
  try {
    const response = await api.post('pricing/', body);
    return response.data;
  } catch (error) {
    throw error;
  }
};

/**
 * Get pricing list
 */
export const callGetsPrice = async (body: any): Promise<any> => {
  try {
    const response = await api.post('pricing_list/', body);
    return response.data;
  } catch (error) {
    throw error;
  }
};

// ==================== ADDITIONAL SERVICES ====================

/**
 * Get travel checklist
 */
export const callTravelChecklist = async (body: any): Promise<any> => {
  try {
    const response = await api.post('fetchservice/', body);
    return response.data;
  } catch (error) {
    throw error;
  }
};

/**
 * Get flight SSR services
 */
export const callFlightSSR = async (body: any): Promise<any> => {
  try {
    const response = await api.post('service_req/', body);
    return response.data;
  } catch (error) {
    throw error;
  }
};

/**
 * Get flight seat selection
 */
export const callFlightSeat = async (body: any): Promise<any> => {
  try {
    const response = await api.post('seat/', body);
    return response.data;
  } catch (error) {
    throw error;
  }
};

// ==================== BOOKING OPERATIONS ====================

/**
 * Retrieve existing booking
 */
export const getRetrieveBooking = async (body: any): Promise<any> => {
  try {
    const response = await api.post('rb/', body);
    return response.data;
  } catch (error) {
    throw error;
  }
};

/**
 * Create new booking
 */
export const bookNowApi = async (body: BookNowBody): Promise<any> => {
  try {
    const journeyIndex = body.flight_booking?.Trips?.[0]?.Journey?.[0]?.Index;
    if (journeyIndex) {
      const fareId = EnhancedIndexHelper.extractFareId(journeyIndex);
      EnhancedIndexHelper.validateTripJackFareId(fareId);
    }

    const response = await api.post('create-booking/', body);
    return response.data;
  } catch (error) {
    throw error;
  }
};

/**
 * Get user booking list
 */
export const getBookingList = async (): Promise<BookingList[]> => {
  try {
    const response = await api.get<BookingList[]>('user-bookings/');
    return response.data;
  } catch (error) {
    throw error;
  }
};

/**
 * Get detailed booking information
 */
export const getBookingDetails = async (bookingReference: string): Promise<DetailedBookingResponse> => {
  try {
    const response = await api.get<DetailedBookingResponse>(`get-booking/${bookingReference}`);
    return response.data;
  } catch (error) {
    throw error;
  }
};

// ==================== FARE COMPARISON OPERATIONS ====================

/**
 * Get fare comparison data
 */
export const getFareComparison = async (body: {
  route: string;
  currentFare: number;
  departureDate?: string;
  airline?: string;
}): Promise<any> => {
  try {
    const response = await api.post('fare-comparison/', body);
    return response.data;
  } catch (error) {
    return null;
  }
};

/**
 * Get fare calendar data
 */
export const getFareCalendar = async (body: {
  route: string;
  startDate: string;
  endDate: string;
}): Promise<any> => {
  try {
    const response = await api.post('fare-calendar/', body);
    return response.data;
  } catch (error) {
    return null;
  }
};