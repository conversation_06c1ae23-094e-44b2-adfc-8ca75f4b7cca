/**
 * Flight Search Payload Examples
 * 
 * This file demonstrates the exact usage of the flight search payload structure
 * as implemented in the project.
 */

import { scheduleBodySet } from '../utils/flightPayloadUtils';
import type { FormSearch, ScheduleBody, ExpressSearchBody, GetExpressSearchBody } from '../models/flight/flight-search.model';

// Example 1: One-Way Flight Search
export const oneWaySearchExample = (): ScheduleBody => {
  const formValue: FormSearch = {
    FareType: 'ON',
    SecType: 'Flight',
    travellers: {
      adult: 2,
      child: 1,
      infant: 0
    },
    cabin: 'E', // Economy
    trips: [
      {
        from: {
          city: 'New York',
          airport: 'John F. Kennedy International Airport',
          iata: 'JFK',
          country: 'United States',
          airportOpen: false
        },
        to: {
          city: 'Los Angeles',
          airport: 'Los Angeles International Airport',
          iata: 'LAX',
          country: 'United States',
          airportOpen: false
        },
        depart: new Date('2024-12-25'),
        return: undefined
      }
    ]
  };

  return scheduleBodySet(formValue, 'ON');
};

// Example 2: Round-Trip Flight Search
export const roundTripSearchExample = (): ScheduleBody => {
  const formValue: FormSearch = {
    FareType: 'RT',
    SecType: 'Flight',
    travellers: {
      adult: 1,
      child: 0,
      infant: 0
    },
    cabin: 'B', // Business
    trips: [
      {
        from: {
          city: 'London',
          airport: 'Heathrow Airport',
          iata: 'LHR',
          country: 'United Kingdom',
          airportOpen: false
        },
        to: {
          city: 'Dubai',
          airport: 'Dubai International Airport',
          iata: 'DXB',
          country: 'United Arab Emirates',
          airportOpen: false
        },
        depart: new Date('2024-12-20'),
        return: new Date('2024-12-30')
      }
    ]
  };

  return scheduleBodySet(formValue, 'RT');
};

// Example 3: Multi-City Flight Search
export const multiCitySearchExample = (): ScheduleBody => {
  const formValue: FormSearch = {
    FareType: 'MC',
    SecType: 'Flight',
    travellers: {
      adult: 2,
      child: 0,
      infant: 1
    },
    cabin: 'PE', // Premium Economy
    trips: [
      {
        from: {
          city: 'New York',
          airport: 'John F. Kennedy International Airport',
          iata: 'JFK',
          country: 'United States',
          airportOpen: false
        },
        to: {
          city: 'London',
          airport: 'Heathrow Airport',
          iata: 'LHR',
          country: 'United Kingdom',
          airportOpen: false
        },
        depart: new Date('2024-12-15'),
        return: undefined
      },
      {
        from: {
          city: 'London',
          airport: 'Heathrow Airport',
          iata: 'LHR',
          country: 'United Kingdom',
          airportOpen: false
        },
        to: {
          city: 'Paris',
          airport: 'Charles de Gaulle Airport',
          iata: 'CDG',
          country: 'France',
          airportOpen: false
        },
        depart: new Date('2024-12-20'),
        return: undefined
      },
      {
        from: {
          city: 'Paris',
          airport: 'Charles de Gaulle Airport',
          iata: 'CDG',
          country: 'France',
          airportOpen: false
        },
        to: {
          city: 'New York',
          airport: 'John F. Kennedy International Airport',
          iata: 'JFK',
          country: 'United States',
          airportOpen: false
        },
        depart: new Date('2024-12-25'),
        return: undefined
      }
    ]
  };

  return scheduleBodySet(formValue, 'MC');
};

// Example 4: Express Search Body (for existing TUI)
export const expressSearchBodyExample = (): ExpressSearchBody => {
  return {
    ClientID: '',
    TUI: 'existing-tui-12345',
    Source: 'CF',
    Mode: 'AS',
    FareType: 'RT'
  };
};

// Example 5: Get Express Search Body (for polling)
export const getExpressSearchBodyExample = (): GetExpressSearchBody => {
  return {
    ClientID: '',
    TUI: 'existing-tui-12345'
  };
};

// Example 6: Expected Payload Structure Output
export const expectedPayloadStructure = (): ScheduleBody => {
  return {
    SecType: 'Flight',
    FareType: 'RT',
    ADT: 2,
    CHD: 1,
    INF: 0,
    Cabin: 'E',
    Source: 'CF',
    Mode: 'AS',
    ClientID: '',
    IsMultipleCarrier: false,
    IsRefundable: false,
    preferedAirlines: null,
    TUI: '',
    YTH: 0,
    Trips: [
      {
        From: 'JFK',
        FromArptName: 'John F. Kennedy International Airport',
        FromCity: 'New York',
        OnwardDate: '2024-12-25',
        OrderId: 1,
        ReturnDate: '2024-12-30',
        To: 'LAX',
        ToArptName: 'Los Angeles International Airport',
        ToCity: 'Los Angeles',
        TUI: ''
      }
    ],
    Parameters: {
      Airlines: '',
      GroupType: '',
      IsDirect: false,
      IsNearbyAirport: true,
      Refundable: ''
    }
  };
};

// Usage Examples:
console.log('One-Way Search Payload:', oneWaySearchExample());
console.log('Round-Trip Search Payload:', roundTripSearchExample());
console.log('Multi-City Search Payload:', multiCitySearchExample());
console.log('Express Search Body:', expressSearchBodyExample());
console.log('Get Express Search Body:', getExpressSearchBodyExample());
