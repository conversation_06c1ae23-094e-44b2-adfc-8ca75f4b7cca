import type { TripTypeValue } from "./common-flight.model";

// Exact interface definitions as specified
export interface FormSearch {
  FareType: string;
  travellers: Travellers;
  cabin: string;
  SecType: string;
  trips: TripsForm[];
}

export interface TripsForm {
  from: FromToForm;
  to: FromToForm;
  depart: Date;
  return: Date | undefined;
}

export interface FromToForm {
  city: string;
  airport: string;
  iata: string;
  country: string;
  airportOpen: boolean;
}

export interface Travellers {
  adult: number;
  child: number;
  infant: number;
}

// Legacy interfaces for backward compatibility
export interface flight_trip {
    from: {
      city: string;
      airport: string;
      iata: string;
      country: string;
      airportOpen: boolean;
    };
    to: {
      city: string;
      airport: string;
      iata: string;
      country: string;
      airportOpen: boolean;
    };
    depart: Date;
    return: Date | null;
    isCalenderOpen: boolean;
  }

export interface passengers {
  adult: number;
  child: number;
  infant: number;
}

export type ClassType = 'E' | 'PE' | 'B' | 'F';
export type TravellerType = 'adult' | 'child' | 'infant';

export interface flight_search_form {
    FareType: TripTypeValue;
    travellers: passengers;
    cabin: ClassType;
    SecType: string;
    trips: flight_trip[];
}

// Exact interface definitions as specified
export interface ScheduleBody {
  SecType: string
  FareType: string
  ADT: number
  CHD: number
  INF: number
  Cabin: string
  Source: string
  Mode: string
  ClientID: string
  IsMultipleCarrier: boolean
  IsRefundable: boolean
  preferedAirlines: string | null
  TUI: string
  YTH: number
  Trips: Trip[]
  Parameters: Parameters
  PaymentType?: string
}

export interface Trip {
  From: string
  FromArptName?: string
  FromCity?: string
  OnwardDate: string | null
  OrderId?: number
  ReturnDate?: string | null
  To: string
  ToArptName?: string
  ToCity?: string
  TUI: string
}

export interface Parameters {
  Airlines: string
  GroupType: string
  Refundable: string
  IsDirect: boolean
  IsNearbyAirport: boolean
}

export interface ExpressSearchBody {
  ClientID: string
  TUI: string | null
  Source: string
  Mode: string
  FareType?: string
}

export interface GetExpressSearchBody {
  ClientID: string
  TUI: string | null
}

// Legacy interfaces for backward compatibility
export interface FLightExpressSearchBody {
    ClientID: string
    TUI: string | null
    Source: string
    Mode: string
    FareType?: string
}

export interface FLightSearchListBody{
    ClientID: string
    TUI: string | null
}

export interface FlightScheduleBody {
    SecType: string
    FareType: string
    ADT: number
    CHD: number
    INF: number
    Cabin: string
    Source: string
    Mode: string
    ClientID: string
    IsMultipleCarrier: boolean
    IsRefundable: boolean
    preferedAirlines: string | null
    TUI: string
    Trips: flight_trip[]
    Parameters: Parameters
    PaymentType?: string
    YTH: number
}

export interface WebSettings {
    Code: string
    Msg: string[]
    TUI: string
    Settings: Setting[]
}

export interface Setting {
    Key: string
    Value?: string
}
