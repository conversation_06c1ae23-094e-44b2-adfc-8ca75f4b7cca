/* Calendar Modal Content Styling - Positioning handled by parent */

/* Calendar modal wrapper - Applied to parent container */
.calendar-modal-wrapper {
  /* Smart height calculation - use available space or fallback */
  max-height: var(--calendar-dropdown-max-height, 600px);

  /* Custom scrollbar styling - more visible (same as TravelClassPicker) */
  &::-webkit-scrollbar {
    width: 8px;
  }

  &::-webkit-scrollbar-track {
    background: #f1f5f9;
    border-radius: 4px;
    margin: 4px 0;
  }

  &::-webkit-scrollbar-thumb {
    background: #94a3b8;
    border-radius: 4px;
    border: 1px solid #e2e8f0;

    &:hover {
      background: #64748b;
    }
  }

  /* Firefox scrollbar */
  scrollbar-width: thin;
  scrollbar-color: #94a3b8 #f1f5f9;

  /* Responsive adjustments */
  @media (max-width: 768px) {
    max-height: calc(100vh - var(--calendar-dropdown-top, 150px) - 1rem);
  }
}

/* Calendar Header */
.calendar-header {
  padding: 1rem;
  border-bottom: 1px solid #e2e8f0;
  background: #f8fafc;
  border-radius: 0.75rem 0.75rem 0 0;
}

/* Calendar Content */
.calendar-content {
  padding: 1rem;
}

/* Calendar Months Container */
.calendar-months-container {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;

  @media (min-width: 768px) {
    flex-direction: row;
    gap: 2rem;
  }
}

/* Days Input Section */
.days-input-section {
  margin-top: 1rem;
  padding: 0.75rem;
  background: #f1f5f9;
  border-radius: 0.5rem;
  border: 1px solid #e2e8f0;
}

/* Calendar Grid Adjustments for Compact Design */
.calendar-days-grid {
  gap: 0.25rem;

  button {
    height: 2.5rem;
    font-size: 0.75rem;

    /* Target price text elements */
    span[class*="text-[10px]"] {
      font-size: 0.625rem;
    }
  }
}

/* Calendar Header Background */
.calendar-header-background {
  background: #f1f5f9;
  margin-bottom: 0.5rem;
}

/* Calendar Weekdays Grid */
.calendar-weekdays-grid {
  gap: 0.25rem;

  div {
    padding: 0.25rem;
    font-size: 0.75rem;
  }
}

/* Positioning is now handled by parent component */

/* Responsive adjustments for calendar content */
@media (max-width: 768px) {
  .calendar-months-container {
    gap: 1rem;
  }

  .calendar-days-grid {
    button {
      height: 2rem;
      font-size: 0.625rem;

      /* Target price text elements */
      span[class*="text-[10px]"] {
        font-size: 0.5rem;
      }
    }
  }

  .calendar-header {
    padding: 0.75rem;

    h3 {
      font-size: 0.875rem;
    }

    p {
      font-size: 0.75rem;
    }
  }

  .calendar-content {
    padding: 0.75rem;
  }

  .days-input-section {
    padding: 0.5rem;

    h4, p {
      font-size: 0.75rem;
    }

    input {
      width: 2.5rem;
      padding: 0.25rem;
    }
  }
}

/* Very small screens */
@media (max-width: 480px) {
  .calendar-dropdown {
    .calendar-modal-container {
      min-width: unset;
      left: -0.5rem;
      right: -0.5rem;
      width: calc(100% + 1rem);
    }

    .calendar-months-container {
      flex-direction: column;
      gap: 0.75rem;
    }

    .calendar-days-grid {
      button {
        height: 1.75rem;
        font-size: 0.625rem;
      }
    }
  }
}
