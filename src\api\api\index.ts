/**
 * API Services Index
 * Central export point for all API services
 */

// Base API service and types
export { BaseApiService, type ApiError, type TokenManager } from './base-api.service';

// Flight API functions - individual callable functions
export * as FlightApi from './flight-api.service';

// Authentication API functions - individual callable functions
export * as AuthApi from './auth-api.service';

// Import individual functions for legacy compatibility
import * as FlightApiService from './flight-api.service';
import * as AuthApiService from './auth-api.service';

/**
 * Legacy compatibility - maintains backward compatibility with existing code
 */
export const FlightApiServiceLegacy = {
  // Token management (delegated to auth service)
  setToken: (token: string) => AuthApiService.setToken(token),
  getToken: () => AuthApiService.getToken(),
  clearToken: () => AuthApiService.clearToken(),

  // Airport operations
  searchAirport: (body: any) => FlightApiService.searchAirport(body),
  getAllAirports: () => FlightApiService.getAllAirports(),

  // Flight search operations
  callUnifiedSearch: (body: any) => FlightApiService.callUnifiedSearch(body),
  callSeamlessRefreshSearch: (body: any) => FlightApiService.callSeamlessRefreshSearch(body),
  callPollingSearch: (body: any, maxAttempts?: number, pollInterval?: number) =>
    FlightApiService.callPollingSearch(body, maxAttempts, pollInterval),
  getExpressSearch: (body: any) => FlightApiService.getExpressSearch(body),
  callExpressSearch: (body: any) => FlightApiService.callExpressSearch(body),

  // Flight details operations
  getFlightInfo: (body: any) => FlightApiService.getFlightInfo(body),
  getFlightSSR: (body: any) => FlightApiService.getFlightSSR(body),
  getFlightFareRule: (body: any) => FlightApiService.getFlightFareRule(body),

  // Configuration operations
  getWebSettings: () => FlightApiService.getWebSettings(),

  // Pricing operations
  getTripValues: () => FlightApiService.getTripValues(),
  callSmartPricer: (body: any) => FlightApiService.callSmartPricer(body),
  callGetsPrice: (body: any) => FlightApiService.callGetsPrice(body),

  // Additional services
  callTravelChecklist: (body: any) => FlightApiService.callTravelChecklist(body),
  callFlightSSR: (body: any) => FlightApiService.callFlightSSR(body),
  callFlightSeat: (body: any) => FlightApiService.callFlightSeat(body),

  // Booking operations
  getRetrieveBooking: (body: any) => FlightApiService.getRetrieveBooking(body),
  bookNowApi: (body: any) => FlightApiService.bookNowApi(body),
  getBookingList: () => FlightApiService.getBookingList(),
  getBookingDetails: (bookingReference: string) => FlightApiService.getBookingDetails(bookingReference),

  // Fare comparison operations
  getFareComparison: (body: any) => FlightApiService.getFareComparison(body),
  getFareCalendar: (body: any) => FlightApiService.getFareCalendar(body),

  // Authentication operations
  registerSubmit: (body: any) => AuthApiService.registerSubmit(body),
  loginSubmit: (body: any) => AuthApiService.loginSubmit(body),
  verifyOtp: (body: any) => AuthApiService.verifyOtp(body),
  verifyLoginOtp: (body: any) => AuthApiService.verifyLoginOtp(body),
};

/**
 * Default export for backward compatibility
 */
export default FlightApiServiceLegacy;

/**
 * API Service Factory
 */
export class ApiServiceFactory {
  static initializeServices(): void {
    // Services are now functional, no initialization needed
  }

  static async healthCheck(): Promise<{
    flight: boolean;
    auth: boolean;
    overall: boolean;
  }> {
    const results = {
      flight: false,
      auth: false,
      overall: false,
    };

    try {
      // Test if flight functions are available
      if (typeof FlightApiService.searchAirport === 'function') {
        results.flight = true;
      }
    } catch (error) {
      // Service health check failed
    }

    try {
      // Test if auth functions are available
      if (typeof AuthApiService.getToken === 'function') {
        results.auth = true;
      }
    } catch (error) {
      // Service health check failed
    }

    results.overall = results.flight && results.auth;
    return results;
  }
}

/**
 * Utility functions for common API operations
 */
export const ApiUtils = {
  isAuthenticated: (): boolean => {
    return AuthApiService.isAuthenticated();
  },

  logout: (): void => {
    AuthApiService.logout();
  },

  getToken: (): string | null => {
    return AuthApiService.getToken();
  },

  setToken: (token: string): void => {
    AuthApiService.setToken(token);
  },
};
