// Flight List Models - Complete implementation for flight search functionality

// Flight List Models
export interface FlightListItem {
  id: string;                    // Unique identifier for the flight
  flightNumber?: string;         // Flight number for display (e.g., "6E2775")
  airline: {
    code: string;                // Airline code (e.g., "6E")
    name: string;                // Airline name (e.g., "IndiGo")
    logo: string;                // URL to airline logo
  };
  departure: {
    airport: string;             // Airport code (e.g., "DEL")
    terminal?: string;           // Terminal number/letter
    city: string;                // City name
    time: string;                // Departure time (e.g., "07:30")
    date: string;                // Departure date (e.g., "2023-05-15")
  };
  arrival: {
    airport: string;             // Airport code (e.g., "BOM")
    terminal?: string;           // Terminal number/letter
    city: string;                // City name
    time: string;                // Arrival time (e.g., "09:45")
    date: string;                // Arrival date (e.g., "2023-05-15")
  };
  duration: string;              // Flight duration (e.g., "2h 15m")
  stops: number;                 // Number of stops (0 for direct)
  connections: Connection[];     // Connection details for multi-stop flights
  price: {
    amount: number;              // Price amount
    currency: string;            // Currency code (e.g., "INR")
    baseAmount: number;          // Base fare without taxes
    taxes: number;               // Tax amount
  };
  availability: {
    seats: number;               // Available seats
    cabinClass: string;          // Cabin class (e.g., "Economy")
  };
  refundable: boolean;           // Whether the flight is refundable
  baggage: {
    cabin: string;               // Cabin baggage allowance (e.g., "7 kg")
    checked: string;             // Checked baggage allowance (e.g., "15 kg")
  };
  fareType: string;              // Fare type (e.g., "REGULAR", "SPECIAL_RETURN")
  fareRestrictions?: FareRestrictions; // Optional fare restrictions
  isVisible: boolean;            // Whether to display this flight
  isSelected: boolean;           // Whether this flight is selected (for round trips)
  sh_price: boolean;             // Whether price is finalized (true) or needs shimmer (false)
  performanceData?: {            // Optional performance tracking data
    cacheHit: boolean;
    liveRefresh: boolean;
    responseTimeMs: number;
  };
}

// Connection details for multi-stop flights
export interface Connection {
  airport: string;               // Connection airport code
  airportName: string;           // Connection airport name
  arrivalTime: string;           // Arrival time at connection
  departureTime: string;         // Departure time from connection
  layoverDuration: string;       // Layover duration (e.g., "1h 30m")
  terminal?: {                   // Optional terminal information
    arrival?: string;
    departure?: string;
  };
}

// Fare restrictions model
export interface FareRestrictions {
  fareIdentifier: string;        // PUBLISHED, SPECIAL_RETURN, etc.
  fareFamily: string;            // Fare family name
  requiresBundling: boolean;     // Whether bundling is required
  compatibleFareIds: string[];   // Compatible fare IDs for round-trip
  specialFareWarning?: string;   // Warning message for special fares
  isSpecialReturn: boolean;      // Whether this is a special return fare
}

// Filter Models
export interface FlightFilterState {
  stops: StopFilter[];
  refundable: RefundableFilter;
  departureTime: TimeRangeFilter[];
  arrivalTime: TimeRangeFilter[];
  airlines: AirlineFilter[];
  priceRange: PriceRangeFilter;
  connectionAirports: ConnectionAirportFilter[];
  expandedSections: {            // Track which filter sections are expanded
    [key: string]: boolean;
  };
}

export interface StopFilter {
  value: number;                 // Number of stops (0, 1, 2+)
  text: string;                  // Display text (e.g., "Non-stop")
  isSelected: boolean;           // Whether this filter is active
}

export interface RefundableFilter {
  isSelected: boolean;
  text: string;                  // Display text (e.g., "Refundable only")
}

export interface TimeRangeFilter {
  min: number;                   // Min time in minutes from midnight (0-1440)
  max: number;                   // Max time in minutes from midnight (0-1440)
  isSelected: boolean;           // Whether this filter is active
  icon: string;                  // Icon name/path
  text: string;                  // Display text (e.g., "Morning")
}

export interface AirlineFilter {
  code: string;                  // Airline code (e.g., "6E")
  name: string;                  // Airline name (e.g., "IndiGo")
  isSelected: boolean;           // Whether this filter is active
  logo?: string;                 // Optional logo URL
}

export interface PriceRangeFilter {
  min: number;                   // Minimum price in dataset
  max: number;                   // Maximum price in dataset
  currentValue: number;          // Current selected value (for single slider)
  currentMin?: number;           // Current min value (for dual slider)
  currentMax?: number;           // Current max value (for dual slider)
}

export interface ConnectionAirportFilter {
  code: string;                  // Airport code (e.g., "DEL")
  name: string;                  // Airport name (e.g., "Delhi")
  isSelected: boolean;           // Whether this filter is active
}

// Sort options
export type SortOption =
  | 'price_low_high'
  | 'price_high_low'
  | 'departure_early_late'
  | 'departure_late_early'
  | 'arrival_early_late'
  | 'arrival_late_early'
  | 'duration_short_long'
  | 'duration_long_short';

// Pagination model
export interface PaginationState {
  currentPage: number;
  itemsPerPage: number;
  totalItems: number;
  totalPages: number;
}

// Performance tracking
export interface PerformanceMetrics {
  searchStartTime: number;
  searchEndTime: number;
  totalResponseTime: number;
  cacheHitRate: number;
  apiCallCount: number;
  dataProcessingTime: number;
}

// Component prop interfaces
export interface FlightListProps {
  flights: FlightListItem[];
  isLoading: boolean;
  error?: string;
  onFlightSelect: (flight: FlightListItem) => void;
  onFilterChange: (filters: FlightFilterState) => void;
  onSortChange: (sortOption: SortOption) => void;
  onPageChange: (page: number) => void;
  filters: FlightFilterState;
  sortOption: SortOption;
  pagination: PaginationState;
  performanceMetrics?: PerformanceMetrics;
}

// Search state management
export interface FlightSearchState {
  outboundFlights: FlightListItem[];
  returnFlights: FlightListItem[];
  multiCityFlights: FlightListItem[][];
  isLoading: boolean;
  error?: string;
  filters: FlightFilterState;
  sortOption: SortOption;
  pagination: PaginationState;
  performanceMetrics: PerformanceMetrics;
}

// Default filter state
export const DEFAULT_FILTER_STATE: FlightFilterState = {
  stops: [
    { value: 0, text: 'Non-stop', isSelected: false },
    { value: 1, text: '1 Stop', isSelected: false },
    { value: 2, text: '2+ Stops', isSelected: false }
  ],
  refundable: {
    isSelected: false,
    text: 'Refundable only'
  },
  departureTime: [
    { min: 0, max: 360, isSelected: false, icon: 'moon', text: 'Early Morning (00:00 - 06:00)' },
    { min: 360, max: 720, isSelected: false, icon: 'sun', text: 'Morning (06:00 - 12:00)' },
    { min: 720, max: 1080, isSelected: false, icon: 'cloud-sun', text: 'Afternoon (12:00 - 18:00)' },
    { min: 1080, max: 1440, isSelected: false, icon: 'star', text: 'Evening (18:00 - 24:00)' }
  ],
  arrivalTime: [
    { min: 0, max: 360, isSelected: false, icon: 'moon', text: 'Early Morning (00:00 - 06:00)' },
    { min: 360, max: 720, isSelected: false, icon: 'sun', text: 'Morning (06:00 - 12:00)' },
    { min: 720, max: 1080, isSelected: false, icon: 'cloud-sun', text: 'Afternoon (12:00 - 18:00)' },
    { min: 1080, max: 1440, isSelected: false, icon: 'star', text: 'Evening (18:00 - 24:00)' }
  ],
  airlines: [],
  priceRange: {
    min: 0,
    max: 100000,
    currentValue: 100000
  },
  connectionAirports: [],
  expandedSections: {
    stops: true,
    refundable: true,
    departureTime: false,
    arrivalTime: false,
    airlines: true,
    priceRange: true,
    connectionAirports: false
  }
};

// Default pagination state
export const DEFAULT_PAGINATION_STATE: PaginationState = {
  currentPage: 1,
  itemsPerPage: 20,
  totalItems: 0,
  totalPages: 0
};

// Default performance metrics
export const DEFAULT_PERFORMANCE_METRICS: PerformanceMetrics = {
  searchStartTime: 0,
  searchEndTime: 0,
  totalResponseTime: 0,
  cacheHitRate: 0,
  apiCallCount: 0,
  dataProcessingTime: 0
};
