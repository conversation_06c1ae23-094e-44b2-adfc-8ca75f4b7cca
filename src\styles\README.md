# Centralized Color System Documentation

This document describes the centralized color system implemented in the application using SCSS variables and custom utility classes.

## Overview

All color values are now centralized in `_variables.scss` and accessed through custom utility classes defined in `_color-utilities.scss`. This system eliminates hardcoded colors and provides a consistent, maintainable design system.

## File Structure

```
src/styles/
├── _variables.scss          # All color variables
├── _color-utilities.scss    # Custom utility classes
└── README.md               # This documentation
```

## Color Variables

### Primary Colors (Main theme colors)
- `$primary_color`: #3b82f6 (Blue-500) - Main brand color
- `$primary_color_light`: #60a5fa (Blue-400)
- `$primary_color_lighter`: #93c5fd (Blue-300)
- `$primary_color_lightest`: #dbeafe (Blue-100)
- `$primary_color_dark`: #2563eb (Blue-600)
- `$primary_color_darker`: #1d4ed8 (Blue-700)
- `$primary_color_darkest`: #1e40af (Blue-800)

### Secondary Colors (Supporting brand colors)
- `$secondary_color`: #10b981 (Emerald-500) - Success/positive actions
- `$secondary_color_light`: #34d399 (Emerald-400)
- `$secondary_color_lighter`: #6ee7b7 (Emerald-300)
- `$secondary_color_lightest`: #d1fae5 (Emerald-100)
- `$secondary_color_dark`: #059669 (Emerald-600)
- `$secondary_color_darker`: #047857 (Emerald-700)
- `$secondary_color_darkest`: #065f46 (Emerald-800)

### Accent Colors (Highlighting and special elements)
- `$accent_color`: #f59e0b (Amber-500) - Warning/attention
- `$accent_color_light`: #fbbf24 (Amber-400)
- `$accent_color_lighter`: #fcd34d (Amber-300)
- `$accent_color_lightest`: #fef3c7 (Amber-100)
- `$accent_color_dark`: #d97706 (Amber-600)
- `$accent_color_darker`: #b45309 (Amber-700)
- `$accent_color_darkest`: #92400e (Amber-800)

### Neutral Colors (Grays for text, backgrounds, borders)
- `$neutral_50` to `$neutral_900`: Complete gray scale

### Semantic Colors (Status and feedback)
- `$success_color`, `$success_color_light`, `$success_color_dark`
- `$warning_color`, `$warning_color_light`, `$warning_color_dark`
- `$error_color`, `$error_color_light`, `$error_color_dark`
- `$info_color`, `$info_color_light`, `$info_color_dark`

## Custom Utility Classes

### Background Colors
- `.primary-background` - Main primary background
- `.primary-background-light` - Lighter primary background
- `.primary-background-lighter` - Even lighter primary background
- `.primary-background-lightest` - Lightest primary background
- `.secondary-background` - Secondary background
- `.neutral-background-50` to `.neutral-background-900` - Neutral backgrounds
- `.background-primary` - White background
- `.background-secondary` - Light gray background
- `.background-tertiary` - Medium gray background

### Text Colors
- `.primary-text` - Primary brand text color
- `.secondary-text` - Secondary text color
- `.text-primary` - Main text color (dark)
- `.text-secondary` - Secondary text color (medium)
- `.text-tertiary` - Tertiary text color (light)
- `.text-disabled` - Disabled text color
- `.text-inverse` - White text for dark backgrounds

### Border Colors
- `.primary-border` - Primary brand border
- `.border-primary` - Standard border color
- `.border-secondary` - Secondary border color
- `.border-focus` - Focus state border

### Component-Specific Classes
- `.flight-card-background` - Flight card background
- `.flight-card-border` - Flight card border
- `.flight-card-hover-border` - Flight card hover border
- `.flight-selected-background` - Selected flight background
- `.flight-selected-border` - Selected flight border
- `.calendar-selected-background` - Calendar selected date background
- `.calendar-range-background` - Calendar date range background
- `.header-background` - Header background
- `.footer-background` - Footer background

### Hover States
- `.hover-primary-background:hover` - Primary background on hover
- `.hover-neutral-background-50:hover` - Light gray background on hover
- `.hover-primary-text:hover` - Primary text color on hover

### Gradients
- `.gradient-primary` - Primary gradient
- `.gradient-secondary` - Secondary gradient
- `.gradient-corporate-rate` - Corporate rate badge gradient

## Usage Guidelines

### 1. Use Custom Classes for Colors
```html
<!-- ✅ Good: Use custom color utility classes -->
<div class="primary-background text-inverse">Content</div>
<button class="primary-text hover-primary-background">Button</button>

<!-- ❌ Bad: Don't use hardcoded Tailwind color classes -->
<div class="bg-blue-500 text-white">Content</div>
<button class="text-blue-600 hover:bg-blue-500">Button</button>
```

### 2. Combine with Tailwind for Non-Color Properties
```html
<!-- ✅ Good: Mix custom color classes with Tailwind utilities -->
<div class="primary-background text-inverse p-4 rounded-lg shadow-md">
  Content with custom colors and Tailwind spacing/layout
</div>
```

### 3. Component-Specific Classes
```html
<!-- ✅ Good: Use component-specific classes when available -->
<div class="flight-card-background flight-card-border rounded-xl">
  Flight card content
</div>
```

### 4. Semantic Color Usage
```html
<!-- ✅ Good: Use semantic colors for appropriate contexts -->
<div class="success-background success-text">Success message</div>
<div class="error-background error-text">Error message</div>
```

## Benefits

1. **Centralized Management**: All colors defined in one place
2. **Consistency**: Uniform color usage across the application
3. **Maintainability**: Easy to update colors globally
4. **Theme Support**: Foundation for implementing multiple themes
5. **No Hardcoded Colors**: Eliminates scattered color values
6. **Semantic Naming**: Clear, meaningful class names
7. **Tailwind Compatibility**: Works alongside Tailwind for non-color utilities

## Migration Notes

- All hardcoded color classes have been replaced with custom utility classes
- The system uses SCSS variables with `!important` declarations to ensure precedence
- Tailwind CSS continues to handle spacing, layout, typography, and other non-color utilities
- The old `index.css` and `App.css` files have been replaced with `index.scss` and `App.scss`

## Future Enhancements

- Add CSS custom properties for runtime theme switching
- Implement dark mode support
- Add more component-specific utility classes as needed
- Consider migrating from SCSS @import to @use for better performance
