import axios from 'axios';
import type { AxiosInstance, AxiosRequestConfig, AxiosResponse, AxiosError } from 'axios';

/**
 * API Error interface
 */
export interface ApiError {
  code: number;
  message: string;
  details?: any;
  timestamp: string;
}

/**
 * Token management interface
 */
export interface TokenManager {
  getToken(): string | null;
  setToken(token: string): void;
  clearToken(): void;
  isTokenValid(): boolean;
}

/**
 * Base API Configuration
 */
export interface BaseApiConfig {
  baseURL: string;
  timeout: number;
  retryAttempts: number;
  retryDelay: number;
}

/**
 * Default API Configuration
 */
const DEFAULT_CONFIG: BaseApiConfig = {
  baseURL: import.meta.env.VITE_API_BASE_URL || 'https://api.seateleven.com/apis',
  timeout: 30000,
  retryAttempts: 3,
  retryDelay: 1000,
};

/**
 * Token Manager Implementation
 */
class TokenManagerImpl implements TokenManager {
  private static instance: TokenManagerImpl;
  private accessToken: string | null = null;
  private readonly TOKEN_KEY = 'dyAccessToken';
  private readonly USER_KEY = 'dyUser';

  private constructor() {
    this.initializeToken();
  }

  public static getInstance(): TokenManagerImpl {
    if (!TokenManagerImpl.instance) {
      TokenManagerImpl.instance = new TokenManagerImpl();
    }
    return TokenManagerImpl.instance;
  }

  private initializeToken(): void {
    if (typeof window !== 'undefined') {
      this.accessToken = localStorage.getItem(this.TOKEN_KEY);
    }
  }

  public getToken(): string | null {
    if (!this.accessToken && typeof window !== 'undefined') {
      this.accessToken = localStorage.getItem(this.TOKEN_KEY);
    }
    return this.accessToken;
  }

  public setToken(token: string): void {
    this.accessToken = token;
    if (typeof window !== 'undefined') {
      localStorage.setItem(this.TOKEN_KEY, token);
    }
  }

  public clearToken(): void {
    this.accessToken = null;
    if (typeof window !== 'undefined') {
      localStorage.removeItem(this.TOKEN_KEY);
      localStorage.removeItem(this.USER_KEY);
    }
  }

  public isTokenValid(): boolean {
    const token = this.getToken();
    if (!token) return false;
    
    try {
      const payload = JSON.parse(atob(token.split('.')[1]));
      const currentTime = Date.now() / 1000;
      return payload.exp > currentTime;
    } catch {
      return false;
    }
  }
}

/**
 * Base API Service Class
 */
export class BaseApiService {
  protected axiosInstance: AxiosInstance;
  protected tokenManager: TokenManager;
  protected config: BaseApiConfig;

  constructor(config: Partial<BaseApiConfig> = {}) {
    this.config = { ...DEFAULT_CONFIG, ...config };
    this.tokenManager = TokenManagerImpl.getInstance();
    this.axiosInstance = this.createAxiosInstance();
    this.setupInterceptors();
  }

  /**
   * Create axios instance with base configuration
   */
  private createAxiosInstance(): AxiosInstance {
    return axios.create({
      baseURL: this.config.baseURL,
      timeout: this.config.timeout,
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
      },
    });
  }

  /**
   * Setup request and response interceptors
   */
  private setupInterceptors(): void {
    this.axiosInstance.interceptors.request.use(
      (config) => this.handleRequest(config),
      (error) => Promise.reject(error)
    );

    this.axiosInstance.interceptors.response.use(
      (response) => this.handleResponse(response),
      (error) => this.handleResponseError(error)
    );
  }

  /**
   * Handle outgoing requests
   */
  private handleRequest(config: AxiosRequestConfig): AxiosRequestConfig {
    config.metadata = { startTime: Date.now() };

    if (this.shouldAddAuthToken(config.url || '')) {
      const token = this.tokenManager.getToken();
      if (token) {
        config.headers = {
          ...config.headers,
          Authorization: `Bearer ${token}`,
        };
      }
    }

    return config;
  }

  /**
   * Handle successful responses
   */
  private handleResponse(response: AxiosResponse): AxiosResponse {
    const responseTime = Date.now() - (response.config.metadata?.startTime || 0);
    
    if (response.data && typeof response.data === 'object') {
      response.data.meta = {
        ...response.data.meta,
        timestamp: new Date().toISOString(),
        responseTime,
        cacheHit: response.headers['x-cache-hit'] === 'true',
      };
    }

    return response;
  }

  /**
   * Handle response errors with retry logic
   */
  private async handleResponseError(error: AxiosError): Promise<any> {
    const originalRequest = error.config as any;

    if (error.response?.status === 401) {
      this.tokenManager.clearToken();
      return Promise.reject(this.createApiError(error));
    }

    if (this.shouldRetry(error) && !originalRequest._retry) {
      originalRequest._retry = true;
      originalRequest._retryCount = (originalRequest._retryCount || 0) + 1;

      if (originalRequest._retryCount <= this.config.retryAttempts) {
        await this.delay(this.config.retryDelay * originalRequest._retryCount);
        return this.axiosInstance(originalRequest);
      }
    }

    return Promise.reject(this.createApiError(error));
  }

  /**
   * Determine if request should include auth token
   */
  private shouldAddAuthToken(url: string): boolean {
    const noAuthEndpoints = [
      'auth/register/', 'auth/login/', 'auth/otp_verify', 'auth/login',
      'search/', 'search_refresh/', 'search_list/', 'airports/', 'airports/search',
      'airports', 'details/', 'service_req/', 'rules/', 'setup/', 'pricing/',
      'pricing_list/', 'fetchservice/', 'seat/', 'fare-comparison/',
      'fare-calendar/', 'assets/',
    ];

    return !noAuthEndpoints.some(endpoint => url.includes(endpoint));
  }

  /**
   * Determine if request should be retried
   */
  private shouldRetry(error: AxiosError): boolean {
    if (!error.response) return true;
    const status = error.response.status;
    return status >= 500 || status === 408 || status === 429;
  }

  /**
   * Create standardized API error
   */
  private createApiError(error: AxiosError): ApiError {
    return {
      code: error.response?.status || 0,
      message: error.response?.data?.message || error.message || 'An unexpected error occurred',
      details: error.response?.data,
      timestamp: new Date().toISOString(),
    };
  }

  /**
   * Utility delay function
   */
  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * Get token manager instance
   */
  public getTokenManager(): TokenManager {
    return this.tokenManager;
  }

  /**
   * Get axios instance for direct use
   */
  public getAxiosInstance(): AxiosInstance {
    return this.axiosInstance;
  }
}

export const baseApiService = new BaseApiService();
