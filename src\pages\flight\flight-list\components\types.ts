// Flight List Page Types and Interfaces

export interface Flight {
  id: string;
  airline: string;
  alliance: string;
  corporateRate: boolean;
  departure: {
    city: string;
    time: string;
    terminal: string;
  };
  arrival: {
    city: string;
    time: string;
    terminal: string;
  };
  duration: string;
  stops: number;
  stopCity?: string;
  price: number;
  originalPrice: number;
  seatsAvailable: number;
  aircraft: string;
  amenities: string[];
  fareTypes: string[];
  carbonEmission: string;
  baggageAllowance: string;
}

export interface Airline {
  name: string;
  rating: number;
}

export interface TimeSlot {
  label: string;
  range: string;
  icon: string;
}

export interface Filters {
  directFlights: boolean;
  refundable: boolean;
  businessClass: boolean;
  corporateRates: boolean;
  departureTime: string;
  arrivalTime: string;
  selectedAirlines?: string[];
  priceRange: {
    min: number;
    max: number;
  };
}

export interface Passengers {
  adults: number;
  children: number;
  infants: number;
}

export interface SelectedDates {
  departure: string;
  return: string;
}

export interface ClassOption {
  id: string;
  label: string;
}

export interface TripType {
  id: string;
  label: string;
}

export interface Airport {
  code: string;
  city: string;
  name: string;
  country: string;
}

export interface Tab {
  id: string;
  label: string;
  icon: string;
}

export interface SortOption {
  value: SortBy;
  label: string;
  icon: string;
}

export type SortBy = 'recommended' | 'price' | 'duration' | 'departure';
export type DateSelectionState = 'departure' | 'return' | null;
export type CurrentView = 'search' | 'results';
export type TripTypeValue = 'oneWay' | 'roundTrip' | 'multiCity';

// Component Props Interfaces
export interface FlightCardProps {
  flight: Flight;
  onFlightSelect: (flight: Flight) => void;
  onShowDetails: (flight: Flight) => void;
  showPriceShimmer?: boolean; // Show shimmer for price when sh_price is false
  isSelected?: boolean; // Indicates if this flight is currently selected
  hideDetailsButton?: boolean; // Hide the details button from the card
}

export interface FlightFiltersProps {
  filters: Filters;
  onFiltersChange: (filters: Filters) => void;
  timeSlots: Record<string, TimeSlot>;
  airlines: Airline[];
  showAllAirlines: boolean;
  onToggleAirlines: () => void;
  isDragging: 'min' | 'max' | null;
  onDragStart: (handle: 'min' | 'max') => void;
}

export interface FlightSearchSummaryProps {
  fromAirport: string;
  toAirport: string;
  selectedDates: SelectedDates;
  passengers: Passengers;
  selectedClass: string;
  classOptions: ClassOption[];
  onModifySearch: () => void;
  formatDate: (dateString: string) => string;
  getTotalPassengers: () => number;
  // New props for inline modify search
  showInlineModifySearch?: boolean;
  onToggleInlineModifySearch?: () => void;
  currentSearchData?: any; // FlightSearchData type
  onSearchUpdate?: (searchData: any) => void;
}

export interface FlightSortBarProps {
  flightCount: number;
  sortBy: SortBy;
  onSortChange: (sortBy: SortBy) => void;
  onShowFilters: () => void;
  sortOptions: SortOption[];
}

export interface FlightDetailsModalProps {
  flight: Flight | null;
  isOpen: boolean;
  onClose: () => void;
  onSelectFlight: (flight: Flight) => void;
}

// Constants
export const TIME_SLOTS: Record<string, TimeSlot> = {
  earlyMorning: { label: 'Early Morning', range: '00:00 - 06:00', icon: 'sun' },
  morning: { label: 'Morning', range: '06:00 - 12:00', icon: 'cloud-sun' },
  afternoon: { label: 'Afternoon', range: '12:00 - 18:00', icon: 'sun' },
  night: { label: 'Night', range: '18:00 - 00:00', icon: 'moon' }
};

export const SORT_OPTIONS: SortOption[] = [
  { value: 'recommended', label: 'Recommended', icon: 'fa-star' },
  { value: 'price', label: 'Price', icon: 'fa-dollar-sign' },
  { value: 'duration', label: 'Duration', icon: 'fa-clock' },
  { value: 'departure', label: 'Departure', icon: 'fa-plane-departure' }
];

export const CLASS_OPTIONS: ClassOption[] = [
  { id: 'economy', label: 'Economy' },
  { id: 'premiumEconomy', label: 'Premium Economy' },
  { id: 'business', label: 'Business' },
  { id: 'first', label: 'First Class' }
];

export const TRIP_TYPES: TripType[] = [
  { id: 'roundTrip', label: 'Round Trip' },
  { id: 'oneWay', label: 'One Way' },
  { id: 'multiCity', label: 'Multi-City' }
];

export const DEFAULT_FILTERS: Filters = {
  directFlights: false,
  refundable: false,
  businessClass: false,
  corporateRates: true,
  departureTime: '',
  arrivalTime: '',
  selectedAirlines: [],
  priceRange: {
    min: 200,
    max: 1500
  }
};
