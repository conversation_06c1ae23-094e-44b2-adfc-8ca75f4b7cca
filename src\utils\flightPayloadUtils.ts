import type { ScheduleBody, FormSearch } from '../models/flight/flight-search.model';

/**
 * Format date as YYYY-MM-DD
 */
const formatDate = (date: Date, format: string): string => {
  if (format === 'yyyy-MM-dd') {
    return date.toISOString().split('T')[0];
  }
  return date.toISOString().split('T')[0];
};

/**
 * Create search payload with exact structure as specified
 * @param formValue - Form data containing search parameters
 * @param fareType - Fare type for the search
 * @returns ScheduleBody - Exact payload structure for API
 */
export const scheduleBodySet = (formValue: any, fareType: string): ScheduleBody => {
  const body: ScheduleBody = {
    SecType: formValue && formValue.SecType,
    FareType: fareType,
    ADT: formValue && formValue.travellers.adult,
    CHD: formValue && formValue.travellers.child,
    INF: formValue && formValue.travellers.infant,
    Cabin: formValue && formValue.cabin,
    Source: 'CF',
    Mode: 'AS',
    ClientID: '',
    IsMultipleCarrier: false,
    IsRefundable: false,
    preferedAirlines: null,
    TUI: '',
    YTH: 0,
    Trips: [],
    Parameters: {
      Airlines: '',
      GroupType: '',
      IsDirect: false,
      IsNearbyAirport: true,
      Refundable: '',
    },
  };

  // Add trip information
  if (formValue && formValue.trips && formValue.trips.length > 0) {
    formValue.trips.forEach((x: any, i: number) => {
      body.Trips.push({
        From: x.from.iata,
        FromArptName: x.from.airport,
        FromCity: x.from.city,
        OnwardDate: formatDate(x.depart, 'yyyy-MM-dd'),
        OrderId: i + 1,
        ReturnDate: x.return ? formatDate(x.return, 'yyyy-MM-dd') : null,
        To: x.to.iata,
        ToArptName: x.to.airport,
        ToCity: x.to.city,
        TUI: '',
      });
    });
  }

  return body;
};

/**
 * Create express search body for existing TUI
 * @param clientID - Client identifier
 * @param tui - Transaction unique identifier
 * @param source - Source identifier
 * @param mode - Mode identifier
 * @param fareType - Optional fare type
 * @returns ExpressSearchBody
 */
export const createExpressSearchBody = (
  clientID: string,
  tui: string | null,
  source: string = 'CF',
  mode: string = 'AS',
  fareType?: string
) => {
  const body: any = {
    ClientID: clientID,
    TUI: tui,
    Source: source,
    Mode: mode,
  };

  if (fareType) {
    body.FareType = fareType;
  }

  return body;
};

/**
 * Create get express search body for polling
 * @param clientID - Client identifier
 * @param tui - Transaction unique identifier
 * @returns GetExpressSearchBody
 */
export const createGetExpressSearchBody = (
  clientID: string,
  tui: string | null
) => {
  return {
    ClientID: clientID,
    TUI: tui,
  };
};
