# Flight Search Payload Implementation

This document describes the exact implementation of the flight search payload structure as specified in the requirements.

## 📋 Overview

The implementation provides the exact payload structure for flight search API calls with the following key components:

1. **scheduleBodySet Function** - Creates search payload with exact structure
2. **Interface Definitions** - Exact TypeScript interfaces as specified
3. **Form Data Structure** - Compatible form data interfaces
4. **Express Search Bodies** - For TUI-based searches

## 🔧 Core Implementation

### scheduleBodySet Function

Located in: `src/utils/flightPayloadUtils.ts`

```typescript
export const scheduleBodySet = (formValue: any, fareType: string): ScheduleBody => {
  const body: ScheduleBody = {
    SecType: formValue && formValue.SecType,
    FareType: fareType,
    ADT: formValue && formValue.travellers.adult,
    CHD: formValue && formValue.travellers.child,
    INF: formValue && formValue.travellers.infant,
    Cabin: formValue && formValue.cabin,
    Source: 'CF',
    Mode: 'AS',
    ClientID: '',
    IsMultipleCarrier: false,
    IsRefundable: false,
    preferedAirlines: null,
    TUI: '',
    YTH: 0,
    Trips: [],
    Parameters: {
      Airlines: '',
      GroupType: '',
      IsDirect: false,
      IsNearbyAirport: true,
      Refundable: '',
    },
  };

  // Add trip information
  if (formValue && formValue.trips && formValue.trips.length > 0) {
    formValue.trips.forEach((x: any, i: number) => {
      body.Trips.push({
        From: x.from.iata,
        FromArptName: x.from.airport,
        FromCity: x.from.city,
        OnwardDate: formatDate(x.depart, 'yyyy-MM-dd'),
        OrderId: i + 1,
        ReturnDate: x.return ? formatDate(x.return, 'yyyy-MM-dd') : null,
        To: x.to.iata,
        ToArptName: x.to.airport,
        ToCity: x.to.city,
        TUI: '',
      });
    });
  }

  return body;
};
```

## 📝 Interface Definitions

### ScheduleBody Interface

```typescript
export interface ScheduleBody {
  SecType: string
  FareType: string
  ADT: number
  CHD: number
  INF: number
  Cabin: string
  Source: string
  Mode: string
  ClientID: string
  IsMultipleCarrier: boolean
  IsRefundable: boolean
  preferedAirlines: string | null
  TUI: string
  YTH: number
  Trips: Trip[]
  Parameters: Parameters
  PaymentType?: string
}
```

### Trip Interface

```typescript
export interface Trip {
  From: string
  FromArptName?: string
  FromCity?: string
  OnwardDate: string | null
  OrderId?: number
  ReturnDate?: string | null
  To: string
  ToArptName?: string
  ToCity?: string
  TUI: string
}
```

### Parameters Interface

```typescript
export interface Parameters {
  Airlines: string
  GroupType: string
  Refundable: string
  IsDirect: boolean
  IsNearbyAirport: boolean
}
```

## 🔄 Express Search Bodies

### ExpressSearchBody Interface

```typescript
export interface ExpressSearchBody {
  ClientID: string
  TUI: string | null
  Source: string
  Mode: string
  FareType?: string
}
```

### GetExpressSearchBody Interface

```typescript
export interface GetExpressSearchBody {
  ClientID: string
  TUI: string | null
}
```

## 📊 Form Data Structure

### FormSearch Interface

```typescript
export interface FormSearch {
  FareType: string;
  travellers: Travellers;
  cabin: string;
  SecType: string;
  trips: TripsForm[];
}
```

### TripsForm Interface

```typescript
export interface TripsForm {
  from: FromToForm;
  to: FromToForm;
  depart: Date;
  return: Date | undefined;
}
```

### FromToForm Interface

```typescript
export interface FromToForm {
  city: string;
  airport: string;
  iata: string;
  country: string;
  airportOpen: boolean;
}
```

### Travellers Interface

```typescript
export interface Travellers {
  adult: number;
  child: number;
  infant: number;
}
```

## 🚀 Usage Examples

### One-Way Flight Search

```typescript
import { scheduleBodySet } from '../utils/flightPayloadUtils';

const formValue = {
  SecType: 'Flight',
  travellers: { adult: 2, child: 1, infant: 0 },
  cabin: 'E',
  trips: [{
    from: { city: 'New York', airport: 'JFK Airport', iata: 'JFK', country: 'US', airportOpen: false },
    to: { city: 'Los Angeles', airport: 'LAX Airport', iata: 'LAX', country: 'US', airportOpen: false },
    depart: new Date('2024-12-25'),
    return: undefined
  }]
};

const payload = scheduleBodySet(formValue, 'ON');
```

### Multi-City Flight Search

```typescript
const multiCityFormValue = {
  SecType: 'Flight',
  travellers: { adult: 1, child: 0, infant: 0 },
  cabin: 'B',
  trips: [
    {
      from: { city: 'New York', airport: 'JFK Airport', iata: 'JFK', country: 'US', airportOpen: false },
      to: { city: 'London', airport: 'Heathrow', iata: 'LHR', country: 'UK', airportOpen: false },
      depart: new Date('2024-12-15'),
      return: undefined
    },
    {
      from: { city: 'London', airport: 'Heathrow', iata: 'LHR', country: 'UK', airportOpen: false },
      to: { city: 'Paris', airport: 'CDG Airport', iata: 'CDG', country: 'FR', airportOpen: false },
      depart: new Date('2024-12-20'),
      return: undefined
    }
  ]
};

const multiCityPayload = scheduleBodySet(multiCityFormValue, 'MC');
```

## 🔗 Integration

The implementation is integrated into the unified flight search workflow:

1. **Form Data Conversion**: `convertToFormSearch()` converts FlightSearchData to FormSearch structure
2. **Payload Creation**: `createSearchBody()` uses `scheduleBodySet()` to create the exact payload
3. **API Compatibility**: Maintains compatibility with existing flight search API workflow

## 📁 File Locations

- **Interfaces**: `src/models/flight/flight-search.model.ts`
- **Payload Utils**: `src/utils/flightPayloadUtils.ts`
- **Integration**: `src/api/unified-flight-search.ts`
- **Examples**: `src/examples/flight-search-payload-examples.ts`

## ✅ Validation

The implementation ensures:

- ✅ Exact payload structure as specified
- ✅ Proper date formatting (yyyy-MM-dd)
- ✅ Correct trip ordering with OrderId
- ✅ Compatible with existing API workflow
- ✅ Type safety with TypeScript interfaces
- ✅ Backward compatibility with legacy interfaces
