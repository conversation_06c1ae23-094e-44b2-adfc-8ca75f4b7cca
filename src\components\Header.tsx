import React, { useEffect, useRef, useState } from "react";
import { Container } from "./layout";

const Header: React.FC = () => {
  const accountDropdownRef = useRef<HTMLDivElement>(null);
  const [showBalance, setShowBalance] = useState(false);
  const [showAccountDropdown, setShowAccountDropdown] = useState(false);


  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        accountDropdownRef.current &&
        !accountDropdownRef.current.contains(event.target as Node)
      ) {
        setShowAccountDropdown(false);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  return (
    <header className="fixed w-full background-primary shadow-sm z-50">
      <Container maxWidth="xl" padding="md">
        <div className="flex items-center justify-between h-14 sm:h-16">
          <div className="flex items-center">
            <i className="fas fa-globe primary-text text-3xl mr-2"></i>
            <span className="text-xl font-semibold text-primary">TravelPro</span>
          </div>
          <div className="flex items-center space-x-4">
            {/* Available Credit Display */}
            <div className="hidden md:flex items-center primary-background-lightest rounded-lg px-4 py-2">
              <span className="text-sm text-secondary mr-2">Available Credit:</span>
              <div className="flex items-center">
                {showBalance ? (
                  <span className="font-semibold text-primary">$25,000.00</span>
                ) : (
                  <span className="font-semibold text-primary">••••••••</span>
                )}
                <button
                  onClick={() => setShowBalance(!showBalance)}
                  className="!rounded-button ml-2 primary-text hover-primary-text-dark cursor-pointer whitespace-nowrap"
                >
                  <i className={`fas ${showBalance ? 'fa-eye-slash' : 'fa-eye'} text-sm`}></i>
                </button>
              </div>
            </div>
            {/* Account Dropdown */}
            <div className="relative" ref={accountDropdownRef}>
              <button
                className="!rounded-button flex items-center space-x-2 primary-background-lightest primary-text px-4 py-2 rounded-lg hover-primary-background-lighter transition-colors cursor-pointer whitespace-nowrap"
                onClick={() => setShowAccountDropdown(!showAccountDropdown)}
              >
                <i className="fas fa-user-circle text-lg"></i>
                <span className="font-medium hidden md:inline">Acme Corp</span>
                <i className={`fas fa-chevron-down text-xs transition-transform ${showAccountDropdown ? 'rotate-180' : ''}`}></i>
              </button>
              {showAccountDropdown && (
                <div className="absolute right-0 mt-2 w-72 background-primary rounded-xl shadow-xl py-4 z-50">
                  <div className="px-6 pb-4 border-b neutral-border-100">
                    <div className="flex items-center space-x-3">
                      <div className="w-12 h-12 primary-background-lighter rounded-full flex items-center justify-center">
                        <i className="fas fa-building primary-text text-lg"></i>
                      </div>
                      <div>
                        <h4 className="font-semibold text-primary">Acme Corporation</h4>
                        <p className="text-sm text-secondary">Enterprise Account</p>
                      </div>
                    </div>
                    {/* Mobile Only Credit Display */}
                    <div className="mt-4 primary-background-lightest rounded-lg p-3 md:hidden">
                      <div className="flex justify-between items-center">
                        <span className="text-sm text-secondary">Available Credit</span>
                        <div className="flex items-center">
                          {showBalance ? (
                            <span className="font-semibold text-primary">$25,000.00</span>
                          ) : (
                            <span className="font-semibold text-primary">••••••••</span>
                          )}
                          <button
                            onClick={(e) => {
                              e.stopPropagation();
                              setShowBalance(!showBalance);
                            }}
                            className="!rounded-button ml-2 primary-text hover-primary-text-dark cursor-pointer whitespace-nowrap"
                          >
                            <i className={`fas ${showBalance ? 'fa-eye-slash' : 'fa-eye'} text-sm`}></i>
                          </button>
                        </div>
                      </div>
                      <div className="flex justify-between items-center mt-2">
                        <span className="text-sm text-secondary">Monthly Limit</span>
                        <div className="flex items-center">
                          {showBalance ? (
                            <span className="font-semibold text-primary">$50,000.00</span>
                          ) : (
                            <span className="font-semibold text-primary">••••••••</span>
                          )}
                        </div>
                      </div>
                    </div>
                  </div>
                  <div className="px-2 py-2">
                    <a href="#" className="flex items-center space-x-3 px-4 py-2 neutral-text-700 hover-neutral-background-50 rounded-lg cursor-pointer">
                      <i className="fas fa-user-cog w-5 text-center text-tertiary"></i>
                      <span>Account Settings</span>
                    </a>
                    <a href="#" className="flex items-center space-x-3 px-4 py-2 neutral-text-700 hover-neutral-background-50 rounded-lg cursor-pointer">
                      <i className="fas fa-credit-card w-5 text-center text-tertiary"></i>
                      <span>Billing & Payments</span>
                    </a>
                    <a href="#" className="flex items-center space-x-3 px-4 py-2 neutral-text-700 hover-neutral-background-50 rounded-lg cursor-pointer">
                      <i className="fas fa-users w-5 text-center text-tertiary"></i>
                      <span>Team Management</span>
                    </a>
                    <a href="#" className="flex items-center space-x-3 px-4 py-2 neutral-text-700 hover-neutral-background-50 rounded-lg cursor-pointer">
                      <i className="fas fa-sign-out-alt w-5 text-center text-tertiary"></i>
                      <span>Sign Out</span>
                    </a>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      </Container>
    </header>
  );
};

export default Header;
