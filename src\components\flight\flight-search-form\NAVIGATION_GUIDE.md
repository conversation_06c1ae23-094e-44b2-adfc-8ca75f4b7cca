# Flight Search Navigation Implementation

This guide explains the complete navigation functionality implemented for the FlightSearchForm component.

## 🎯 **Navigation Flow**

```
FlightSearchForm → Validation → Save to LocalStorage → Navigate to Results Page
     ↓
[User fills form] → [Clicks Search] → [Form validates] → [Data saved] → [Navigate to /flight/results]
     ↓
FlightListPage → Receives search data → Displays results → Can navigate back to search
```

## 🚀 **Key Features**

### ✅ **Automatic Navigation**
- Form automatically navigates to `/flight/results` when valid search is submitted
- No manual navigation code needed in parent components
- Backward compatibility with optional `onSearch` callback

### ✅ **Data Persistence**
- Search data automatically saved to localStorage before navigation
- Search history tracking with timestamps
- Data passed via React Router state (not URL parameters)

### ✅ **Validation & Protection**
- Form validates all required fields before navigation
- Results page redirects to search if no data provided
- Prevents direct access to results without search data

### ✅ **Page Title Management**
- Automatic page title updates based on search data
- Format: "Flight Results: NYC ⇄ LON | Dec 15 - Dec 22"

## 📁 **Files Modified/Created**

### **Core Navigation Files**
1. **`FlightSearchForm.tsx`** - Added navigation logic
2. **`FlightListPage.tsx`** - Added data reception and validation
3. **`flightNavigationUtils.ts`** - Navigation utility functions

### **Context & Utils**
4. **`FlightSearchContext.tsx`** - Context with localStorage integration
5. **`airportUtils.ts`** - Airport data parsing utilities

## 🔧 **Implementation Details**

### **FlightSearchForm Changes**
```tsx
// Added navigation import
import { useNavigate } from 'react-router-dom';
import { navigateToResults } from '../../../utils/flightNavigationUtils';

// Updated handleSearch function
const handleSearch = () => {
  if (isSearchValid()) {
    saveSearch(); // Save to localStorage
    
    // Optional callback for backward compatibility
    if (onSearch) {
      onSearch(legacySearchData);
    }
    
    // Navigate to results with search data
    if (currentSearch) {
      navigateToResults(navigate, currentSearch, legacySearchData);
    }
  }
};
```

### **FlightListPage Changes**
```tsx
// Added navigation imports
import { useLocation, useNavigate } from 'react-router-dom';
import { isPageAccessValid, generatePageTitle } from '../../../utils/flightNavigationUtils';

// Extract search data from navigation state
const searchData = location.state?.searchData;

// Redirect if no search data
useEffect(() => {
  if (!isPageAccessValid(location)) {
    navigate('/flight/search', { replace: true });
  }
}, [location, navigate]);

// Set page title
useEffect(() => {
  if (searchData) {
    document.title = generatePageTitle(searchData);
  }
}, [searchData]);
```

## 🎯 **Usage Examples**

### **Basic Usage (Automatic Navigation)**
```tsx
import FlightSearchFormWithProvider from './components/flight/flight-search-form/FlightSearchFormWithProvider';

const SearchPage = () => {
  return (
    <div>
      <h1>Search Flights</h1>
      {/* Form handles navigation automatically */}
      <FlightSearchFormWithProvider />
    </div>
  );
};
```

### **With Optional Callback**
```tsx
import FlightSearchFormWithProvider from './components/flight/flight-search-form/FlightSearchFormWithProvider';

const SearchPage = () => {
  const handleSearch = (searchData) => {
    // Optional: Analytics, logging, etc.
    console.log('Search submitted:', searchData);
    // Navigation happens automatically after this
  };

  return (
    <FlightSearchFormWithProvider onSearch={handleSearch} />
  );
};
```

### **Receiving Data in Results Page**
```tsx
import { useLocation, useNavigate } from 'react-router-dom';

const FlightListPage = () => {
  const location = useLocation();
  const searchData = location.state?.searchData;
  
  // Use search data for API calls
  useEffect(() => {
    if (searchData) {
      fetchFlights({
        from: searchData.from.iata,
        to: searchData.to.iata,
        departure: searchData.depart,
        return: searchData.return,
        passengers: searchData.passengers,
        class: searchData.class,
        // ... other search parameters
      });
    }
  }, [searchData]);
  
  return (
    <div>
      {/* Flight results */}
    </div>
  );
};
```

## 🛠️ **Navigation Utilities**

### **Available Functions**
```tsx
// Navigate to results with search data
navigateToResults(navigate, searchData, legacyData);

// Navigate back to search with preserved data
navigateToSearch(navigate, searchData);

// Check if page access is valid
isPageAccessValid(location);

// Generate page title from search data
generatePageTitle(searchData);

// Format search summary for display
formatSearchSummary(searchData);

// Generate shareable URL (optional)
generateShareableUrl(searchData);
```

## 🔄 **Data Flow**

### **Search Submission Flow**
1. User fills form and clicks search
2. Form validates all required fields
3. If valid: Save to localStorage + Search history
4. Call optional onSearch callback (if provided)
5. Navigate to `/flight/results` with search data in state
6. Results page receives data and displays flights

### **Data Structure Passed**
```tsx
// Navigation state contains:
{
  searchData: FlightSearchData,      // New format with full types
  legacySearchData: LegacyFormat,    // Backward compatibility
  timestamp: string                  // When navigation occurred
}
```

## 🚨 **Important Notes**

### **Requirements**
- React Router must be set up in the application
- FlightSearchProvider must wrap the form component
- Results page must handle the navigation state

### **Route Configuration**
```tsx
// Required routes:
<Route path="/flight/search" element={<FlightSearchPage />} />
<Route path="/flight/results" element={<FlightListPage />} />
```

### **Error Handling**
- Invalid search data prevents navigation
- Results page redirects to search if no data
- Form shows validation errors for incomplete data

## 🎨 **User Experience**

### **Smooth Navigation**
- Instant navigation after successful validation
- No loading states needed for navigation
- Preserved search data across page transitions

### **Data Persistence**
- Search data saved to localStorage automatically
- Search history available for quick re-search
- Data survives browser refresh/close

### **Validation Feedback**
- Clear validation messages for incomplete forms
- Prevents navigation with invalid data
- Highlights missing required fields

## 🔍 **Testing the Implementation**

### **Test Cases**
1. **Valid Search**: Fill all fields → Click search → Should navigate to results
2. **Invalid Search**: Leave required fields empty → Should show validation
3. **Direct Results Access**: Navigate to `/flight/results` directly → Should redirect to search
4. **Data Persistence**: Submit search → Refresh results page → Should redirect (no data in state)
5. **Back Navigation**: From results → Back button → Should return to search with data

### **Validation Checklist**
- ✅ From airport selected
- ✅ To airport selected  
- ✅ Departure date selected
- ✅ Return date selected (for round trip)
- ✅ At least 1 adult passenger
- ✅ Different from/to airports

This implementation provides a complete, production-ready navigation system for flight search with proper data handling, validation, and user experience.
