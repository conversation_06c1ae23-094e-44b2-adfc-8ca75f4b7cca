
export interface FLightDetailBodyTrip {
    TUI: string
    Amount: number
    Index: string
    OrderID: number
    ChannelCode: string | null
}

// ==================== FLIGHT FARE RULE  ====================

export interface FlightFareRuleBody {
    ClientID: string
    Source: string
    Trips: FLightDetailBodyTrip[]
}


export interface FlightFareRuleResponse {
    TUI: string
    Code: string
    Msg: string[]
    CurrencyCode: string
    Trips: FlightFareRuleTrip[]
}

export interface FlightFareRuleTrip {
    Journey: FlightFareRuleJourney[]
}

export interface FlightFareRuleJourney {
    Provider: string
    Segments: FlightFareRuleSegment[]
}

export interface FlightFareRuleSegment {
    FUID: string
    VAC: string
    Rules: FlightFareRuleRule[]
}

export interface FlightFareRuleRule {
    OrginDestination: string
    FareRuleText: string | null
    Rule: FlightFareRuleRule2[]
}

export interface FlightFareRuleRule2 {
    Info: FlightFareRuleInfo[]
    Head: string
}

export interface FlightFareRuleInfo {
    AdultAmount: string
    ChildAmount: string
    InfantAmount: string
    YouthAmount: string | null
    Description: string
    CurrencyCode: string
}

// ==================== FLIGHT FARE RULE END ====================


// ==================== FLIGHT INFO  ====================

export interface FlightInfoBody {
    ClientID: string
    TripType: string
    Trips: FLightDetailBodyTrip[]
}

export interface FlightInfoResponse {
    TUI: string
    From: string
    To: string
    OnwardDate: string
    ReturnDate: string
    ADT: number
    CHD: number
    INF: number
    YTH: number
    NetAmount: number
    SSRAmount: number
    GrossAmount: number
    Trips: FlightInfoTrip[]
    GeneralKeys: string | null
    CeilingInfo: string | null
    Code: string
    Msg: string[]
}

export interface FlightInfoTrip {
    Journey: FlightInfoJourney[]
}


export interface FlightInfoJourney {
    Provider: string
    OrderID: number
    Stops: number
    Index: string
    SPFareNotice: string
    GrossFare: number
    NetFare: number
    Notices: FlightInfoNotice[] | null
    Segments: FlightInfoSegment[]
    FCType: string
}

export interface FlightInfoNotice {
    Notice: string
    Link: string
    NoticeType: string
}

export interface FlightInfoSegment {
    Flight: FlightInfoFlight
    Fares: FlightInfoFares
}

export interface FlightInfoFlight {
    FUID: number
    VAC: string
    MAC: string
    OAC: string
    FBC: string
    Airline: string
    FlightNo: string
    ArrivalTime: string
    DepartureTime: string
    FareClass: string
    ArrivalCode: string
    DepartureCode: string
    ArrivalTerminal: string
    DepartureTerminal: string
    ArrAirportName: string
    DepAirportName: string
    EquipmentType: string
    RBD: string
    Cabin: string
    Refundable: string
    Amenities: string | null
    Seats: number
    Hops: FlightInfoHops[] | null
    Duration: string
    AirCraft: string
}

export interface FlightInfoHops {
    ArrivalTime: string
    DepartureTime: string
    ArrivalCode: string
    ArrAirportName: string
    Duration: string
    ArrivalDuration: string
    DepartureDuration: string
}


export interface FlightInfoFares {
    PTCFare: FlightInfoPtcfare[]
    GrossFare: number
    NetFare: number
    TotalServiceTax: number
    TotalTransactionFee: number
    TotalBaseFare: number
    TotalTax: number
    TotalCommission: number
    TotalVATonServiceCharge: number
    TotalVATonTransactionFee: number
    TotalAgentMarkUp: number
    TotalAddonMarkup: number
    TotalAddonDiscount: number
    TotalAtoCharge: number
    TotalReissueCharge: number
    OldSSRAmount: number
}

export interface FlightInfoPtcfare {
    PTC: string
    Fare: number
    YQ: number
    PSF: number
    YR: number
    UD: number
    K3: number
    K7: number
    API: number
    RCF: number
    RCS: number
    PHF: number
    CUTE: number
    OTT?: string
    OT?: string
    Tax: number
    GrossFare: number
    NetFare: number
    ST: number
    TransactionFee: number
    VATonServiceCharge: number
    VATonTransactionFee: number
    AgentMarkUp: number
    AddonMarkup: number
    ATOAddonMarkup: number
    AddonDiscount: number
    Ammendment: number
    AtoCharge: number
    ReissueCharge: number
    OldSSRAmount: number
    CGST: number
    SGST: number
    IGST: number
    JN?: number
} 

// ==================== FLIGHT INFO END ====================


// ==================== FLIGHT SSR START ====================

export interface FlightSsrBody {
    ClientID: string
    PaidSSR: boolean
    Source: string
    Trips: FLightDetailBodyTrip[]
}

export interface FlightSsrResponse {
    TUI: string
    CurrencyCode: string
    PaidSSR: boolean
    Trips: FlightSsrTrip[]
    Code: string
    Msg: string[]
}

export interface FlightSsrTrip {
    From: string
    To: string
    Journey: FlightSsrJourney[]
}

export interface FlightSsrJourney {
    Provider: string
    MultiSSR: string
    ConversationID: string
    Segments: FlightSsrSegment[]
}

export interface FlightSsrSegment {
    FUID: string
    VAC: string
    Index: string | null
    SSR: FlightSsrSsr[]
}

export interface FlightSsrSsr {
    Code: string
    Description: string
    PieceDescription: string
    Charge: number
    VAT: number
    Type: string
    Category: string
    PTC: string
    ID: number
    IsFreeMeal: boolean
    MealImage: string
    SSRUrl: string | null
    AdditionalFields: string | null
}

// ==================== FLIGHT SSR END ====================


// ==================== CACHE METADATA INTERFACES ====================

export interface CacheMetadata {
  hit: boolean;
  source: 'memory' | 'api' | 'refresh';
  ttl: number;
  timestamp: number;
  responseTimeMs: number;
  dataFreshness: 'fresh' | 'stale' | 'expired';
}

export interface CacheConfig {
  ttl: number;
  maxAge: number;
  staleWhileRevalidate: boolean;
  forceRefresh: boolean;
}

export interface EnhancedFlightInfoResponse extends FlightInfoResponse {
  _cache: CacheMetadata;
}

export interface EnhancedFlightSsrResponse extends FlightSsrResponse {
  _cache: CacheMetadata;
}

export interface EnhancedFlightFareRuleResponse extends FlightFareRuleResponse {
  _cache: CacheMetadata;
}

export interface ComprehensiveFlightDetailsResponse {
  flightInfo: EnhancedFlightInfoResponse;
  ssrData: EnhancedFlightSsrResponse;
  fareRules: EnhancedFlightFareRuleResponse;
  _cache: {
    overall: CacheMetadata;
    breakdown: {
      flightInfo: CacheMetadata;
      ssrData: CacheMetadata;
      fareRules: CacheMetadata;
    };
    consolidationTimeMs: number;
  };
}

export interface CacheStats {
  totalEntries: number;
  freshEntries: number;
  staleEntries: number;
  expiredEntries: number;
  cacheHitRate: number;
  avgResponseTime: number;
  memoryUsage: number;
  performanceGrade: 'A' | 'B' | 'C' | 'D';
  recommendations: string[];
}

export interface LoadingStateWithCache {
  isLoading: boolean;
  error: string | null;
  loadingStage: 'initial' | 'fetching' | 'processing' | 'complete';
  retryCount: number;
  cacheStatus?: {
    hit: boolean;
    source: string;
    freshness: string;
  };
}

// ==================== TRIPJACK INTEGRATION INTERFACES ====================

export interface FlightDetailsEmitData {
  TripType: 'ON' | 'RT';
  Trips: Array<Array<{
    TUI: string;
    Amount: number;
    Index: string;
    OrderID: number;
    ChannelCode?: string | null;
  }>>;
}

export interface TripJackTripInfo {
  sI: Array<{
    id: string;
    fD: string;
    tD: string;
    at: string;
    dt: string;
    da: {
      code: string;
      name: string;
      cityCode: string;
      city: string;
      country: string;
      terminal: string;
    };
    aa: {
      code: string;
      name: string;
      cityCode: string;
      city: string;
      country: string;
      terminal: string;
    };
    stops: number;
    duration: number;
    iand: boolean;
    isRs: boolean;
    sN: number;
  }>;
  totalPriceList: Array<{
    id: string;
    fareIdentifier: string;
    msri: Array<string>;
    refundable: boolean;
    fd: {
      ADULT: {
        fC: {
          BF: number;
          TAF: number;
          NF: number;
        };
        afC: {
          TAF: {
            YQ: number;
            YR: number;
            K3: number;
            IN: number;
            WO: number;
            OT: number;
          };
        };
        sR: number;
        bI: {
          iB: string;
          cB: string;
        };
      };
      CHILD?: {
        fC: {
          BF: number;
          TAF: number;
          NF: number;
        };
        afC: {
          TAF: {
            YQ: number;
            YR: number;
            K3: number;
            IN: number;
            WO: number;
            OT: number;
          };
        };
        sR: number;
        bI: {
          iB: string;
          cB: string;
        };
      };
      INFANT?: {
        fC: {
          BF: number;
          TAF: number;
          NF: number;
        };
        afC: {
          TAF: {
            YQ: number;
            YR: number;
            K3: number;
            IN: number;
            WO: number;
            OT: number;
          };
        };
        sR: number;
        bI: {
          iB: string;
          cB: string;
        };
      };
    };
  }>;
}

export interface TripJackApiDataInfo {
  Trips: Array<{
    Journey: Array<{
      Provider: string;
      Segments: Array<{
        FUID: string;
        VAC: string;
        MAC: string;
        Airline: string;
        FlightNo: string;
        AirCraft: string;
        Cabin: string;
        DepartureTime: string;
        ArrivalTime: string;
        DepartureCode: string;
        ArrivalCode: string;
        DepAirportName: string;
        ArrAirportName: string;
        Duration: string;
        FareBasis: string;
        BaggageAllowance: string;
        SSRInfo?: Record<string, any>;
      }>;
    }>;
  }>;
  extra?: {
    tripjack_response?: {
      searchQuery?: {
        paxInfo?: {
          ADULT?: number;
          CHILD?: number;
          INFANT?: number;
        };
        cabinClass?: string;
      };
      tripInfos?: Array<TripJackTripInfo>;
      totalPriceList?: Array<{
        fareRuleInformation?: {
          tfr: Array<{
            ruleType: string;
            description: string;
            penalty?: string;
          }>;
        };
      }>;
    };
  };
  // Enhanced cache support
  _cache?: CacheMetadata;
  cache_hit?: boolean;
  CacheHit?: boolean;
}

export interface EnhancedTripJackApiDataInfo extends TripJackApiDataInfo {
  _cache: CacheMetadata;
  performanceMetrics?: {
    fetchTime: number;
    transformationTime: number;
    cacheEfficiency: number;
  };
}
  SSRServices?: {
    BaggageServices: Array<{
      Code: string;
      Description: string;
      Amount: string;
      Currency: string;
    }>;
    MealServices: Array<{
      Code: string;
      Description: string;
      Amount: string;
      Currency: string;
    }>;
    SeatServices: Array<{
      Code: string;
      Description: string;
      Amount: string;
      Currency: string;
    }>;
  };
  FareRules?: Array<{
    RuleType: string;
    Description: string;
    Penalty?: string;
  }>;
}

export interface TransformedLegacyFormat {
  flightSegments: Array<{
    airline: string;
    flightNumber: string;
    aircraft: string;
    cabin: string;
    departure: {
      time: string;
      code: string;
      name: string;
      terminal?: string;
    };
    arrival: {
      time: string;
      code: string;
      name: string;
      terminal?: string;
    };
    duration: string;
    fareBasis: string;
    baggageAllowance: string;
    // Enhanced fields
    segmentIndex?: number;
    estimatedDuration?: string;
    airlineLogoUrl?: string;
    route?: string;
    fuid?: string;
    vac?: string;
    mac?: string;
    stops?: number;
    refundable?: boolean;
  }>;
  fareBreakdown: {
    baseFare: number;
    taxes: number;
    totalAmount: number;
    currency: string;
    passengers: {
      ADULT: number;
      CHILD: number;
      INFANT: number;
    };
    // Enhanced fields
    perPassengerBreakdown?: {
      baseFarePerPerson: number;
      taxesPerPerson: number;
      totalPerPerson: number;
    };
    formattedAmounts?: {
      baseFare: string;
      taxes: string;
      totalAmount: string;
    };
  };
  baggageInfo: {
    cabin: string;
    checked: string;
    additionalServices?: Array<{
      code: string;
      description: string;
      amount: string;
      currency: string;
    }>;
    // Enhanced fields
    mealServices?: Array<{
      code: string;
      description: string;
      amount: string;
      currency: string;
    }>;
    seatServices?: Array<{
      code: string;
      description: string;
      amount: string;
      currency: string;
    }>;
    isValidFormat?: boolean;
  };
  fareRules: Array<{
    ruleType: string;
    description: string;
    penalty?: string;
  }>;
  // Enhanced metadata
  metadata?: {
    transformedAt: string;
    sourceDataValid: boolean;
    warnings: string[];
    segmentCount: number;
    totalPassengers: number;
  };
}

// ==================== TRIPJACK INTEGRATION INTERFACES END ====================