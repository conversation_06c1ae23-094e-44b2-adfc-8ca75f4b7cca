import React from 'react';

interface ClassOption {
  id: 'economy' | 'premiumEconomy' | 'business' | 'first';
  label: string;
}

interface ClassSelectorProps {
  selectedClass: 'economy' | 'premiumEconomy' | 'business' | 'first';
  onClassChange: (classId: 'economy' | 'premiumEconomy' | 'business' | 'first') => void;
  onApply: () => void;
}

const ClassSelector: React.FC<ClassSelectorProps> = ({
  selectedClass,
  onClassChange,
  onApply
}) => {
  const classOptions: ClassOption[] = [
    { id: 'economy', label: 'Economy' },
    { id: 'premiumEconomy', label: 'Premium Economy' },
    { id: 'business', label: 'Business' },
    { id: 'first', label: 'First' }
  ];

  return (
    <div className="bg-white rounded-xl shadow-xl border border-gray-200 p-4 min-w-64">
      <div className="mb-4">
        <h4 className="text-sm font-semibold text-gray-800 mb-3">Select Class</h4>
        <div className="space-y-2">
          {classOptions.map(cls => (
            <button
              key={cls.id}
              type="button"
              className={`w-full text-left px-3 py-2 rounded-lg border transition-colors ${
                selectedClass === cls.id
                  ? 'bg-blue-50 border-blue-200 text-blue-800'
                  : 'bg-white border-gray-200 text-gray-700 hover:bg-gray-50'
              }`}
              onClick={() => onClassChange(cls.id)}
            >
              {cls.label}
            </button>
          ))}
        </div>
      </div>

      <button
        type="button"
        className="w-full bg-blue-600 text-white py-2 px-4 rounded-lg hover:bg-blue-700 transition-colors text-sm font-medium"
        onClick={onApply}
      >
        Apply
      </button>
    </div>
  );
};

export default ClassSelector;
