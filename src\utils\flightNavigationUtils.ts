import type { FlightSearchData } from '../contexts/FlightSearchContext';

/**
 * Utility functions for flight search navigation and URL handling
 */

/**
 * Generate search parameters for URL (optional - for sharing/bookmarking)
 * @param searchData - Flight search data
 * @returns URLSearchParams object
 */
export const generateSearchParams = (searchData: FlightSearchData): URLSearchParams => {
  const params = new URLSearchParams();
  
  if (searchData.from.iata) params.set('from', searchData.from.iata);
  if (searchData.to.iata) params.set('to', searchData.to.iata);
  if (searchData.depart) params.set('depart', searchData.depart.toISOString().split('T')[0]);
  if (searchData.return) params.set('return', searchData.return.toISOString().split('T')[0]);
  if (searchData.tripType) params.set('tripType', searchData.tripType);
  if (searchData.passengers.adults) params.set('adults', searchData.passengers.adults.toString());
  if (searchData.passengers.children) params.set('children', searchData.passengers.children.toString());
  if (searchData.passengers.infants) params.set('infants', searchData.passengers.infants.toString());
  if (searchData.class) params.set('class', searchData.class);
  
  return params;
};

/**
 * Generate shareable URL for flight search
 * @param searchData - Flight search data
 * @param baseUrl - Base URL (optional, defaults to current origin)
 * @returns Complete shareable URL
 */
export const generateShareableUrl = (searchData: FlightSearchData, baseUrl?: string): string => {
  const base = baseUrl || window.location.origin;
  const params = generateSearchParams(searchData);
  return `${base}/flight/search?${params.toString()}`;
};

/**
 * Parse search parameters from URL
 * @param searchParams - URLSearchParams or search string
 * @returns Partial flight search data
 */
export const parseSearchParams = (searchParams: URLSearchParams | string): Partial<FlightSearchData> => {
  const params = typeof searchParams === 'string' 
    ? new URLSearchParams(searchParams) 
    : searchParams;
    
  const data: Partial<FlightSearchData> = {};
  
  // Parse basic parameters
  const fromIata = params.get('from');
  const toIata = params.get('to');
  const departDate = params.get('depart');
  const returnDate = params.get('return');
  const tripType = params.get('tripType') as 'roundTrip' | 'oneWay' | 'multiCity';
  const adults = params.get('adults');
  const children = params.get('children');
  const infants = params.get('infants');
  const flightClass = params.get('class') as 'economy' | 'premiumEconomy' | 'business' | 'first';
  
  // Build airport objects (minimal data from URL)
  if (fromIata) {
    data.from = {
      city: '', // Will need to be resolved from airport data
      airport: '',
      iata: fromIata,
      country: '',
      airportOpen: false
    };
  }
  
  if (toIata) {
    data.to = {
      city: '', // Will need to be resolved from airport data
      airport: '',
      iata: toIata,
      country: '',
      airportOpen: false
    };
  }
  
  // Parse dates
  if (departDate) {
    data.depart = new Date(departDate);
  }
  
  if (returnDate) {
    data.return = new Date(returnDate);
  }
  
  // Parse trip type
  if (tripType) {
    data.tripType = tripType;
  }
  
  // Parse passengers
  if (adults || children || infants) {
    data.passengers = {
      adults: adults ? parseInt(adults) : 1,
      children: children ? parseInt(children) : 0,
      infants: infants ? parseInt(infants) : 0
    };
  }
  
  // Parse class
  if (flightClass) {
    data.class = flightClass;
  }
  
  return data;
};

/**
 * Validate if search data is sufficient for navigation
 * @param searchData - Flight search data to validate
 * @returns boolean indicating if data is valid for navigation
 */
export const isValidForNavigation = (searchData: Partial<FlightSearchData>): boolean => {
  return !!(
    searchData.from?.iata &&
    searchData.to?.iata &&
    searchData.from.iata !== searchData.to.iata &&
    searchData.depart &&
    searchData.depart instanceof Date &&
    !isNaN(searchData.depart.getTime()) &&
    (searchData.tripType === 'oneWay' || 
     (searchData.return && searchData.return instanceof Date && !isNaN(searchData.return.getTime())))
  );
};

/**
 * Format search data for display in breadcrumbs or page titles
 * @param searchData - Flight search data
 * @returns Formatted string for display
 */
export const formatSearchSummary = (searchData: FlightSearchData): string => {
  const from = searchData.from.iata || searchData.from.city;
  const to = searchData.to.iata || searchData.to.city;
  const departDate = searchData.depart.toLocaleDateString();
  const returnDate = searchData.return?.toLocaleDateString();
  
  if (searchData.tripType === 'oneWay') {
    return `${from} → ${to} on ${departDate}`;
  } else {
    return `${from} ⇄ ${to} | ${departDate} - ${returnDate}`;
  }
};

/**
 * Generate page title for flight search results
 * @param searchData - Flight search data
 * @returns Page title string
 */
export const generatePageTitle = (searchData: FlightSearchData): string => {
  const summary = formatSearchSummary(searchData);
  return `Flight Results: ${summary}`;
};

/**
 * Navigation helper for programmatic navigation to flight results
 * @param navigate - React Router navigate function
 * @param searchData - Flight search data
 * @param legacyData - Optional legacy format data
 */
export const navigateToResults = (
  navigate: (to: string, options?: any) => void,
  searchData: FlightSearchData,
  legacyData?: any
) => {
  navigate('/flight/results', {
    state: {
      searchData,
      legacySearchData: legacyData,
      timestamp: new Date().toISOString()
    }
  });
};

/**
 * Navigation helper for going back to search with preserved data
 * @param navigate - React Router navigate function
 * @param searchData - Flight search data to preserve
 */
export const navigateToSearch = (
  navigate: (to: string, options?: any) => void,
  searchData?: FlightSearchData
) => {
  navigate('/flight/search', {
    state: searchData ? { preservedSearch: searchData } : undefined
  });
};

/**
 * Check if current page is accessible (has required search data)
 * @param location - React Router location object
 * @returns boolean indicating if page access is valid
 */
export const isPageAccessValid = (location: any): boolean => {
  const searchData = location.state?.searchData;
  const legacySearchData = location.state?.legacySearchData;
  
  return !!(searchData || legacySearchData);
};
