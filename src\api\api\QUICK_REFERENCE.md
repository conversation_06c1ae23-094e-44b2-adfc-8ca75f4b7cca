# Flight API Service - Quick Reference Guide

## 🚀 Import & Usage

```typescript
// Import specific functions (recommended)
import { searchAirport, callUnifiedSearch, getFlightInfo } from 'services/api/flight-api.service';

// Import all functions
import * as FlightApi from 'services/api/flight-api.service';

// Legacy compatibility
import FlightApiService from 'services/api';
```

## ⚡ Most Used Functions

### 1. **Airport Search**
```typescript
const airports = await searchAirport({ search_text: 'Delhi' });
```

### 2. **Flight Search** ⭐
```typescript
const results = await callUnifiedSearch({
  SecType: "DOM",
  FareType: "ON", // "ON"=One-way, "RT"=Round-trip
  ADT: 1, CHD: 0, INF: 0,
  Cabin: "E", // "E"=Economy, "B"=Business
  Source: "WEB", Mode: "LIVE", ClientID: "",
  IsMultipleCarrier: true, IsRefundable: false,
  preferedAirlines: null, TUI: "",
  Trips: [{ Origin: "DEL", Destination: "BOM", DepartureDate: "2024-12-25" }],
  Parameters: { Currency: "INR", Language: "en" }
});
```

### 3. **Flight Details**
```typescript
const details = await getFlightInfo({
  TUI: "search-tui",
  Index: "flight-index",
  ClientID: ""
});
```

### 4. **Create Booking**
```typescript
const booking = await bookNowApi({
  flight_booking: {
    TUI: "search-tui",
    Trips: [{ Journey: [{ Index: "flight-index" }] }]
  },
  passenger_details: [{ /* passenger info */ }]
});
```

## 📋 Function Categories

### 🏢 **Airport Operations**
| Function | Purpose | Auth Required |
|----------|---------|---------------|
| `searchAirport(body)` | Search airports by text | ❌ |
| `getAllAirports(limit?)` | Get all airports paginated | ❌ |

### ✈️ **Flight Search Operations**
| Function | Purpose | Auth Required |
|----------|---------|---------------|
| `callUnifiedSearch(body)` | Complete search workflow | ❌ |
| `callSeamlessRefreshSearch(body)` | Cache-optimized search | ❌ |
| `callPollingSearch(body, max?, interval?)` | Guaranteed results search | ❌ |
| `getExpressSearch(body)` | Direct search with TUI | ❌ |

### 📋 **Flight Details Operations**
| Function | Purpose | Auth Required |
|----------|---------|---------------|
| `getFlightInfo(body)` | Detailed flight info | ❌ |
| `getFlightSSR(body)` | Special service requests | ❌ |
| `getFlightFareRule(body)` | Fare rules & restrictions | ❌ |

### 🎫 **Booking Operations**
| Function | Purpose | Auth Required |
|----------|---------|---------------|
| `bookNowApi(body)` | Create new booking | ✅ |
| `getBookingList()` | Get user bookings | ✅ |
| `getBookingDetails(ref)` | Get booking details | ✅ |
| `getRetrieveBooking(body)` | Retrieve booking | ✅ |

### 💰 **Pricing Operations**
| Function | Purpose | Auth Required |
|----------|---------|---------------|
| `getTripValues()` | Smart pricing data | ❌ |
| `callSmartPricer(body)` | Pricing suggestions | ❌ |
| `callGetsPrice(body)` | Price breakdown | ❌ |

### 🔧 **Configuration Operations**
| Function | Purpose | Auth Required |
|----------|---------|---------------|
| `getWebSettings()` | App configuration | ❌ |

### 🎯 **Additional Services**
| Function | Purpose | Auth Required |
|----------|---------|---------------|
| `callTravelChecklist(body)` | Travel checklist | ❌ |
| `callFlightSSR(body)` | SSR services | ❌ |
| `callFlightSeat(body)` | Seat selection | ❌ |

### 📊 **Fare Comparison Operations**
| Function | Purpose | Auth Required |
|----------|---------|---------------|
| `getFareComparison(body)` | Compare fares | ❌ |
| `getFareCalendar(body)` | Fare calendar | ❌ |

## 🔄 Common Workflows

### **Basic Search Flow**
```typescript
// 1. Search airports
const airports = await searchAirport({ search_text: 'Delhi' });

// 2. Search flights
const results = await callUnifiedSearch({
  Trips: [{ Origin: "DEL", Destination: "BOM", DepartureDate: "2024-12-25" }],
  // ... other required fields
});

// 3. Get flight details (optional)
const details = await getFlightInfo({
  TUI: results.TUI,
  Index: results.Trips[0].Journey[0].Index
});
```

### **Complete Booking Flow**
```typescript
// 1. Search & select flight
const searchResults = await callUnifiedSearch(searchBody);
const selectedFlight = searchResults.Trips[0].Journey[0];

// 2. Get details & SSR (optional)
const flightInfo = await getFlightInfo({
  TUI: searchResults.TUI,
  Index: selectedFlight.Index
});

const ssrOptions = await getFlightSSR({
  TUI: searchResults.TUI,
  Index: selectedFlight.Index
});

// 3. Create booking
const booking = await bookNowApi({
  flight_booking: {
    TUI: searchResults.TUI,
    Trips: [{ Journey: [{ Index: selectedFlight.Index }] }]
  },
  passenger_details: [/* passenger data */]
});

// 4. Get booking confirmation
const bookingDetails = await getBookingDetails(booking.BookingReference);
```

## ⚠️ Error Handling

```typescript
try {
  const results = await callUnifiedSearch(searchBody);
  // Handle success
} catch (error) {
  // All functions throw clean errors
  console.error('API Error:', error.message);
  // Handle error appropriately
}
```

## 🔧 Technical Notes

### **Common Axios Instance**
- All functions use `baseApiService.getAxiosInstance()`
- Shared interceptors for auth, error handling, retries
- Automatic token management
- Response metadata injection

### **Function Structure**
- Individual callable functions (not class methods)
- Clean error propagation with `throw error`
- TypeScript support with proper interfaces
- No console logs in production code

### **Authentication**
- Automatic Bearer token injection for protected endpoints
- Token validation and refresh handling
- 401 error handling with token cleanup

### **Performance**
- Shared axios instance for connection pooling
- Request/response interceptors for optimization
- Retry logic for failed requests
- Cache detection and metadata

## 📝 Quick Tips

1. **Use `callUnifiedSearch()`** for most search operations
2. **Handle errors** with try-catch blocks
3. **Check authentication** for booking operations
4. **Use TypeScript interfaces** for better development experience
5. **Import only needed functions** for better tree-shaking
6. **All functions are async** - always use await
7. **Common axios instance** ensures consistent behavior

## 🔗 Related Files

- `flight-api.service.ts` - Main service file
- `base-api.service.ts` - Common axios instance & auth
- `auth-api.service.ts` - Authentication functions
- `index.ts` - Central exports & legacy compatibility
- `FLIGHT_API_DOCUMENTATION.md` - Detailed documentation
- `FLIGHT_API_FLOW.md` - Flow diagrams & architecture
