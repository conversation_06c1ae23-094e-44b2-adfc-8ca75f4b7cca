import React, { useState, useEffect } from 'react';

interface MultiCityListProps {
  apiData: any;
  apiMetadata: any;
  isLoading: boolean;
  onShowFlightDetails?: (flight: any) => void;
}

const MultiCityList: React.FC<MultiCityListProps> = ({
  apiData,
  apiMetadata,
  isLoading,
  onShowFlightDetails
}) => {
  // Process API data when it changes
  useEffect(() => {
    if (apiData) {
      console.log('🔄 MultiCityList: Processing API data:', apiData);
      // For now, just log the data - transformation will be added later
    }
  }, [apiData, apiMetadata]);

  // Show shimmer when loading
  if (isLoading) {
    return (
      <div className="flex gap-6">
        {/* Main Content - 70% width */}
        <div className="w-[70%] space-y-6">
          {/* Segment 1 Shimmer */}
          <div className="space-y-4">
            {/* Segment Header Shimmer */}
            <div className="bg-white rounded-lg border border-gray-200 p-4">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-4">
                  <div className="h-6 bg-gray-200 rounded w-32 animate-pulse"></div>
                  <div className="h-4 bg-gray-200 rounded w-24 animate-pulse"></div>
                </div>
                <div className="h-8 bg-gray-200 rounded w-20 animate-pulse"></div>
              </div>
            </div>

            {/* Filters and Flight List Side by Side */}
            <div className="flex gap-4">
              {/* Compact Filters Shimmer - Left side */}
              <div className="w-64 flex-shrink-0">
                <div className="bg-white rounded-lg border border-gray-200 p-4 space-y-4">
                  {/* Price Range */}
                  <div>
                    <div className="h-4 bg-gray-200 rounded w-20 mb-2 animate-pulse"></div>
                    <div className="h-8 bg-gray-200 rounded w-full animate-pulse"></div>
                  </div>

                  {/* Airlines */}
                  <div>
                    <div className="h-4 bg-gray-200 rounded w-16 mb-2 animate-pulse"></div>
                    <div className="space-y-2">
                      {[...Array(4)].map((_, index) => (
                        <div key={index} className="flex items-center space-x-2">
                          <div className="w-4 h-4 bg-gray-200 rounded animate-pulse"></div>
                          <div className="h-3 bg-gray-200 rounded w-24 animate-pulse"></div>
                        </div>
                      ))}
                    </div>
                  </div>

                  {/* Departure Times */}
                  <div>
                    <div className="h-4 bg-gray-200 rounded w-24 mb-2 animate-pulse"></div>
                    <div className="grid grid-cols-2 gap-2">
                      {[...Array(4)].map((_, index) => (
                        <div key={index} className="h-8 bg-gray-200 rounded animate-pulse"></div>
                      ))}
                    </div>
                  </div>

                  {/* Stops */}
                  <div>
                    <div className="h-4 bg-gray-200 rounded w-12 mb-2 animate-pulse"></div>
                    <div className="flex gap-2">
                      {[...Array(3)].map((_, index) => (
                        <div key={index} className="h-8 bg-gray-200 rounded w-16 animate-pulse"></div>
                      ))}
                    </div>
                  </div>
                </div>
              </div>

              {/* Flight List Shimmer - Right side */}
              <div className="flex-1 space-y-3">
                {[...Array(3)].map((_, index) => (
                  <div key={index} className="bg-white rounded-xl border border-gray-200 p-4">
                    <div className="flex items-center justify-between mb-3">
                      <div className="flex items-center flex-1">
                        <div className="w-6 h-6 bg-gray-200 rounded-full animate-pulse mr-3"></div>
                        <div className="space-y-1">
                          <div className="h-4 bg-gray-200 rounded w-32 animate-pulse"></div>
                          <div className="h-3 bg-gray-200 rounded w-24 animate-pulse"></div>
                        </div>
                      </div>
                      <div className="text-right space-y-1">
                        <div className="h-6 bg-gray-200 rounded w-20 animate-pulse"></div>
                      </div>
                    </div>
                    <div className="flex justify-between items-center">
                      <div className="flex space-x-8">
                        <div className="space-y-1">
                          <div className="h-5 bg-gray-200 rounded w-16 animate-pulse"></div>
                          <div className="h-3 bg-gray-200 rounded w-12 animate-pulse"></div>
                        </div>
                        <div className="space-y-1">
                          <div className="h-5 bg-gray-200 rounded w-16 animate-pulse"></div>
                          <div className="h-3 bg-gray-200 rounded w-12 animate-pulse"></div>
                        </div>
                      </div>
                      <div className="h-8 bg-gray-200 rounded w-24 animate-pulse"></div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>

          {/* Segment 2 Shimmer */}
          <div className="space-y-4">
            {/* Segment Header Shimmer */}
            <div className="bg-white rounded-lg border border-gray-200 p-4">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-4">
                  <div className="h-6 bg-gray-200 rounded w-32 animate-pulse"></div>
                  <div className="h-4 bg-gray-200 rounded w-24 animate-pulse"></div>
                </div>
                <div className="h-8 bg-gray-200 rounded w-20 animate-pulse"></div>
              </div>
            </div>

            {/* Filters and Flight List Side by Side */}
            <div className="flex gap-4">
              {/* Compact Filters Shimmer - Left side */}
              <div className="w-64 flex-shrink-0">
                <div className="bg-white rounded-lg border border-gray-200 p-4 space-y-4">
                  {/* Price Range */}
                  <div>
                    <div className="h-4 bg-gray-200 rounded w-20 mb-2 animate-pulse"></div>
                    <div className="h-8 bg-gray-200 rounded w-full animate-pulse"></div>
                  </div>

                  {/* Airlines */}
                  <div>
                    <div className="h-4 bg-gray-200 rounded w-16 mb-2 animate-pulse"></div>
                    <div className="space-y-2">
                      {[...Array(4)].map((_, index) => (
                        <div key={index} className="flex items-center space-x-2">
                          <div className="w-4 h-4 bg-gray-200 rounded animate-pulse"></div>
                          <div className="h-3 bg-gray-200 rounded w-24 animate-pulse"></div>
                        </div>
                      ))}
                    </div>
                  </div>

                  {/* Departure Times */}
                  <div>
                    <div className="h-4 bg-gray-200 rounded w-24 mb-2 animate-pulse"></div>
                    <div className="grid grid-cols-2 gap-2">
                      {[...Array(4)].map((_, index) => (
                        <div key={index} className="h-8 bg-gray-200 rounded animate-pulse"></div>
                      ))}
                    </div>
                  </div>

                  {/* Stops */}
                  <div>
                    <div className="h-4 bg-gray-200 rounded w-12 mb-2 animate-pulse"></div>
                    <div className="flex gap-2">
                      {[...Array(3)].map((_, index) => (
                        <div key={index} className="h-8 bg-gray-200 rounded w-16 animate-pulse"></div>
                      ))}
                    </div>
                  </div>
                </div>
              </div>

              {/* Flight List Shimmer - Right side */}
              <div className="flex-1 space-y-3">
                {[...Array(3)].map((_, index) => (
                  <div key={index} className="bg-white rounded-xl border border-gray-200 p-4">
                    <div className="flex items-center justify-between mb-3">
                      <div className="flex items-center flex-1">
                        <div className="w-6 h-6 bg-gray-200 rounded-full animate-pulse mr-3"></div>
                        <div className="space-y-1">
                          <div className="h-4 bg-gray-200 rounded w-32 animate-pulse"></div>
                          <div className="h-3 bg-gray-200 rounded w-24 animate-pulse"></div>
                        </div>
                      </div>
                      <div className="text-right space-y-1">
                        <div className="h-6 bg-gray-200 rounded w-20 animate-pulse"></div>
                      </div>
                    </div>
                    <div className="flex justify-between items-center">
                      <div className="flex space-x-8">
                        <div className="space-y-1">
                          <div className="h-5 bg-gray-200 rounded w-16 animate-pulse"></div>
                          <div className="h-3 bg-gray-200 rounded w-12 animate-pulse"></div>
                        </div>
                        <div className="space-y-1">
                          <div className="h-5 bg-gray-200 rounded w-16 animate-pulse"></div>
                          <div className="h-3 bg-gray-200 rounded w-12 animate-pulse"></div>
                        </div>
                      </div>
                      <div className="h-8 bg-gray-200 rounded w-24 animate-pulse"></div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>

        {/* Trip Summary Sidebar Shimmer - 30% width */}
        <div className="w-[30%] sticky top-24 self-start">
          <div className="bg-white rounded-lg border border-gray-200 p-6 space-y-4">
            <div className="h-6 bg-gray-200 rounded w-32 animate-pulse"></div>
            <div className="space-y-3">
              {[...Array(3)].map((_, index) => (
                <div key={index} className="space-y-2">
                  <div className="h-4 bg-gray-200 rounded w-24 animate-pulse"></div>
                  <div className="h-3 bg-gray-200 rounded w-full animate-pulse"></div>
                </div>
              ))}
            </div>
            <div className="pt-4 border-t border-gray-200">
              <div className="h-8 bg-gray-200 rounded w-full animate-pulse"></div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  const renderSegmentFlights = (flights: any[], segmentIndex: number) => {
    if (flights.length === 0) {
      return (
        <div className="text-center py-8">
          <div className="text-gray-400 mb-4">
            <i className="fas fa-plane text-2xl"></i>
          </div>
          <h3 className="text-lg font-medium text-gray-900 mb-2">No flights found for Segment {segmentIndex + 1}</h3>
          <p className="text-gray-500">Try adjusting your search criteria</p>
        </div>
      );
    }

    return (
      <div className="space-y-3">
        {flights.map((flight) => (
          <div
            key={flight.id}
            className="bg-white rounded-lg border border-gray-200 p-4 hover:shadow-md transition-shadow cursor-pointer"
            onClick={() => console.log('Selected flight:', flight)}
          >
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-4">
                {/* Airline */}
                <div className="flex items-center space-x-2">
                  <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                    <span className="text-xs font-medium text-blue-600">
                      {flight.airline.code}
                    </span>
                  </div>
                  <span className="text-sm font-medium text-gray-900">
                    {flight.airline.name}
                  </span>
                </div>

                {/* Flight Route */}
                <div className="flex items-center space-x-2">
                  <div className="text-center">
                    <div className="text-lg font-semibold text-gray-900">
                      {flight.departure.time}
                    </div>
                    <div className="text-xs text-gray-500">
                      {flight.departure.airport}
                    </div>
                  </div>
                  
                  <div className="flex items-center space-x-1 px-2">
                    <div className="w-2 h-2 bg-gray-300 rounded-full"></div>
                    <div className="w-8 h-px bg-gray-300"></div>
                    <div className="text-xs text-gray-500">{flight.duration}</div>
                    <div className="w-8 h-px bg-gray-300"></div>
                    <div className="w-2 h-2 bg-gray-300 rounded-full"></div>
                  </div>
                  
                  <div className="text-center">
                    <div className="text-lg font-semibold text-gray-900">
                      {flight.arrival.time}
                    </div>
                    <div className="text-xs text-gray-500">
                      {flight.arrival.airport}
                    </div>
                  </div>
                </div>

                {/* Stops */}
                <div className="text-center">
                  <div className="text-sm font-medium text-gray-900">
                    {flight.stops === 0 ? 'Non-stop' : `${flight.stops} stop${flight.stops > 1 ? 's' : ''}`}
                  </div>
                  {flight.connections.length > 0 && (
                    <div className="text-xs text-gray-500">
                      via {flight.connections[0].airportName}
                    </div>
                  )}
                </div>
              </div>

              {/* Price and Details */}
              <div className="text-right">
                <div className="text-xl font-bold text-blue-600">
                  {flight.price.currency} {flight.price.amount.toLocaleString()}
                </div>
                <div className="text-xs text-gray-500">
                  {flight.refundable ? 'Refundable' : 'Non-refundable'}
                </div>
                <div className="text-xs text-gray-500">
                  {flight.availability.seats} seats left
                </div>
              </div>
            </div>
          </div>
        ))}
      </div>
    );
  };

  return (
    <div className="space-y-8">
      <div className="bg-white rounded-lg border border-gray-200 p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Multi-City Flights</h3>
        <p className="text-gray-600">Multi-city flight processing will be implemented here.</p>

        {/* Show raw API data for debugging */}
        {apiData && (
          <div className="mt-4 p-3 bg-gray-50 rounded text-xs">
            <p><strong>API Data Available:</strong> {apiData.Trips?.length || 0} trips</p>
            <p><strong>Completed:</strong> {apiData.Completed ? 'Yes' : 'No'}</p>
          </div>
        )}
      </div>

      {/* Performance Metrics */}
      {apiMetadata && (
        <div className="mt-4 p-3 bg-gray-100 rounded-lg text-xs text-gray-600">
          <div>Response Time: {apiMetadata.responseTimeMS || 0}ms</div>
          <div>Cache Hit: {apiMetadata.cacheHit ? 'Yes' : 'No'}</div>
          <div>Data Source: {apiMetadata.dataSource || 'Unknown'}</div>
        </div>
      )}
    </div>
  );
};

export default MultiCityList;
