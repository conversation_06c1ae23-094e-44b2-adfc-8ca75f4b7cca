import React from 'react';

interface AdvancedSearchProps {
  showAdvancedSearch: boolean;
  onToggleAdvancedSearch: () => void;
  getAdvancedSearchCount: () => number;
}

const AdvancedSearch: React.FC<AdvancedSearchProps> = ({
  showAdvancedSearch,
  onToggleAdvancedSearch,
  getAdvancedSearchCount
}) => {
  return (
    <div className="w-full">
      {/* Advanced Search Toggle */}
      <div className="relative group">
        <label className="block text-sm font-medium text-gray-700 mb-2 flex items-center">
          <i className="fas fa-sliders-h text-blue-600 mr-2 text-xs"></i>
          Options
        </label>
        <button
          type="button"
          className={`!rounded-button w-full flex items-center justify-between pl-4 pr-4 py-3 border-2 rounded-xl text-sm transition-all cursor-pointer ${
            showAdvancedSearch
              ? 'border-blue-500 bg-blue-50 text-blue-700'
              : 'border-gray-200 hover:border-gray-300 text-gray-700'
          }`}
          onClick={onToggleAdvancedSearch}
        >
          <div className="flex items-center">
            <i className={`fas fa-cog mr-3 ${showAdvancedSearch ? 'text-blue-600' : 'text-gray-400'}`}></i>
            <div className="flex items-center">
              <span className="font-medium">Advanced Search</span>
              {getAdvancedSearchCount() > 0 && (
                <span className="ml-2 bg-blue-600 text-white text-xs font-semibold px-2 py-1 rounded-full min-w-[20px] text-center">
                  {getAdvancedSearchCount()}
                </span>
              )}
            </div>
          </div>
          <i className={`fas fa-chevron-down transition-transform ${showAdvancedSearch ? 'rotate-180 text-blue-600' : 'text-gray-400'}`}></i>
        </button>
      </div>
    </div>
  );
};

export default AdvancedSearch;
