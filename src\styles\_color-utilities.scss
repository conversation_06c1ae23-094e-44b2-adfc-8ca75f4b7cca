// ============================================================================
// Color Utility Classes - Custom classes using SCSS variables
// ============================================================================

@use 'variables' as *;

// Background Color Utilities
// --------------------------------------------------

// Primary backgrounds
.primary-background { background-color: $primary_color !important; }
.primary-background-light { background-color: $primary_color_light !important; }
.primary-background-lighter { background-color: $primary_color_lighter !important; }
.primary-background-lightest { background-color: $primary_color_lightest !important; }
.primary-background-dark { background-color: $primary_color_dark !important; }
.primary-background-darker { background-color: $primary_color_darker !important; }
.primary-background-darkest { background-color: $primary_color_darkest !important; }

// Secondary backgrounds
.secondary-background { background-color: $secondary_color !important; }
.secondary-background-light { background-color: $secondary_color_light !important; }
.secondary-background-lighter { background-color: $secondary_color_lighter !important; }
.secondary-background-lightest { background-color: $secondary_color_lightest !important; }
.secondary-background-dark { background-color: $secondary_color_dark !important; }
.secondary-background-darker { background-color: $secondary_color_darker !important; }
.secondary-background-darkest { background-color: $secondary_color_darkest !important; }

// Accent backgrounds
.accent-background { background-color: $accent_color !important; }
.accent-background-light { background-color: $accent_color_light !important; }
.accent-background-lighter { background-color: $accent_color_lighter !important; }
.accent-background-lightest { background-color: $accent_color_lightest !important; }
.accent-background-dark { background-color: $accent_color_dark !important; }
.accent-background-darker { background-color: $accent_color_darker !important; }
.accent-background-darkest { background-color: $accent_color_darkest !important; }

// Neutral backgrounds
.neutral-background-50 { background-color: $neutral_50 !important; }
.neutral-background-100 { background-color: $neutral_100 !important; }
.neutral-background-200 { background-color: $neutral_200 !important; }
.neutral-background-300 { background-color: $neutral_300 !important; }
.neutral-background-400 { background-color: $neutral_400 !important; }
.neutral-background-500 { background-color: $neutral_500 !important; }
.neutral-background-600 { background-color: $neutral_600 !important; }
.neutral-background-700 { background-color: $neutral_700 !important; }
.neutral-background-800 { background-color: $neutral_800 !important; }
.neutral-background-900 { background-color: $neutral_900 !important; }

// Semantic backgrounds
.success-background { background-color: $success_color !important; }
.success-background-light { background-color: $success_color_light !important; }
.warning-background { background-color: $warning_color !important; }
.warning-background-light { background-color: $warning_color_light !important; }
.error-background { background-color: $error_color !important; }
.error-background-light { background-color: $error_color_light !important; }
.info-background { background-color: $info_color !important; }
.info-background-light { background-color: $info_color_light !important; }

// Standard backgrounds
.background-primary { background-color: $background_primary !important; }
.background-secondary { background-color: $background_secondary !important; }
.background-tertiary { background-color: $background_tertiary !important; }

// Text Color Utilities
// --------------------------------------------------

// Primary text colors
.primary-text { color: $primary_color !important; }
.primary-text-light { color: $primary_color_light !important; }
.primary-text-lighter { color: $primary_color_lighter !important; }
.primary-text-dark { color: $primary_color_dark !important; }
.primary-text-darker { color: $primary_color_darker !important; }
.primary-text-darkest { color: $primary_color_darkest !important; }

// Secondary text colors
.secondary-text { color: $secondary_color !important; }
.secondary-text-light { color: $secondary_color_light !important; }
.secondary-text-dark { color: $secondary_color_dark !important; }

// Accent text colors
.accent-text { color: $accent_color !important; }
.accent-text-light { color: $accent_color_light !important; }
.accent-text-dark { color: $accent_color_dark !important; }

// Neutral text colors
.neutral-text-400 { color: $neutral_400 !important; }
.neutral-text-500 { color: $neutral_500 !important; }
.neutral-text-600 { color: $neutral_600 !important; }
.neutral-text-700 { color: $neutral_700 !important; }
.neutral-text-800 { color: $neutral_800 !important; }
.neutral-text-900 { color: $neutral_900 !important; }

// Semantic text colors
.success-text { color: $success_color !important; }
.success-text-dark { color: $success_color_dark !important; }
.warning-text { color: $warning_color !important; }
.warning-text-dark { color: $warning_color_dark !important; }
.error-text { color: $error_color !important; }
.error-text-dark { color: $error_color_dark !important; }
.info-text { color: $info_color !important; }
.info-text-dark { color: $info_color_dark !important; }

// Standard text colors
.text-primary { color: $text_primary !important; }
.text-secondary { color: $text_secondary !important; }
.text-tertiary { color: $text_tertiary !important; }
.text-disabled { color: $text_disabled !important; }
.text-inverse { color: $text_inverse !important; }

// Border Color Utilities
// --------------------------------------------------

// Primary borders
.primary-border { border-color: $primary_color !important; }
.primary-border-light { border-color: $primary_color_light !important; }
.primary-border-lighter { border-color: $primary_color_lighter !important; }
.primary-border-lightest { border-color: $primary_color_lightest !important; }

// Secondary borders
.secondary-border { border-color: $secondary_color !important; }
.secondary-border-light { border-color: $secondary_color_light !important; }

// Accent borders
.accent-border { border-color: $accent_color !important; }
.accent-border-light { border-color: $accent_color_light !important; }

// Neutral borders
.neutral-border-100 { border-color: $neutral_100 !important; }
.neutral-border-200 { border-color: $neutral_200 !important; }
.neutral-border-300 { border-color: $neutral_300 !important; }

// Semantic borders
.success-border { border-color: $success_color !important; }
.warning-border { border-color: $warning_color !important; }
.error-border { border-color: $error_color !important; }
.info-border { border-color: $info_color !important; }

// Standard borders
.border-primary { border-color: $border_primary !important; }
.border-secondary { border-color: $border_secondary !important; }
.border-focus { border-color: $border_focus !important; }

// Component-specific Utility Classes
// --------------------------------------------------

// Flight card utilities
.flight-card-background { background-color: $flight_card_background !important; }
.flight-card-border { border-color: $flight_card_border !important; }
.flight-card-hover-border { border-color: $flight_card_hover_border !important; }
.flight-selected-background { background-color: $flight_selected_background !important; }
.flight-selected-border { border-color: $flight_selected_border !important; }

// Calendar utilities
.calendar-selected-background { background-color: $calendar_selected_background !important; }
.calendar-selected-text { color: $calendar_selected_text !important; }
.calendar-range-background { background-color: $calendar_range_background !important; }
.calendar-range-text { color: $calendar_range_text !important; }
.calendar-hover-background { background-color: $calendar_hover_background !important; }
.calendar-disabled-text { color: $calendar_disabled_text !important; }
.calendar-return-background { background-color: $calendar_return_background !important; }
.calendar-return-text { color: $calendar_return_text !important; }
.calendar-modal-background { background-color: $calendar_modal_background !important; }
.calendar-modal-border { border-color: $calendar_modal_border !important; }
.calendar-header-background { background-color: $calendar_header_background !important; }
.calendar-weekend-background { background-color: $calendar_weekend_background !important; }
.calendar-today-border { border-color: $calendar_today_border !important; }
.calendar-price-text { color: $calendar_price_text !important; }
.calendar-day-height { height: $calendar_day_height !important; }

// Header and footer utilities
.header-background { background-color: $header_background !important; }
.footer-background { background-color: $footer_background !important; }

// Button utilities
.button-primary-background { background-color: $button_primary_background !important; }
.button-secondary-background { background-color: $button_secondary_background !important; }

// Hover state utilities
// --------------------------------------------------
.hover-primary-background:hover { background-color: $primary_color !important; }
.hover-primary-background-light:hover { background-color: $primary_color_light !important; }
.hover-primary-background-lighter:hover { background-color: $primary_color_lighter !important; }
.hover-neutral-background-50:hover { background-color: $neutral_50 !important; }
.hover-neutral-background-100:hover { background-color: $neutral_100 !important; }
.hover-neutral-background-200:hover { background-color: $neutral_200 !important; }

.hover-primary-text:hover { color: $primary_color !important; }
.hover-text-inverse:hover { color: $text_inverse !important; }

.hover-primary-border:hover { border-color: $primary_color !important; }
.hover-primary-border-lighter:hover { border-color: $primary_color_lighter !important; }

// Focus state utilities
// --------------------------------------------------
.focus-primary-border:focus { border-color: $primary_color !important; }
.focus-border-focus:focus { border-color: $border_focus !important; }

// Gradient utilities
// --------------------------------------------------
.gradient-primary { background: linear-gradient(135deg, $primary_color, $primary_color_dark) !important; }
.gradient-secondary { background: linear-gradient(135deg, $secondary_color, $secondary_color_dark) !important; }
.gradient-accent { background: linear-gradient(135deg, $accent_color, $accent_color_dark) !important; }
.gradient-corporate-rate { background: $corporate_rate_background !important; }
