import type { AirportApiResponse, AirportSearchApiResponse } from "../../models/flight/common-flight.model";
import axiosInstance from "../axiosINstance";

export const getAirportApi = async (searchTerm:string) : Promise<AirportSearchApiResponse> => {
    const params = new URLSearchParams({
      query: searchTerm || '',
      limit: '20' // Default limit
    });
    const api = await axiosInstance.get<AirportSearchApiResponse>(`airports/search?${params.toString()}`)
    return api.data
}   

export const getPopularAirportApi = async () : Promise<AirportApiResponse> => {
    const api = await axiosInstance.get<AirportApiResponse>(`airports/?limit=20`)
    return api.data
}   