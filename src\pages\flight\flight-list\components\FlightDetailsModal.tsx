import React, { useRef, useEffect } from 'react';
import type { FlightDetailsModalProps } from './types';

const FlightDetailsModal: React.FC<FlightDetailsModalProps> = ({
  flight,
  isOpen,
  onClose,
  onSelectFlight
}) => {
  const modalRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (modalRef.current && !modalRef.current.contains(event.target as Node)) {
        onClose();
      }
    };

    if (isOpen) {
      document.addEventListener('mousedown', handleClickOutside);
      document.body.style.overflow = 'hidden';
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
      document.body.style.overflow = 'unset';
    };
  }, [isOpen, onClose]);

  if (!isOpen || !flight) return null;

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center p-4">
      <div
        className="absolute inset-0 bg-black bg-opacity-50 backdrop-blur-sm"
        onClick={onClose}
        aria-hidden="true"
      ></div>
      <div
        ref={modalRef}
        className="relative flight-card-background w-full max-w-4xl max-h-[90vh] overflow-y-auto rounded-2xl shadow-2xl border flight-card-border"
        role="dialog"
        aria-modal="true"
        aria-labelledby="flight-details-title"
      >
        {/* Modal Header */}
        <div className="sticky top-0 flight-card-background px-6 py-4 border-b border-primary flex items-center justify-between">
          <div className="flex items-center">
            <i className="fas fa-plane text-2xl primary-text mr-4"></i>
            <div>
              <h3 id="flight-details-title" className="text-xl font-semibold text-primary">{flight.airline}</h3>
              <p className="text-sm text-secondary">{flight.alliance} • Flight {flight.id}</p>
            </div>
          </div>
          <button
            className="!rounded-button text-tertiary hover:text-secondary p-2 hover-neutral-background-50 rounded-lg transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-primary-color focus:ring-offset-1"
            onClick={onClose}
            aria-label="Close flight details"
          >
            <i className="fas fa-times text-xl"></i>
          </button>
        </div>

        {/* Modal Content */}
        <div className="p-6">
          {/* Flight Route */}
          <div className="mb-8">
            <h4 className="text-lg font-semibold text-primary mb-4">Flight Route</h4>
            <div className="flex items-start">
              <div className="flex-none w-px border-secondary mx-4 relative">
                <div className="absolute top-0 left-1/2 transform -translate-x-1/2 -translate-y-1/2">
                  <div className="w-4 h-4 rounded-full primary-background border-4 primary-background-lightest"></div>
                </div>
                <div className="h-20"></div>
                <div className="absolute bottom-0 left-1/2 transform -translate-x-1/2 translate-y-1/2">
                  <div className="w-4 h-4 rounded-full primary-background border-4 primary-background-lightest"></div>
                </div>
              </div>
              <div className="flex-1">
                <div className="mb-8">
                  <div className="text-xl font-semibold text-primary">{flight.departure.time}</div>
                  <div className="text-secondary">{flight.departure.city}</div>
                  <div className="text-sm text-tertiary">Terminal {flight.departure.terminal}</div>
                </div>
                <div>
                  <div className="text-xl font-semibold text-primary">{flight.arrival.time}</div>
                  <div className="text-secondary">{flight.arrival.city}</div>
                  <div className="text-sm text-tertiary">Terminal {flight.arrival.terminal}</div>
                </div>
              </div>
              <div className="flex-none text-right">
                <div className="text-sm text-tertiary">Duration</div>
                <div className="text-lg font-semibold text-primary">{flight.duration}</div>
              </div>
            </div>
          </div>

          {/* Aircraft Details */}
          <div className="mb-8">
            <h4 className="text-lg font-semibold text-gray-900 mb-4">Aircraft Details</h4>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="bg-gray-50 rounded-xl p-4">
                <div className="flex items-center mb-2">
                  <i className="fas fa-plane text-blue-600 mr-3"></i>
                  <span className="font-medium text-gray-900">Aircraft Type</span>
                </div>
                <p className="text-gray-600">{flight.aircraft}</p>
              </div>
              <div className="bg-gray-50 rounded-xl p-4">
                <div className="flex items-center mb-2">
                  <i className="fas fa-leaf text-green-500 mr-3"></i>
                  <span className="font-medium text-gray-900">Carbon Emission</span>
                </div>
                <p className="text-gray-600">{flight.carbonEmission}</p>
              </div>
            </div>
          </div>

          {/* Baggage and Amenities */}
          <div className="mb-8">
            <h4 className="text-lg font-semibold text-gray-900 mb-4">Baggage & Amenities</h4>
            <div className="space-y-4">
              <div className="flex items-start p-4 bg-gray-50 rounded-xl">
                <i className="fas fa-suitcase text-blue-600 mt-1 mr-4"></i>
                <div>
                  <h5 className="font-medium text-gray-900 mb-1">Baggage Allowance</h5>
                  <p className="text-gray-600">{flight.baggageAllowance}</p>
                </div>
              </div>
              <div className="flex items-start p-4 bg-gray-50 rounded-xl">
                <i className="fas fa-concierge-bell text-blue-600 mt-1 mr-4"></i>
                <div>
                  <h5 className="font-medium text-gray-900 mb-2">Onboard Amenities</h5>
                  <div className="grid grid-cols-2 gap-3">
                    {flight.amenities.map((amenity, index) => (
                      <div key={index} className="flex items-center">
                        <i className="fas fa-check text-green-500 mr-2"></i>
                        <span className="text-gray-600">{amenity}</span>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Fare Rules */}
          <div className="mb-8">
            <h4 className="text-lg font-semibold text-gray-900 mb-4">Fare Rules</h4>
            <div className="bg-blue-50 border border-blue-100 rounded-xl p-4">
              <div className="space-y-3">
                <div className="flex items-start">
                  <i className="fas fa-undo text-blue-600 mt-1 mr-3"></i>
                  <div>
                    <h5 className="font-medium text-gray-900">Cancellation</h5>
                    <p className="text-sm text-gray-600">Free cancellation up to 24 hours before departure</p>
                  </div>
                </div>
                <div className="flex items-start">
                  <i className="fas fa-exchange-alt text-blue-600 mt-1 mr-3"></i>
                  <div>
                    <h5 className="font-medium text-gray-900">Changes</h5>
                    <p className="text-sm text-gray-600">Flight changes allowed with minimal fees</p>
                  </div>
                </div>
                <div className="flex items-start">
                  <i className="fas fa-percent text-blue-600 mt-1 mr-3"></i>
                  <div>
                    <h5 className="font-medium text-gray-900">Corporate Benefits</h5>
                    <p className="text-sm text-gray-600">Special corporate rates and benefits applied</p>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Action Buttons */}
          <div className="flex justify-end space-x-4">
            <button
              className="!rounded-button bg-gray-100 text-gray-700 px-6 py-3 font-medium hover:bg-gray-200 transition-colors cursor-pointer whitespace-nowrap"
              onClick={onClose}
            >
              Close
            </button>
            <button 
              className="!rounded-button bg-blue-600 text-white px-8 py-3 font-medium hover:bg-blue-700 transition-colors cursor-pointer whitespace-nowrap"
              onClick={() => onSelectFlight(flight)}
            >
              Select Flight
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default FlightDetailsModal;
