/**
 * TripJack Response Adapter
 * Transforms TripJack API responses to standardized formats
 */

import type { TripJackApiDataInfo } from '../../models/flight/flight-details.model';

export class TripJackResponseAdapter {
  /**
   * Transform SSR (Special Service Requests) response from TripJack format
   */
  static transformSSRResponse(dataInfo: TripJackApiDataInfo): {
    BaggageServices: Array<{
      Code: string;
      Description: string;
      Amount: string;
      Currency: string;
    }>;
    MealServices: Array<{
      Code: string;
      Description: string;
      Amount: string;
      Currency: string;
    }>;
    SeatServices: Array<{
      Code: string;
      Description: string;
      Amount: string;
      Currency: string;
    }>;
  } {
    const defaultSSR = {
      BaggageServices: [],
      MealServices: [],
      SeatServices: []
    };

    if (!dataInfo.SSRServices) {
      return defaultSSR;
    }

    return {
      BaggageServices: dataInfo.SSRServices.BaggageServices || [],
      MealServices: dataInfo.SSRServices.MealServices || [],
      SeatServices: dataInfo.SSRServices.SeatServices || []
    };
  }

  /**
   * Transform fare rules response from TripJack format with enhanced handling
   */
  static transformFareRulesResponse(dataInfo: TripJackApiDataInfo): Array<{
    RuleType: string;
    Description: string;
    Penalty?: string;
  }> {
    const fareRules: Array<{
      RuleType: string;
      Description: string;
      Penalty?: string;
    }> = [];

    try {
      // Method 1: Extract from direct FareRules array
      if (dataInfo.FareRules && Array.isArray(dataInfo.FareRules)) {
        dataInfo.FareRules.forEach(rule => {
          if (rule && typeof rule === 'object') {
            fareRules.push({
              RuleType: rule.RuleType || 'general',
              Description: rule.Description || 'No description available',
              Penalty: rule.Penalty
            });
          }
        });
      }

      // Method 2: Extract from extra.tripjack_response.totalPriceList (array-based)
      if (dataInfo.extra?.tripjack_response?.totalPriceList && Array.isArray(dataInfo.extra.tripjack_response.totalPriceList)) {
        dataInfo.extra.tripjack_response.totalPriceList.forEach(priceItem => {
          if (priceItem?.fareRuleInformation?.tfr && Array.isArray(priceItem.fareRuleInformation.tfr)) {
            priceItem.fareRuleInformation.tfr.forEach(rule => {
              if (rule && typeof rule === 'object') {
                fareRules.push({
                  RuleType: rule.ruleType || 'general',
                  Description: rule.description || 'No description available',
                  Penalty: rule.penalty
                });
              }
            });
          }
        });
      }

      // Method 3: Extract from tripInfos fare rules (object-based)
      if (dataInfo.extra?.tripjack_response?.tripInfos && Array.isArray(dataInfo.extra.tripjack_response.tripInfos)) {
        dataInfo.extra.tripjack_response.tripInfos.forEach(tripInfo => {
          if (tripInfo?.totalPriceList && Array.isArray(tripInfo.totalPriceList)) {
            tripInfo.totalPriceList.forEach(priceInfo => {
              // Handle object-based fare rules
              if (priceInfo?.fareRuleInformation) {
                const fareRuleInfo = priceInfo.fareRuleInformation;

                // Handle different fare rule formats
                if (fareRuleInfo.tfr && Array.isArray(fareRuleInfo.tfr)) {
                  fareRuleInfo.tfr.forEach(rule => {
                    fareRules.push({
                      RuleType: rule.ruleType || 'general',
                      Description: rule.description || 'No description available',
                      Penalty: rule.penalty
                    });
                  });
                } else if (typeof fareRuleInfo === 'object') {
                  // Handle object-based fare rules
                  Object.keys(fareRuleInfo).forEach(key => {
                    const ruleData = fareRuleInfo[key];
                    if (ruleData && typeof ruleData === 'object') {
                      fareRules.push({
                        RuleType: key,
                        Description: ruleData.description || ruleData.text || 'No description available',
                        Penalty: ruleData.penalty || ruleData.charge
                      });
                    }
                  });
                }
              }
            });
          }
        });
      }

      // Method 4: Extract from segments-level fare rules
      if (dataInfo.Trips && Array.isArray(dataInfo.Trips)) {
        dataInfo.Trips.forEach(trip => {
          if (trip.Journey && Array.isArray(trip.Journey)) {
            trip.Journey.forEach(journey => {
              if (journey.Segments && Array.isArray(journey.Segments)) {
                journey.Segments.forEach(segment => {
                  if (segment.SSRInfo?.fareRules) {
                    const segmentRules = segment.SSRInfo.fareRules;
                    if (Array.isArray(segmentRules)) {
                      segmentRules.forEach(rule => {
                        fareRules.push({
                          RuleType: rule.type || 'segment',
                          Description: rule.description || 'Segment-specific rule',
                          Penalty: rule.penalty
                        });
                      });
                    }
                  }
                });
              }
            });
          }
        });
      }

      // Remove duplicates based on RuleType and Description
      const uniqueRules = fareRules.filter((rule, index, self) =>
        index === self.findIndex(r => r.RuleType === rule.RuleType && r.Description === rule.Description)
      );

      // Add fallback rules if no rules found
      if (uniqueRules.length === 0) {
        return this.getFallbackFareRules();
      }

      return uniqueRules;
    } catch (error) {
      console.warn('Error extracting fare rules from TripJack response:', error);
      return this.getFallbackFareRules();
    }
  }

  /**
   * Get fallback fare rules when extraction fails or no rules are found
   */
  private static getFallbackFareRules(): Array<{
    RuleType: string;
    Description: string;
    Penalty?: string;
  }> {
    return [
      {
        RuleType: 'cancellation',
        Description: 'Cancellation charges may apply as per airline policy. Please contact customer service for specific details.',
        Penalty: 'Varies by airline'
      },
      {
        RuleType: 'dateChange',
        Description: 'Date change may be allowed with applicable charges and fare difference.',
        Penalty: 'Charges apply'
      },
      {
        RuleType: 'refund',
        Description: 'Refund policy varies by fare type. Please check with the airline for specific conditions.',
        Penalty: 'Subject to airline policy'
      },
      {
        RuleType: 'noShow',
        Description: 'No-show charges may apply if you fail to board the flight without prior cancellation.',
        Penalty: 'Full ticket value may be forfeited'
      }
    ];
  }

  /**
   * Extract passenger information from TripJack response with improved error handling
   */
  static extractPassengerInfo(dataInfo: TripJackApiDataInfo): {
    ADULT: number;
    CHILD: number;
    INFANT: number;
  } {
    const defaultPaxInfo = { ADULT: 1, CHILD: 0, INFANT: 0 };

    try {
      // Try multiple sources for passenger information
      let paxInfo = null;

      // Primary source: searchQuery.paxInfo
      if (dataInfo.extra?.tripjack_response?.searchQuery?.paxInfo) {
        paxInfo = dataInfo.extra.tripjack_response.searchQuery.paxInfo;
      }
      // Secondary source: tripInfos passenger data
      else if (dataInfo.extra?.tripjack_response?.tripInfos?.[0]?.totalPriceList?.[0]?.fd) {
        const fareData = dataInfo.extra.tripjack_response.tripInfos[0].totalPriceList[0].fd;
        paxInfo = {
          ADULT: fareData.ADULT ? 1 : 0,
          CHILD: fareData.CHILD ? 1 : 0,
          INFANT: fareData.INFANT ? 1 : 0
        };
      }

      if (paxInfo) {
        // Ensure valid passenger counts (at least 1 adult)
        const adultCount = Math.max(paxInfo.ADULT || 0, 1);
        const childCount = Math.max(paxInfo.CHILD || 0, 0);
        const infantCount = Math.max(paxInfo.INFANT || 0, 0);

        return {
          ADULT: adultCount,
          CHILD: childCount,
          INFANT: infantCount
        };
      }
    } catch (error) {
      console.warn('Error extracting passenger info from TripJack response:', error);
    }

    return defaultPaxInfo;
  }

  /**
   * Extract cabin class from TripJack response
   */
  static extractCabinClass(dataInfo: TripJackApiDataInfo): string {
    return dataInfo.extra?.tripjack_response?.searchQuery?.cabinClass || 'Economy';
  }

  /**
   * Transform flight segments from TripJack format with enhanced field mapping
   */
  static transformFlightSegments(dataInfo: TripJackApiDataInfo): Array<{
    airline: string;
    flightNumber: string;
    aircraft: string;
    cabin: string;
    departure: {
      time: string;
      code: string;
      name: string;
      terminal?: string;
    };
    arrival: {
      time: string;
      code: string;
      name: string;
      terminal?: string;
    };
    duration: string;
    fareBasis: string;
    baggageAllowance: string;
    // Additional fields for FlightInfo component
    fuid?: string;
    vac?: string;
    mac?: string;
    stops?: number;
    refundable?: boolean;
  }> {
    const segments: Array<any> = [];

    try {
      if (dataInfo.Trips && Array.isArray(dataInfo.Trips)) {
        dataInfo.Trips.forEach((trip, tripIndex) => {
          if (trip.Journey && Array.isArray(trip.Journey)) {
            trip.Journey.forEach((journey, journeyIndex) => {
              if (journey.Segments && Array.isArray(journey.Segments)) {
                journey.Segments.forEach((segment, segmentIndex) => {
                  // Enhanced aircraft type mapping with proper capitalization
                  const aircraftType = this.normalizeAircraftType(segment.AirCraft || '');

                  // Enhanced airline name mapping
                  const airlineName = this.normalizeAirlineName(segment.Airline || '');

                  // Enhanced cabin class mapping
                  const cabinClass = this.normalizeCabinClass(segment.Cabin || 'Economy');

                  // Parse terminal information if available
                  const departureTerminal = this.extractTerminalInfo(segment.DepartureCode, segment.DepAirportName);
                  const arrivalTerminal = this.extractTerminalInfo(segment.ArrivalCode, segment.ArrAirportName);

                  segments.push({
                    airline: airlineName,
                    flightNumber: segment.FlightNo || '',
                    aircraft: aircraftType,
                    cabin: cabinClass,
                    departure: {
                      time: segment.DepartureTime || '',
                      code: segment.DepartureCode || '',
                      name: segment.DepAirportName || '',
                      terminal: departureTerminal
                    },
                    arrival: {
                      time: segment.ArrivalTime || '',
                      code: segment.ArrivalCode || '',
                      name: segment.ArrAirportName || '',
                      terminal: arrivalTerminal
                    },
                    duration: segment.Duration || '',
                    fareBasis: segment.FareBasis || '',
                    baggageAllowance: segment.BaggageAllowance || '',
                    // Additional fields for enhanced functionality
                    fuid: segment.FUID || '',
                    vac: segment.VAC || '',
                    mac: segment.MAC || '',
                    stops: 0, // Direct flight by default, can be enhanced
                    refundable: this.determineRefundability(segment, dataInfo)
                  });
                });
              }
            });
          }
        });
      }
    } catch (error) {
      console.warn('Error transforming flight segments:', error);
    }

    return segments;
  }

  /**
   * Normalize aircraft type with proper capitalization
   */
  private static normalizeAircraftType(aircraft: string): string {
    if (!aircraft) return 'Unknown';

    // Common aircraft type mappings
    const aircraftMappings: Record<string, string> = {
      'a320': 'Airbus A320',
      'a321': 'Airbus A321',
      'a330': 'Airbus A330',
      'a350': 'Airbus A350',
      'b737': 'Boeing 737',
      'b777': 'Boeing 777',
      'b787': 'Boeing 787',
      'atr72': 'ATR 72',
      'e190': 'Embraer E190'
    };

    const normalizedKey = aircraft.toLowerCase().replace(/[^a-z0-9]/g, '');
    return aircraftMappings[normalizedKey] || aircraft.toUpperCase();
  }

  /**
   * Normalize airline name
   */
  private static normalizeAirlineName(airline: string): string {
    if (!airline) return 'Unknown Airline';

    // Common airline name mappings
    const airlineMappings: Record<string, string> = {
      '6e': 'IndiGo',
      'ai': 'Air India',
      'sg': 'SpiceJet',
      'uk': 'Vistara',
      'g8': 'GoAir',
      'i5': 'AirAsia India'
    };

    const normalizedKey = airline.toLowerCase().replace(/[^a-z0-9]/g, '');
    return airlineMappings[normalizedKey] || airline;
  }

  /**
   * Normalize cabin class
   */
  private static normalizeCabinClass(cabin: string): string {
    if (!cabin) return 'Economy';

    const cabinMappings: Record<string, string> = {
      'y': 'Economy',
      'e': 'Economy',
      'economy': 'Economy',
      'c': 'Business',
      'business': 'Business',
      'f': 'First',
      'first': 'First',
      'w': 'Premium Economy',
      'premium': 'Premium Economy'
    };

    const normalizedKey = cabin.toLowerCase().replace(/[^a-z]/g, '');
    return cabinMappings[normalizedKey] || cabin;
  }

  /**
   * Extract terminal information from airport data
   */
  private static extractTerminalInfo(airportCode: string, airportName: string): string | undefined {
    // This could be enhanced with a terminal mapping database
    // For now, return undefined as terminal info is not always available
    return undefined;
  }

  /**
   * Determine refundability from segment and overall data
   */
  private static determineRefundability(segment: any, dataInfo: TripJackApiDataInfo): boolean {
    try {
      // Check fare rules for refundability
      if (dataInfo.extra?.tripjack_response?.tripInfos) {
        const tripInfo = dataInfo.extra.tripjack_response.tripInfos[0];
        if (tripInfo?.totalPriceList) {
          const priceInfo = tripInfo.totalPriceList[0];
          return priceInfo?.refundable || false;
        }
      }
    } catch (error) {
      console.warn('Error determining refundability:', error);
    }

    return false; // Default to non-refundable
  }

  /**
   * Calculate total fare from TripJack response with improved error handling
   */
  static calculateTotalFare(dataInfo: TripJackApiDataInfo): {
    baseFare: number;
    taxes: number;
    totalAmount: number;
    currency: string;
  } {
    const defaultFare = {
      baseFare: 0,
      taxes: 0,
      totalAmount: 0,
      currency: 'INR'
    };

    try {
      // Try multiple sources for fare information
      let fareData = null;

      // Primary source: tripInfos fare data
      if (dataInfo.extra?.tripjack_response?.tripInfos && Array.isArray(dataInfo.extra.tripjack_response.tripInfos)) {
        const tripInfo = dataInfo.extra.tripjack_response.tripInfos[0];
        if (tripInfo?.totalPriceList && Array.isArray(tripInfo.totalPriceList)) {
          const priceInfo = tripInfo.totalPriceList[0];
          if (priceInfo?.fd?.ADULT?.fC) {
            fareData = priceInfo.fd.ADULT.fC;
          }
        }
      }

      // Secondary source: direct totalPriceList
      if (!fareData && dataInfo.extra?.tripjack_response?.totalPriceList && Array.isArray(dataInfo.extra.tripjack_response.totalPriceList)) {
        const priceInfo = dataInfo.extra.tripjack_response.totalPriceList[0];
        if (priceInfo?.fd?.ADULT?.fC) {
          fareData = priceInfo.fd.ADULT.fC;
        }
      }

      if (fareData) {
        const baseFare = Number(fareData.BF) || 0;
        const taxes = Number(fareData.TAF) || 0;
        const totalAmount = Number(fareData.NF) || (baseFare + taxes);

        // Validate fare amounts
        if (baseFare >= 0 && taxes >= 0 && totalAmount >= 0) {
          return {
            baseFare,
            taxes,
            totalAmount,
            currency: 'INR'
          };
        }
      }
    } catch (error) {
      console.warn('Error calculating total fare from TripJack response:', error);
    }

    return defaultFare;
  }

  /**
   * Extract baggage information from TripJack response with optimization
   */
  static extractBaggageInfo(dataInfo: TripJackApiDataInfo): {
    cabin: string;
    checked: string;
  } {
    const defaultBaggage = {
      cabin: '7 kg',
      checked: '15 kg'
    };

    try {
      // Try multiple sources for baggage information
      let baggageInfo = null;

      // Primary source: tripInfos baggage data
      if (dataInfo.extra?.tripjack_response?.tripInfos && Array.isArray(dataInfo.extra.tripjack_response.tripInfos)) {
        const tripInfo = dataInfo.extra.tripjack_response.tripInfos[0];
        if (tripInfo?.totalPriceList && Array.isArray(tripInfo.totalPriceList)) {
          const priceInfo = tripInfo.totalPriceList[0];
          if (priceInfo?.fd?.ADULT?.bI) {
            baggageInfo = priceInfo.fd.ADULT.bI;
          }
        }
      }

      // Secondary source: flight segments baggage allowance
      if (!baggageInfo && dataInfo.Trips && Array.isArray(dataInfo.Trips)) {
        for (const trip of dataInfo.Trips) {
          if (trip.Journey && Array.isArray(trip.Journey)) {
            for (const journey of trip.Journey) {
              if (journey.Segments && Array.isArray(journey.Segments)) {
                for (const segment of journey.Segments) {
                  if (segment.BaggageAllowance) {
                    // Parse baggage allowance string (e.g., "7kg cabin, 15kg checked")
                    const baggageStr = segment.BaggageAllowance.toLowerCase();
                    const cabinMatch = baggageStr.match(/(\d+)\s*kg.*cabin/);
                    const checkedMatch = baggageStr.match(/(\d+)\s*kg.*checked/);

                    return {
                      cabin: cabinMatch ? `${cabinMatch[1]} kg` : defaultBaggage.cabin,
                      checked: checkedMatch ? `${checkedMatch[1]} kg` : defaultBaggage.checked
                    };
                  }
                }
              }
            }
          }
        }
      }

      if (baggageInfo) {
        const cabin = baggageInfo.cB || defaultBaggage.cabin;
        const checked = baggageInfo.iB || defaultBaggage.checked;

        // Validate baggage format
        const isValidBaggage = (baggage: string) => {
          return baggage && (baggage.includes('kg') || baggage.includes('piece') || baggage.includes('lb'));
        };

        return {
          cabin: isValidBaggage(cabin) ? cabin : defaultBaggage.cabin,
          checked: isValidBaggage(checked) ? checked : defaultBaggage.checked
        };
      }
    } catch (error) {
      console.warn('Error extracting baggage info from TripJack response:', error);
    }

    return defaultBaggage;
  }

  /**
   * Validate TripJack response structure
   */
  static validateResponse(dataInfo: TripJackApiDataInfo): {
    isValid: boolean;
    errors: string[];
  } {
    const errors: string[] = [];

    if (!dataInfo) {
      errors.push('Response data is null or undefined');
      return { isValid: false, errors };
    }

    if (!dataInfo.Trips || !Array.isArray(dataInfo.Trips)) {
      errors.push('Trips data is missing or invalid');
    }

    if (dataInfo.Trips && dataInfo.Trips.length === 0) {
      errors.push('No trips found in response');
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }
}
