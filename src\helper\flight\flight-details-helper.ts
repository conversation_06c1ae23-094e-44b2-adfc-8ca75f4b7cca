/**
 * Flight Details Helper Functions
 * Helper functions for transforming TripJack data and processing flight details
 */

import type { TripJackApiDataInfo, TransformedLegacyFormat } from '../../models/flight/flight-details.model';
import { TripJackResponseAdapter } from './tripjack-response-adapter';

/**
 * Transform TripJack API response to legacy format with enhanced field mapping
 */
export function transformTripJackToLegacyFormat(dataInfo: TripJackApiDataInfo): TransformedLegacyFormat {
  try {
    // Validate input data
    const validation = TripJackResponseAdapter.validateResponse(dataInfo);
    if (!validation.isValid) {
      console.warn('Invalid TripJack response structure:', validation.errors);
    }

    // Transform all components with error handling
    const flightSegments = TripJackResponseAdapter.transformFlightSegments(dataInfo);
    const fareBreakdown = TripJackResponseAdapter.calculateTotalFare(dataInfo);
    const passengerInfo = TripJackResponseAdapter.extractPassengerInfo(dataInfo);
    const baggageInfo = TripJackResponseAdapter.extractBaggageInfo(dataInfo);
    const fareRules = TripJackResponseAdapter.transformFareRulesResponse(dataInfo);
    const ssrServices = TripJackResponseAdapter.transformSSRResponse(dataInfo);

    // Enhanced fare breakdown with additional calculations
    const enhancedFareBreakdown = {
      ...fareBreakdown,
      passengers: passengerInfo,
      // Calculate per-passenger breakdown
      perPassengerBreakdown: calculatePerPassengerFare(fareBreakdown, passengerInfo),
      // Add currency formatting
      formattedAmounts: {
        baseFare: formatCurrencyAmount(fareBreakdown.baseFare, fareBreakdown.currency),
        taxes: formatCurrencyAmount(fareBreakdown.taxes, fareBreakdown.currency),
        totalAmount: formatCurrencyAmount(fareBreakdown.totalAmount, fareBreakdown.currency)
      }
    };

    // Enhanced baggage info with validation
    const enhancedBaggageInfo = {
      ...baggageInfo,
      additionalServices: ssrServices.BaggageServices || [],
      // Add meal and seat services for completeness
      mealServices: ssrServices.MealServices || [],
      seatServices: ssrServices.SeatServices || [],
      // Validate baggage allowance format
      isValidFormat: validateBaggageFormat(baggageInfo)
    };

    // Enhanced flight segments with additional metadata
    const enhancedFlightSegments = flightSegments.map((segment, index) => ({
      ...segment,
      segmentIndex: index,
      // Add calculated fields
      estimatedDuration: calculateSegmentDuration(segment.departure.time, segment.arrival.time),
      // Add airline logo URL
      airlineLogoUrl: getAirlineLogoUrl(segment.vac || ''),
      // Add route information
      route: `${segment.departure.code}-${segment.arrival.code}`
    }));

    return {
      flightSegments: enhancedFlightSegments,
      fareBreakdown: enhancedFareBreakdown,
      baggageInfo: enhancedBaggageInfo,
      fareRules: fareRules || [],
      // Add metadata for debugging and analytics
      metadata: {
        transformedAt: new Date().toISOString(),
        sourceDataValid: validation.isValid,
        warnings: validation.errors,
        segmentCount: enhancedFlightSegments.length,
        totalPassengers: passengerInfo.ADULT + passengerInfo.CHILD + passengerInfo.INFANT
      }
    };
  } catch (error) {
    console.error('Error transforming TripJack data to legacy format:', error);

    // Return minimal fallback structure
    return createFallbackLegacyFormat(dataInfo);
  }
}

/**
 * Calculate per-passenger fare breakdown
 */
function calculatePerPassengerFare(fareBreakdown: any, passengerInfo: any) {
  const totalPassengers = passengerInfo.ADULT + passengerInfo.CHILD + passengerInfo.INFANT;

  if (totalPassengers === 0) return null;

  return {
    baseFarePerPerson: Math.round(fareBreakdown.baseFare / totalPassengers),
    taxesPerPerson: Math.round(fareBreakdown.taxes / totalPassengers),
    totalPerPerson: Math.round(fareBreakdown.totalAmount / totalPassengers)
  };
}

/**
 * Validate baggage format
 */
function validateBaggageFormat(baggageInfo: any): boolean {
  const isValidBaggage = (baggage: string) => {
    return baggage && (baggage.includes('kg') || baggage.includes('piece') || baggage.includes('lb'));
  };

  return isValidBaggage(baggageInfo.cabin) && isValidBaggage(baggageInfo.checked);
}

/**
 * Create fallback legacy format when transformation fails
 */
function createFallbackLegacyFormat(dataInfo: TripJackApiDataInfo): TransformedLegacyFormat {
  return {
    flightSegments: [],
    fareBreakdown: {
      baseFare: 0,
      taxes: 0,
      totalAmount: 0,
      currency: 'INR',
      passengers: { ADULT: 1, CHILD: 0, INFANT: 0 }
    },
    baggageInfo: {
      cabin: '7 kg',
      checked: '15 kg',
      additionalServices: []
    },
    fareRules: [],
    metadata: {
      transformedAt: new Date().toISOString(),
      sourceDataValid: false,
      warnings: ['Transformation failed, using fallback data'],
      segmentCount: 0,
      totalPassengers: 1
    }
  };
}

/**
 * Extract baggage information from TripJack response for specific trip index
 */
export function extractBaggageFromTripJackResponse(dataInfo: TripJackApiDataInfo, index: number): {
  cabin: string;
  checked: string;
  additionalServices: Array<{
    code: string;
    description: string;
    amount: string;
    currency: string;
  }>;
} {
  const defaultBaggage = {
    cabin: '7 kg',
    checked: '15 kg',
    additionalServices: []
  };

  try {
    // Extract basic baggage info
    const baggageInfo = TripJackResponseAdapter.extractBaggageInfo(dataInfo);
    
    // Extract additional baggage services
    const ssrServices = TripJackResponseAdapter.transformSSRResponse(dataInfo);
    
    return {
      ...baggageInfo,
      additionalServices: ssrServices.BaggageServices || []
    };
  } catch (error) {
    console.error('Error extracting baggage information:', error);
    return defaultBaggage;
  }
}

/**
 * Extract fare rules from TripJack response for specific trip index
 */
export function extractFareRulesFromTripJackResponse(dataInfo: TripJackApiDataInfo, index: number): Array<{
  ruleType: string;
  description: string;
  penalty?: string;
}> {
  try {
    const fareRules = TripJackResponseAdapter.transformFareRulesResponse(dataInfo);
    
    return fareRules.map(rule => ({
      ruleType: rule.RuleType,
      description: rule.Description,
      penalty: rule.Penalty
    }));
  } catch (error) {
    console.error('Error extracting fare rules:', error);
    return [];
  }
}

/**
 * Create fallback fare summary when TripJack data is incomplete with enhanced error handling
 */
export function createFallbackFareSummary(dataInfo: TripJackApiDataInfo): {
  baseFare: number;
  taxes: number;
  totalAmount: number;
  currency: string;
  breakdown: {
    baseFare: number;
    taxes: {
      yq: number;
      yr: number;
      k3: number;
      other: number;
    };
    fees: {
      serviceFee: number;
      transactionFee: number;
    };
    total: number;
  };
  passengers: {
    ADULT: number;
    CHILD: number;
    INFANT: number;
  };
  isEstimated: boolean;
  warnings: string[];
} {
  const warnings: string[] = [];
  let fareInfo;
  let passengerInfo;

  try {
    fareInfo = TripJackResponseAdapter.calculateTotalFare(dataInfo);
    passengerInfo = TripJackResponseAdapter.extractPassengerInfo(dataInfo);
  } catch (error) {
    console.warn('Error in fallback fare summary calculation:', error);
    warnings.push('Unable to extract complete fare information');

    // Use absolute fallback values
    fareInfo = {
      baseFare: 0,
      taxes: 0,
      totalAmount: 0,
      currency: 'INR'
    };
    passengerInfo = {
      ADULT: 1,
      CHILD: 0,
      INFANT: 0
    };
  }

  // Validate fare amounts
  const isValidFare = fareInfo.baseFare > 0 || fareInfo.totalAmount > 0;
  if (!isValidFare) {
    warnings.push('Fare amounts appear to be incomplete or invalid');
  }

  // Create detailed breakdown with fallback values
  const breakdown = {
    baseFare: fareInfo.baseFare,
    taxes: {
      yq: Math.round(fareInfo.taxes * 0.4), // Estimate YQ as 40% of total taxes
      yr: Math.round(fareInfo.taxes * 0.2), // Estimate YR as 20% of total taxes
      k3: Math.round(fareInfo.taxes * 0.1), // Estimate K3 as 10% of total taxes
      other: Math.round(fareInfo.taxes * 0.3) // Remaining 30% as other taxes
    },
    fees: {
      serviceFee: 0, // Default service fee
      transactionFee: 0 // Default transaction fee
    },
    total: fareInfo.totalAmount
  };

  return {
    baseFare: fareInfo.baseFare,
    taxes: fareInfo.taxes,
    totalAmount: fareInfo.totalAmount,
    currency: fareInfo.currency,
    breakdown,
    passengers: passengerInfo,
    isEstimated: !isValidFare || warnings.length > 0,
    warnings
  };
}

/**
 * Enhanced error handling for incomplete TripJack data
 */
export function handleIncompleteTripJackData(dataInfo: TripJackApiDataInfo | null): {
  isComplete: boolean;
  missingFields: string[];
  canProceed: boolean;
  fallbackData?: Partial<TransformedLegacyFormat>;
} {
  const missingFields: string[] = [];

  if (!dataInfo) {
    return {
      isComplete: false,
      missingFields: ['Complete response data'],
      canProceed: false
    };
  }

  // Check essential fields
  if (!dataInfo.Trips || !Array.isArray(dataInfo.Trips) || dataInfo.Trips.length === 0) {
    missingFields.push('Flight trips data');
  }

  if (!dataInfo.extra?.tripjack_response) {
    missingFields.push('TripJack response metadata');
  }

  // Check if we have at least some flight segments
  let hasFlightSegments = false;
  try {
    if (dataInfo.Trips) {
      for (const trip of dataInfo.Trips) {
        if (trip.Journey && Array.isArray(trip.Journey)) {
          for (const journey of trip.Journey) {
            if (journey.Segments && Array.isArray(journey.Segments) && journey.Segments.length > 0) {
              hasFlightSegments = true;
              break;
            }
          }
        }
        if (hasFlightSegments) break;
      }
    }
  } catch (error) {
    console.warn('Error checking flight segments:', error);
  }

  if (!hasFlightSegments) {
    missingFields.push('Flight segments');
  }

  const canProceed = hasFlightSegments; // We can proceed if we have at least flight segments

  // Create fallback data if we can proceed
  let fallbackData: Partial<TransformedLegacyFormat> | undefined;
  if (canProceed) {
    try {
      fallbackData = {
        flightSegments: TripJackResponseAdapter.transformFlightSegments(dataInfo),
        fareBreakdown: {
          baseFare: 0,
          taxes: 0,
          totalAmount: 0,
          currency: 'INR',
          passengers: { ADULT: 1, CHILD: 0, INFANT: 0 }
        },
        baggageInfo: {
          cabin: '7 kg',
          checked: '15 kg',
          additionalServices: []
        },
        fareRules: []
      };
    } catch (error) {
      console.warn('Error creating fallback data:', error);
    }
  }

  return {
    isComplete: missingFields.length === 0,
    missingFields,
    canProceed,
    fallbackData
  };
}

/**
 * Retry logic with exponential backoff
 */
export function createRetryHandler(maxRetries: number = 3, baseDelay: number = 1000) {
  return async function retryWithBackoff<T>(
    operation: () => Promise<T>,
    retryCount: number = 0
  ): Promise<T> {
    try {
      return await operation();
    } catch (error) {
      if (retryCount >= maxRetries) {
        throw error;
      }

      const delay = baseDelay * Math.pow(2, retryCount);
      console.warn(`Operation failed, retrying in ${delay}ms (attempt ${retryCount + 1}/${maxRetries}):`, error);

      await new Promise(resolve => setTimeout(resolve, delay));
      return retryWithBackoff(operation, retryCount + 1);
    }
  };
}

/**
 * Format flight duration from minutes to human readable format
 */
export function formatFlightDuration(durationMinutes: number): string {
  if (!durationMinutes || durationMinutes <= 0) {
    return '0h 0m';
  }

  const hours = Math.floor(durationMinutes / 60);
  const minutes = durationMinutes % 60;

  if (hours === 0) {
    return `${minutes}m`;
  }

  if (minutes === 0) {
    return `${hours}h`;
  }

  return `${hours}h ${minutes}m`;
}

/**
 * Format currency amount with proper decimal places
 */
export function formatCurrencyAmount(amount: number, currency: string = 'INR'): string {
  if (!amount || isNaN(amount)) {
    return `${currency} 0`;
  }

  // For INR, show no decimal places for whole numbers, one decimal place otherwise
  if (currency === 'INR') {
    if (amount % 1 === 0) {
      return `₹ ${amount.toLocaleString('en-IN')}`;
    } else {
      return `₹ ${amount.toLocaleString('en-IN', { minimumFractionDigits: 1, maximumFractionDigits: 1 })}`;
    }
  }

  // For other currencies, use standard formatting
  return `${currency} ${amount.toLocaleString('en-US', { minimumFractionDigits: 0, maximumFractionDigits: 2 })}`;
}

/**
 * Parse and format flight time (handles various time formats)
 */
export function formatFlightTime(timeString: string): string {
  if (!timeString) {
    return '--:--';
  }

  try {
    // Handle ISO date string
    if (timeString.includes('T')) {
      const date = new Date(timeString);
      return date.toLocaleTimeString('en-US', { 
        hour: '2-digit', 
        minute: '2-digit', 
        hour12: false 
      });
    }

    // Handle HH:MM format
    if (timeString.includes(':')) {
      const [hours, minutes] = timeString.split(':');
      const hour = parseInt(hours, 10);
      const min = parseInt(minutes, 10);
      
      if (!isNaN(hour) && !isNaN(min)) {
        return `${hour.toString().padStart(2, '0')}:${min.toString().padStart(2, '0')}`;
      }
    }

    // Handle timestamp
    if (!isNaN(Number(timeString))) {
      const date = new Date(Number(timeString));
      return date.toLocaleTimeString('en-US', { 
        hour: '2-digit', 
        minute: '2-digit', 
        hour12: false 
      });
    }

    return timeString;
  } catch (error) {
    console.error('Error formatting flight time:', error);
    return '--:--';
  }
}

/**
 * Validate flight details data completeness
 */
export function validateFlightDetailsData(dataInfo: TripJackApiDataInfo): {
  isValid: boolean;
  missingFields: string[];
  warnings: string[];
} {
  const validation = TripJackResponseAdapter.validateResponse(dataInfo);
  const missingFields: string[] = [...validation.errors];
  const warnings: string[] = [];

  // Check for essential flight information
  if (dataInfo.Trips && dataInfo.Trips.length > 0) {
    const firstTrip = dataInfo.Trips[0];
    if (!firstTrip.Journey || firstTrip.Journey.length === 0) {
      missingFields.push('Journey information');
    } else {
      const firstJourney = firstTrip.Journey[0];
      if (!firstJourney.Segments || firstJourney.Segments.length === 0) {
        missingFields.push('Flight segments');
      }
    }
  }

  // Check for fare information
  if (!dataInfo.extra?.tripjack_response?.tripInfos) {
    warnings.push('Fare information may be incomplete');
  }

  // Check for baggage information
  const baggageInfo = TripJackResponseAdapter.extractBaggageInfo(dataInfo);
  if (baggageInfo.cabin === '7 kg' && baggageInfo.checked === '15 kg') {
    warnings.push('Using default baggage allowance');
  }

  return {
    isValid: missingFields.length === 0,
    missingFields,
    warnings
  };
}

/**
 * Get airline logo URL (fallback implementation)
 */
export function getAirlineLogoUrl(airlineCode: string): string {
  // This would typically connect to an airline logo service
  // For now, return a placeholder or default logo
  return `/assets/airlines/${airlineCode.toLowerCase()}.png`;
}

/**
 * Calculate segment duration between departure and arrival times
 */
export function calculateSegmentDuration(departureTime: string, arrivalTime: string): string {
  try {
    const departure = new Date(departureTime);
    const arrival = new Date(arrivalTime);
    const diffMs = arrival.getTime() - departure.getTime();
    const diffMinutes = Math.floor(diffMs / (1000 * 60));

    return formatFlightDuration(diffMinutes);
  } catch (error) {
    console.error('Error calculating segment duration:', error);
    return '--';
  }
}

/**
 * Calculate layover time between segments
 */
export function calculateLayoverTime(arrivalTime: string, departureTime: string): string {
  try {
    const arrival = new Date(arrivalTime);
    const departure = new Date(departureTime);
    const diffMs = departure.getTime() - arrival.getTime();
    const diffMinutes = Math.floor(diffMs / (1000 * 60));

    return formatFlightDuration(diffMinutes);
  } catch (error) {
    console.error('Error calculating layover time:', error);
    return '--';
  }
}
