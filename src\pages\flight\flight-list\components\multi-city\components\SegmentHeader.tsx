import React from 'react';
import AirlineIcon from './AirlineIcon';
import type { SegmentHeaderProps } from './types';

const SegmentHeader: React.FC<SegmentHeaderProps> = ({
  segment,
  selectedFlight,
  isCollapsed,
  onToggleCollapse
}) => {
  const priceRange = {
    min: Math.min(...segment.flights.map((f) => f.fare.price)),
    max: Math.max(...segment.flights.map((f) => f.fare.price))
  };

  return (
    <div
      className={`relative overflow-hidden transition-all duration-300 ${
        selectedFlight
          ? "bg-gradient-to-r from-emerald-500 to-teal-600"
          : "bg-gradient-to-r from-blue-500 to-indigo-600"
      }`}
    >
      {/* Background Pattern */}
      <div className="absolute inset-0 opacity-10">
        <div className="absolute inset-0 bg-[url('data:image/svg+xml,%3Csvg%20width%3D%2260%22%20height%3D%2260%22%20viewBox%3D%220%200%2060%2060%22%20xmlns%3D%22http%3A//www.w3.org/2000/svg%22%3E%3Cg%20fill%3D%22none%22%20fill-rule%3D%22evenodd%22%3E%3Cg%20fill%3D%22%23ffffff%22%20fill-opacity%3D%220.1%22%3E%3Ccircle%20cx%3D%2230%22%20cy%3D%2230%22%20r%3D%222%22/%3E%3C/g%3E%3C/g%3E%3C/svg%3E')]"></div>
      </div>

      <div className="relative text-white p-4 lg:p-5">
        <div className="flex flex-col lg:flex-row lg:justify-between lg:items-center gap-3">
          {/* Left Section - Route Info */}
          <div className="flex-1">
            <div className="flex items-center gap-3 mb-2">
              {/* Status Badge */}
              {selectedFlight && (
                <div className="flex items-center bg-white/20 backdrop-blur-sm rounded-full px-3 py-1">
                  <i className="fas fa-check-circle text-emerald-200 mr-1.5"></i>
                  <span className="text-sm font-medium">Selected</span>
                </div>
              )}
            </div>

            {/* City Names with Airport Codes and Date */}
            <div className="flex flex-col lg:flex-row lg:items-center gap-2 text-white/90">
              <div className="text-lg">
                <span className="font-medium">{segment.origin.city} ({segment.origin.code})</span>
                <span className="mx-2">→</span>
                <span className="font-medium">{segment.destination.city} ({segment.destination.code})</span>
              </div>
              <div className="hidden lg:block w-1 h-1 bg-white/40 rounded-full"></div>
              <div className="text-sm font-medium">{segment.date}</div>
              {!selectedFlight && (
                <>
                  <div className="hidden lg:block w-1 h-1 bg-white/40 rounded-full"></div>
                  <div className="text-sm">
                    {segment.flights.length} flight{segment.flights.length !== 1 ? 's' : ''} available
                  </div>
                </>
              )}
            </div>


          </div>

          {/* Right Section - Price Range and Actions */}
          <div className="flex items-center justify-between lg:justify-end gap-4">
            {/* Price Range (when no flight selected) */}
            {!selectedFlight && (
              <div className="text-right">
                <div className="text-xs text-white/70 uppercase tracking-wide">Price Range</div>
                <div className="text-lg font-bold">
                  ${priceRange.min.toLocaleString()} - ${priceRange.max.toLocaleString()}
                </div>
              </div>
            )}

            {/* Collapse/Expand Button (when flight selected) */}
            {selectedFlight && (
              <button
                onClick={(e) => {
                  e.stopPropagation();
                  onToggleCollapse();
                }}
                className="flex items-center gap-2 bg-white/20 hover:bg-white/30 backdrop-blur-sm text-white px-4 py-2 rounded-lg transition-all duration-200 font-medium"
              >
                <i
                  className={`fas fa-${
                    isCollapsed ? "chevron-down" : "chevron-up"
                  } text-sm`}
                ></i>
                <span className="hidden sm:inline">
                  {isCollapsed ? "Show Options" : "Hide Options"}
                </span>
              </button>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default SegmentHeader;
