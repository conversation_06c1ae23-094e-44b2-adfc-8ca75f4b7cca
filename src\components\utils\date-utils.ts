export function formatShortFriendlyDate(date: Date | string | null): string {
  if (!date) return '';

  // Convert string to Date if needed
  const dateObj = date instanceof Date ? date : new Date(date);

  // Check if the date is valid
  if (isNaN(dateObj.getTime())) return '';

  const options: Intl.DateTimeFormatOptions = {
    weekday: 'short',
    day: '2-digit',
    month: 'short',
    year: '2-digit',
  };

  return dateObj.toLocaleDateString('en-US', options);
}