import React, { useCallback, useEffect } from 'react';
import type { FlightFiltersProps } from './types';

const FlightFilters: React.FC<FlightFiltersProps> = ({
  filters,
  onFiltersChange,
  timeSlots,
  airlines,
  showAllAirlines,
  onToggleAirlines,
  isDragging,
  onDragStart
}) => {
  const handleFilterChange = <K extends keyof typeof filters>(key: K, value: typeof filters[K]) => {
    onFiltersChange({
      ...filters,
      [key]: value
    });
  };

  const handlePriceRangeChange = useCallback((type: 'min' | 'max', value: number) => {
    onFiltersChange({
      ...filters,
      priceRange: {
        ...filters.priceRange,
        [type]: value
      }
    });
  }, [filters, onFiltersChange]);

  const handleMouseMove = useCallback((e: MouseEvent) => {
    if (!isDragging) return;

    const slider = document.querySelector('.price-slider') as HTMLElement;
    if (!slider) return;

    const rect = slider.getBoundingClientRect();
    const percentage = Math.max(0, Math.min(1, (e.clientX - rect.left) / rect.width));
    const value = Math.round(200 + percentage * (1500 - 200));

    handlePriceRangeChange(isDragging, value);
  }, [isDragging, handlePriceRangeChange]);

  const handleMouseUp = useCallback(() => {
    // Mouse up handling is managed by the parent component
    // No need to call onDragStart here
  }, []);

  useEffect(() => {
    if (isDragging) {
      document.addEventListener('mousemove', handleMouseMove);
      document.addEventListener('mouseup', handleMouseUp);
      return () => {
        document.removeEventListener('mousemove', handleMouseMove);
        document.removeEventListener('mouseup', handleMouseUp);
      };
    }
  }, [isDragging, handleMouseMove, handleMouseUp]);

  return (
    <div className="hidden xl:block w-64 space-y-4 flex-shrink-0">
      {/* Corporate Benefits - Compact Design */}
      <div className="bg-gradient-to-br from-blue-600 to-blue-700 rounded-lg p-3 text-white mb-3">
        <div className="flex items-center mb-2">
          <i className="fas fa-briefcase text-sm"></i>
          <h3 className="text-sm font-semibold ml-2">Corporate Benefits</h3>
        </div>
        <ul className="space-y-1">
          <li className="flex items-center">
            <i className="fas fa-check-circle mr-1.5 text-xs"></i>
            <span className="text-xs">Special corporate rates</span>
          </li>
          <li className="flex items-center">
            <i className="fas fa-check-circle mr-1.5 text-xs"></i>
            <span className="text-xs">Priority boarding</span>
          </li>
          <li className="flex items-center">
            <i className="fas fa-check-circle mr-1.5 text-xs"></i>
            <span className="text-xs">Lounge access included</span>
          </li>
          <li className="flex items-center">
            <i className="fas fa-check-circle mr-1.5 text-xs"></i>
            <span className="text-xs">Extra baggage allowance</span>
          </li>
        </ul>
      </div>

      {/* Filter Options - Fixed Layout */}
      <div className="flight-card-background rounded-lg shadow-sm sticky top-16 lg:top-20 max-h-[calc(100vh-100px)] lg:max-h-[calc(100vh-120px)] flex flex-col border flight-card-border">
        <div className="p-3 pb-2 border-b border-primary flex-shrink-0">
          <h3 className="font-semibold text-primary text-sm">Filters</h3>
        </div>

        <div className="p-3 overflow-y-auto flex-1 custom-scrollbar">
          {/* Departure Time */}
          <div className="mb-4">
            <h4 className="text-xs font-medium text-secondary mb-2">Departure Time</h4>
            <div className="grid grid-cols-2 gap-1.5">
              {Object.entries(timeSlots).map(([key, slot]) => (
                <button
                  key={key}
                  className={`!rounded-button flex flex-col items-center p-2 ${
                    filters.departureTime === key
                      ? 'primary-background-lightest primary-text'
                      : 'neutral-background-50 hover-primary-background-lightest'
                  } transition-colors duration-200 rounded cursor-pointer group whitespace-nowrap focus:outline-none focus:ring-2 focus:ring-primary-color focus:ring-offset-1`}
                  onClick={() => handleFilterChange('departureTime', filters.departureTime === key ? '' : key)}
                  aria-pressed={filters.departureTime === key}
                  aria-label={`Filter by ${slot.label} departure time`}
                >
                  <i className={`fas fa-${slot.icon} ${
                    filters.departureTime === key
                      ? 'primary-text'
                      : 'text-tertiary group-hover:primary-text'
                  } mb-1 text-xs`}></i>
                  <span className={`text-xs ${
                    filters.departureTime === key
                      ? 'primary-text'
                      : 'text-secondary'
                  }`}>{slot.label}</span>
                  <span className="text-xs text-tertiary">{slot.range}</span>
                </button>
              ))}
            </div>
          </div>

          {/* Arrival Time */}
          <div className="mb-4">
            <h4 className="text-xs font-medium text-secondary mb-2">Arrival Time</h4>
            <div className="grid grid-cols-2 gap-1.5">
              {Object.entries(timeSlots).map(([key, slot]) => (
                <button
                  key={key}
                  className={`!rounded-button flex flex-col items-center p-2 ${
                    filters.arrivalTime === key
                      ? 'primary-background-lightest primary-text'
                      : 'neutral-background-50 hover-primary-background-lightest'
                  } transition-colors duration-200 rounded cursor-pointer group whitespace-nowrap focus:outline-none focus:ring-2 focus:ring-primary-color focus:ring-offset-1`}
                  onClick={() => handleFilterChange('arrivalTime', filters.arrivalTime === key ? '' : key)}
                  aria-pressed={filters.arrivalTime === key}
                  aria-label={`Filter by ${slot.label} arrival time`}
                >
                  <i className={`fas fa-${slot.icon} ${
                    filters.arrivalTime === key
                      ? 'primary-text'
                      : 'text-tertiary group-hover:primary-text'
                  } mb-1 text-xs`}></i>
                  <span className={`text-xs ${
                    filters.arrivalTime === key
                      ? 'primary-text'
                      : 'text-secondary'
                  }`}>{slot.label}</span>
                  <span className="text-xs text-tertiary">{slot.range}</span>
                </button>
              ))}
            </div>
          </div>

          {/* Stops */}
          <div className="mb-3">
            <h4 className="text-xs font-medium text-gray-700 mb-2">Stops</h4>
            <div className="space-y-2">
              <label className="flex items-center cursor-pointer">
                <input
                  type="checkbox"
                  className="form-checkbox text-blue-600 rounded"
                  checked={filters.directFlights}
                  onChange={(e) => handleFilterChange('directFlights', e.target.checked)}
                />
                <span className="ml-2 text-sm text-gray-600">Direct flights only</span>
              </label>
            </div>
          </div>

          {/* Airlines */}
          <div className="mb-3">
            <h4 className="text-xs font-medium text-gray-700 mb-2">Airlines</h4>
            <div className="space-y-2">
              {airlines.slice(0, showAllAirlines ? airlines.length : 3).map((airline, index) => {
                const isSelected = filters.selectedAirlines?.includes(airline.name) || false;
                return (
                  <label key={index} className="flex items-center justify-between p-2 bg-gray-50 rounded hover:bg-blue-50 transition-all cursor-pointer group">
                    <div className="flex items-center">
                      <div className="relative flex items-center">
                        <input
                          type="checkbox"
                          className="peer sr-only"
                          checked={isSelected}
                          onChange={(e) => {
                            const selectedAirlines = filters.selectedAirlines || [];
                            const newSelectedAirlines = e.target.checked
                              ? [...selectedAirlines, airline.name]
                              : selectedAirlines.filter(name => name !== airline.name);
                            handleFilterChange('selectedAirlines', newSelectedAirlines);
                          }}
                        />
                        <div className={`w-4 h-4 bg-white border-2 rounded transition-all ${
                          isSelected
                            ? 'bg-blue-600 border-blue-600'
                            : 'border-gray-300'
                        }`}>
                          {isSelected && (
                            <i className="fas fa-check text-white text-xs absolute left-1 top-0"></i>
                          )}
                        </div>
                      </div>
                      <span className="ml-2 text-xs font-medium text-gray-700 group-hover:text-blue-700">{airline.name}</span>
                    </div>
                    <div className="flex items-center text-xs text-gray-500">
                      <i className="fas fa-star text-amber-400 mr-1 text-xs"></i>
                      <span>{airline.rating}</span>
                    </div>
                  </label>
                );
              })}
              <button
                onClick={onToggleAirlines}
                className="w-full text-sm text-blue-600 hover:text-blue-700 mt-2 flex items-center justify-center transition-colors"
              >
                <i className={`fas ${showAllAirlines ? 'fa-minus' : 'fa-plus'} mr-2`}></i>
                {showAllAirlines ? 'Show Less Airlines' : 'Show More Airlines'}
              </button>
            </div>
          </div>

          {/* Fare Types */}
          <div className="mb-3">
            <h4 className="text-xs font-medium text-gray-700 mb-2">Fare Types</h4>
            <div className="space-y-2">
              <label className="flex items-center cursor-pointer">
                <input
                  type="checkbox"
                  className="form-checkbox text-blue-600 rounded"
                  checked={filters.corporateRates}
                  onChange={(e) => handleFilterChange('corporateRates', e.target.checked)}
                />
                <span className="ml-2 text-sm text-gray-600">Corporate Rates</span>
              </label>
              <label className="flex items-center cursor-pointer">
                <input
                  type="checkbox"
                  className="form-checkbox text-blue-600 rounded"
                  checked={filters.refundable}
                  onChange={(e) => handleFilterChange('refundable', e.target.checked)}
                />
                <span className="ml-2 text-sm text-gray-600">Refundable Fares</span>
              </label>
            </div>
          </div>

          {/* Price Range Slider */}
          <div className="mb-3">
            <div className="flex items-center justify-between mb-2">
              <h4 className="text-xs font-medium text-gray-700">Price Range</h4>
              <div className="text-xs text-gray-500">
                ${filters.priceRange.min} - ${filters.priceRange.max}
              </div>
            </div>
            <div className="price-slider h-2 bg-gray-100 rounded-full relative">
              <div
                className="absolute h-full rounded-full bg-blue-600"
                style={{
                  left: `${((filters.priceRange.min - 200) / (1500 - 200)) * 100}%`,
                  right: `${100 - ((filters.priceRange.max - 200) / (1500 - 200)) * 100}%`
                }}
              ></div>
              <div
                className={`absolute top-1/2 w-4 h-4 bg-white border-2 ${isDragging === 'min' ? 'border-blue-700 scale-110' : 'border-blue-600'} rounded-full transform -translate-x-1/2 -translate-y-1/2 cursor-pointer transition-all`}
                style={{
                  left: `${((filters.priceRange.min - 200) / (1500 - 200)) * 100}%`
                }}
                onMouseDown={() => onDragStart('min')}
              ></div>
              <div
                className={`absolute top-1/2 w-4 h-4 bg-white border-2 ${isDragging === 'max' ? 'border-blue-700 scale-110' : 'border-blue-600'} rounded-full transform -translate-x-1/2 -translate-y-1/2 cursor-pointer transition-all`}
                style={{
                  left: `${((filters.priceRange.max - 200) / (1500 - 200)) * 100}%`
                }}
                onMouseDown={() => onDragStart('max')}
              ></div>
            </div>
          </div>

          {/* Reset Filters - Inside scrollable area */}
          <div className="mt-4 pt-3 border-t border-gray-200">
            <button
              className="!rounded-button w-full bg-gray-100 text-gray-700 py-1.5 px-3 text-xs font-medium hover:bg-gray-200 transition-colors cursor-pointer whitespace-nowrap"
              onClick={() => onFiltersChange({
                directFlights: false,
                refundable: false,
                businessClass: false,
                corporateRates: true,
                departureTime: '',
                arrivalTime: '',
                selectedAirlines: [],
                priceRange: { min: 200, max: 1500 }
              })}
            >
              Reset Filters
            </button>
          </div>
        </div>
      </div>

      {/* Travel Policy - Compact Design */}
      <div className="bg-white rounded-lg shadow-sm p-3 mt-3">
        <div className="flex items-center mb-2">
          <i className="fas fa-shield-alt text-blue-600 text-sm"></i>
          <h3 className="font-semibold text-gray-800 ml-2 text-xs">Travel Policy</h3>
        </div>
        <p className="text-xs text-gray-600 mb-2">All flights comply with your company's travel policy.</p>
        <div className="flex items-center text-xs text-blue-600 cursor-pointer hover:text-blue-700 transition-colors">
          <span>View Policy Details</span>
          <i className="fas fa-chevron-right ml-1 text-xs"></i>
        </div>
      </div>
    </div>
  );
};

export default FlightFilters;
