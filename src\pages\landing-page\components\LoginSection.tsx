import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';

const LoginSection: React.FC = () => {
  const [showPassword, setShowPassword] = useState(false);
  const [username, setUsername] = useState('');
  const [password, setPassword] = useState('');
  const [error, setError] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const navigate = useNavigate();

  const handleLogin = async (e: React.FormEvent) => {
    e.preventDefault();
    setError('');
    setIsLoading(true);

    // Simple authentication check
    if (username === 'dummy' && password === '1234') {
      // Simulate a brief loading state for better UX
      setTimeout(() => {
        setIsLoading(false);
        navigate('/flight/search');
      }, 500);
    } else {
      setTimeout(() => {
        setIsLoading(false);
        setError('Invalid username or password. Please try again.');
      }, 500);
    }
  };

  return (
    <div className="max-w-7xl mx-auto mt-24 px-4">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-12 items-center">
        <div className="relative h-[600px] overflow-hidden rounded-2xl shadow-2xl">
          <img
            src="https://readdy.ai/api/search-image?query=luxurious%20business%20airport%20lounge%20with%20panoramic%20glass%20windows%2C%20elegant%20corporate%20travelers%20relaxing%20in%20premium%20modern%20seating%20area%2C%20professional%20photography%2C%20cinematic%20lighting&width=700&height=600&seq=3&orientation=landscape"
            alt="Login Section"
            className="w-full h-full object-cover"
          />
          <div className="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent"></div>
          <div className="absolute bottom-8 left-8 text-white">
            <h3 className="text-2xl font-bold mb-2">Premium Business Travel</h3>
            <p className="text-sm">Experience luxury and efficiency in every journey</p>
          </div>
        </div>
        <div className="bg-white p-12 rounded-2xl shadow-xl">
          <div className="text-center mb-10">
            <h3 className="text-3xl font-bold text-gray-900 mb-3">Welcome Back</h3>
            <p className="text-gray-600">Access your premium business travel account</p>
          </div>
          <form className="space-y-8" onSubmit={handleLogin}>
            <div className="space-y-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Username</label>
                <div className="relative">
                  <div className="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none">
                    <i className="fas fa-user text-gray-400"></i>
                  </div>
                  <input
                    type="text"
                    value={username}
                    onChange={(e) => setUsername(e.target.value)}
                    className="w-full pl-12 pr-4 py-3 border-2 border-gray-200 rounded-xl text-sm focus:ring-2 focus:ring-blue-500/20 focus:border-blue-500 transition-all"
                    placeholder="Enter username (dummy)"
                    required
                  />
                </div>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Password</label>
                <div className="relative">
                  <div className="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none">
                    <i className="fas fa-lock text-gray-400"></i>
                  </div>
                  <input
                    type={showPassword ? "text" : "password"}
                    value={password}
                    onChange={(e) => setPassword(e.target.value)}
                    className="w-full pl-12 pr-12 py-3 border-2 border-gray-200 rounded-xl text-sm focus:ring-2 focus:ring-blue-500/20 focus:border-blue-500 transition-all"
                    placeholder="Enter password (1234)"
                    required
                  />
                  <button
                    type="button"
                    onClick={() => setShowPassword(!showPassword)}
                    className="absolute right-4 top-1/2 -translate-y-1/2 text-gray-400 hover:text-gray-600 transition-colors"
                  >
                    <i className={`fas ${showPassword ? 'fa-eye-slash' : 'fa-eye'}`}></i>
                  </button>
                </div>
              </div>
            </div>

            {/* Error Message */}
            {error && (
              <div className="bg-red-50 border border-red-200 rounded-lg p-3">
                <div className="flex items-center">
                  <i className="fas fa-exclamation-circle text-red-500 mr-2"></i>
                  <span className="text-sm text-red-700">{error}</span>
                </div>
              </div>
            )}

            <div className="flex items-center justify-between">
              <div className="flex items-center">
                <input
                  type="checkbox"
                  className="h-5 w-5 text-blue-600 focus:ring-2 focus:ring-blue-500/20 border-2 border-gray-300 rounded"
                />
                <label className="ml-3 block text-sm text-gray-600">Keep me signed in</label>
              </div>
              <button type="button" className="text-sm font-medium text-blue-600 hover:text-blue-500 transition-colors">
                Forgot password?
              </button>
            </div>
            <button
              type="submit"
              disabled={isLoading}
              className="!rounded-button w-full bg-blue-600 text-white py-4 px-6 text-sm font-semibold hover:bg-blue-700 disabled:bg-blue-400 disabled:cursor-not-allowed transition-all shadow-lg shadow-blue-600/20 mt-8 flex items-center justify-center"
            >
              {isLoading ? (
                <>
                  <i className="fas fa-spinner fa-spin mr-2"></i>
                  Signing in...
                </>
              ) : (
                'Sign in to your account'
              )}
            </button>
            <div className="text-center mt-6 space-y-3">
              {/* Demo Credentials Hint */}
              <div className="bg-blue-50 border border-blue-200 rounded-lg p-3">
                <div className="text-xs text-blue-700">
                  <i className="fas fa-info-circle mr-1"></i>
                  <strong>Demo Credentials:</strong> Username: <code className="bg-blue-100 px-1 rounded">dummy</code> | Password: <code className="bg-blue-100 px-1 rounded">1234</code>
                </div>
              </div>

              <p className="text-sm text-gray-500">
                Need technical support? <a href="#" className="text-blue-600 hover:text-blue-500 font-medium">Contact our team</a>
              </p>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
};

export default LoginSection;
