import React from 'react';
import { FlightSearchProvider } from '../../../contexts/FlightSearchContext';
import FlightSearchForm from './FlightSearchForm';
import type { FlightSearchFormProps } from './FlightSearchForm';

/**
 * FlightSearchForm component wrapped with FlightSearchProvider
 * This ensures the context is available for the form
 */
const FlightSearchFormWithProvider: React.FC<FlightSearchFormProps> = (props) => {
  return (
    <FlightSearchProvider>
      <FlightSearchForm {...props} />
    </FlightSearchProvider>
  );
};

export default FlightSearchFormWithProvider;
export type { FlightSearchFormProps };
