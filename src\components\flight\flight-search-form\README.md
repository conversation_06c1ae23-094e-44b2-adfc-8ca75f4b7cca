# FlightSearchForm Architecture Documentation

## Overview
The FlightSearchForm component follows a well-structured architecture that separates concerns between state management and UI rendering. This design promotes reusability, maintainability, and follows React best practices.

## Architecture Pattern

### Main FlightSearchForm Component
**Location:** `src/components/flight/flight-search-form/FlightSearchForm.tsx`

**Responsibilities:**
- ✅ All state management (form data, modal states, UI states)
- ✅ All modal logic and positioning
- ✅ Event handlers and business logic
- ✅ Data transformation and validation
- ✅ Modal open/close behavior and auto-scroll functionality

**Key State Variables:**
```typescript
// Form Data State
const [selectedTripType, setSelectedTripType] = useState<'roundTrip' | 'oneWay' | 'multiCity'>('roundTrip');
const [fromAirport, setFromAirport] = useState('New York (JFK)');
const [toAirport, setToAirport] = useState('London (LHR)');
const [selectedDates, setSelectedDates] = useState({ departure: '', return: '' });
const [passengers, setPassengers] = useState({ adults: 1, children: 0, infants: 0 });
const [selectedClass, setSelectedClass] = useState<'economy' | 'premiumEconomy' | 'business' | 'first'>('economy');

// Modal State Management
const [showFromDropdown, setShowFromDropdown] = useState(false);
const [showToDropdown, setShowToDropdown] = useState(false);
const [showCalendar, setShowCalendar] = useState(false);
const [showTravelersClassDropdown, setShowTravelersClassDropdown] = useState(false);
const [showAdvancedSearch, setShowAdvancedSearch] = useState(false);

// Advanced Search State
const [selectedAirlines, setSelectedAirlines] = useState<string[]>([]);
const [selectedFlightOptions, setSelectedFlightOptions] = useState({...});
const [selectedServices, setSelectedServices] = useState({...});
```

### Extracted Reusable Components
**Location:** `src/components/flight/flight-search-form/components/`

All components follow these principles:
- ✅ **Full width styling:** `className="w-full"` for responsive container filling
- ✅ **Props-based data:** Receive all data and handlers from parent component
- ✅ **UI-focused:** Only handle rendering, no internal state management
- ✅ **Reusable:** Can be used in other contexts with different data

## Component Structure

### 1. AirportSearch Component
**File:** `AirportSearch.tsx`

**Purpose:** Renders airport search input with dropdown functionality

**Props Interface:**
```typescript
interface AirportSearchProps {
  label: string;                    // Field label
  icon: string;                     // Icon class name
  placeholder: string;              // Input placeholder
  searchTerm: string;               // Current search value
  showDropdown: boolean;            // Dropdown visibility state
  airports: Airport[];              // Available airports data
  onSearchTermChange: (term: string) => void;     // Search input handler
  onAirportSelect: (airport: string) => void;     // Airport selection handler
  onToggleDropdown: () => void;                   // Dropdown toggle handler
  dropdownRef: React.RefObject<HTMLDivElement>;   // Ref for click-outside detection
}
```

**Styling:** `className="w-full"` - Fills parent container width

### 2. MultiDatePicker Component
**File:** `MultiDatePicker.tsx`

**Purpose:** Renders departure and return date input fields

**Props Interface:**
```typescript
interface MultiDatePickerProps {
  selectedTripType: 'roundTrip' | 'oneWay' | 'multiCity';  // Trip type for conditional rendering
  selectedDates: { departure: string; return: string };    // Selected date values
  onDateClick: (dateType: 'departure' | 'return') => void; // Date field click handler
}
```

**Features:**
- Conditional return date rendering based on trip type
- Trip duration calculation and display
- Responsive layout with proper date formatting

### 3. TravelClassPicker Component
**File:** `TravelClassPicker.tsx`

**Purpose:** Renders travelers and class selection with dropdown

**Props Interface:**
```typescript
interface TravelClassPickerProps {
  passengers: { adults: number; children: number; infants: number };
  selectedClass: 'economy' | 'premiumEconomy' | 'business' | 'first';
  classOptions: ClassOption[];
  showDropdown: boolean;
  onToggleDropdown: () => void;
  onPassengerChange: (type: 'adults' | 'children' | 'infants', action: 'increase' | 'decrease') => void;
  onClassChange: (classId: string) => void;
  onApply: () => void;
  getTravelersClassText: () => string;
  dropdownRef: React.RefObject<HTMLDivElement>;
}
```

**Features:**
- Passenger count controls with validation
- Class selection grid
- Summary text generation

### 4. AdvancedSearch Component
**File:** `AdvancedSearch.tsx`

**Purpose:** Renders advanced search toggle button with filter count

**Props Interface:**
```typescript
interface AdvancedSearchProps {
  showAdvancedSearch: boolean;           // Expansion state
  onToggleAdvancedSearch: () => void;    // Toggle handler
  getAdvancedSearchCount: () => number;  // Active filter count
}
```

**Features:**
- Visual state indication (expanded/collapsed)
- Filter count badge
- Smooth transitions

### 5. CalendarModal Component
**File:** `CalendarModal.tsx`

**Purpose:** Renders the calendar modal content for date selection

**Key Features:**
- Dual-month calendar display
- Date range selection with visual indicators
- Price display per date
- Trip duration input for quick calculation
- Responsive modal layout

### 6. Supporting Components
- **AdvancedSearchContent:** Advanced search options content
- **TripTypeSelector:** Trip type selection buttons
- **RecentSearches:** Recent search history display
- **SearchButton:** Search action button

## Modal Management

### Modal State Centralization
All modal states are managed in the parent FlightSearchForm component:

```typescript
// Modal visibility states
const [showFromDropdown, setShowFromDropdown] = useState(false);
const [showToDropdown, setShowToDropdown] = useState(false);
const [showCalendar, setShowCalendar] = useState(false);
const [showTravelersClassDropdown, setShowTravelersClassDropdown] = useState(false);
```

### Auto-Scroll and Positioning
The parent component handles modal positioning and auto-scroll functionality:

```typescript
const scrollToOptimalPosition = (modalHeight: number = 700) => {
  // Intelligent modal positioning logic
  // Ensures modals are properly positioned within viewport
};
```

### Click-Outside Detection
Centralized click-outside handling for all modals using refs:

```typescript
useEffect(() => {
  const handleClickOutside = (event: MouseEvent) => {
    // Handle click-outside for all modal components
  };
  // Event listener management
}, [showCalendar]);
```

## Benefits of This Architecture

1. **Separation of Concerns:** State management is centralized, UI components focus on rendering
2. **Reusability:** Components can be easily reused in other contexts
3. **Maintainability:** Clear boundaries between logic and presentation
4. **Testability:** Components can be tested independently with mock props
5. **Consistency:** All components follow the same architectural patterns
6. **Performance:** Efficient re-rendering through proper prop passing
7. **Responsive Design:** Full-width components adapt to any container size

## Usage Example

```tsx
// In FlightSearchForm.tsx
<AirportSearch
  label="From"
  icon="fas fa-map-marker-alt"
  placeholder="City or airport"
  searchTerm={searchFromTerm || fromAirport}
  showDropdown={showFromDropdown}
  airports={popularAirports}
  onSearchTermChange={setSearchFromTerm}
  onAirportSelect={setFromAirport}
  onToggleDropdown={() => setShowFromDropdown(!showFromDropdown)}
  dropdownRef={fromDropdownRef}
/>
```

This architecture ensures that the FlightSearchForm component is well-organized, maintainable, and follows React best practices for component composition and state management.
