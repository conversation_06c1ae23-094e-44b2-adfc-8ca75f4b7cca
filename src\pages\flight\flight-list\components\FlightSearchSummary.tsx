import React, { useState, useEffect, useRef } from 'react';
import type { FlightSearchSummaryProps } from './types';
import { InlineEditComponents } from './index';
import ModifySearchForm from '../../../../components/flight/modify-search/ModifySearchForm';
import { FlightSearchProvider, useFlightSearch } from '../../../../contexts/FlightSearchContext';
import type { FlightSearchData } from '../../../../contexts/FlightSearchContext';

// Wrapper component for ModifySearchForm that initializes context
interface ModifySearchWrapperProps {
  isOpen: boolean;
  onClose: () => void;
  onSearch: (searchData: FlightSearchData) => void;
  currentSearchData: FlightSearchData;
}

const ModifySearchWrapper: React.FC<ModifySearchWrapperProps> = ({
  isOpen,
  onClose,
  onSearch,
  currentSearchData
}) => {
  // No need to update context here - it should already have the correct data

  return (
    <ModifySearchForm
      isOpen={isOpen}
      onClose={onClose}
      onSearch={onSearch}
      isModal={true}
    />
  );
};

const FlightSearchSummary: React.FC<FlightSearchSummaryProps> = ({
  fromAirport,
  toAirport,
  selectedDates,
  selectedClass,
  classOptions,
  onModifySearch,
  formatDate,
  getTotalPassengers,
  // New props for inline modify search
  showInlineModifySearch = false,
  onToggleInlineModifySearch,
  currentSearchData,
  onSearchUpdate
}) => {
  const [isScrolled, setIsScrolled] = useState(false);
  const [scrollProgress, setScrollProgress] = useState(0);

  // Inline editing states
  const [activeEditComponent, setActiveEditComponent] = useState<'airports' | 'dates' | 'passengers' | 'class' | null>(null);
  const editComponentRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const handleScroll = () => {
      const scrollTop = window.scrollY;
      const threshold = 80; // Increased threshold for smoother transition
      const isScrolledNow = scrollTop > threshold;

      // Calculate scroll progress for smooth animation (0 to 1)
      const progress = Math.min(scrollTop / threshold, 1);

      setIsScrolled(isScrolledNow);
      setScrollProgress(progress);
    };

    window.addEventListener('scroll', handleScroll, { passive: true });
    handleScroll(); // Check initial scroll position
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  // Close edit component when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (editComponentRef.current && !editComponentRef.current.contains(event.target as Node)) {
        setActiveEditComponent(null);
      }
    };

    if (activeEditComponent) {
      document.addEventListener('mousedown', handleClickOutside);
      return () => document.removeEventListener('mousedown', handleClickOutside);
    }
  }, [activeEditComponent]);

  // Handle inline edit component toggle
  const handleEditComponentToggle = (component: 'airports' | 'dates' | 'passengers' | 'class') => {
    setActiveEditComponent(activeEditComponent === component ? null : component);
  };

  return (
    <div
      className={`${
        isScrolled
          ? 'fixed top-0 left-0 right-0 z-[9999]'
          : 'relative z-10'
      } background-primary border-b border-primary transition-all duration-500 ease-out`}
      style={{
        boxShadow: `0 ${4 + scrollProgress * 16}px ${8 + scrollProgress * 32}px -${2 + scrollProgress * 8}px rgba(0, 0, 0, ${0.05 + scrollProgress * 0.15})`,
        transform: `translateY(${isScrolled ? 0 : scrollProgress * -2}px)`,
        borderBottomWidth: `${1 + scrollProgress * 1}px`,
        backdropFilter: `blur(${scrollProgress * 8}px) saturate(${1 + scrollProgress * 0.2})`,
        backgroundColor: `rgba(255, 255, 255, ${0.85 + scrollProgress * 0.15})`
      }}
    >
      <div className="max-w-[1920px] mx-auto px-4 lg:px-6 xl:px-8">
        <div
          className="flex items-center justify-between transition-all duration-500 ease-out"
          style={{
            paddingTop: `${8 - scrollProgress * 2}px`,
            paddingBottom: `${8 - scrollProgress * 2}px`,
            opacity: 0.85 + scrollProgress * 0.15
          }}
        >
          {/* Search Details - Professional Layout */}
          <div
            className="flex items-center space-x-4 transition-all duration-500 ease-out"
            style={{
              transform: `scale(${0.98 + scrollProgress * 0.02})`,
            }}
          >
            {/* Route Information - Clickable */}
            <div className="relative">
              <button
                onClick={() => handleEditComponentToggle('airports')}
                className="flex items-center neutral-background-50 px-3 py-2 rounded-lg border border-secondary hover-neutral-background-100 hover:border-primary transition-all duration-200 cursor-pointer focus:outline-none focus:ring-2 focus:ring-primary-color focus:ring-offset-1"
                title="Click to edit airports"
                aria-label="Edit departure and destination airports"
              >
                <div className="flex items-center">
                  <div className="flex items-center">
                    <i className="fas fa-plane-departure primary-text mr-2 text-sm"></i>
                    <span className="font-semibold text-primary text-sm truncate max-w-32">{fromAirport}</span>
                  </div>
                  <div className="mx-3 flex items-center">
                    <i className="fas fa-arrow-right text-tertiary text-xs"></i>
                  </div>
                  <div className="flex items-center">
                    <i className="fas fa-plane-arrival success-text mr-2 text-sm"></i>
                    <span className="font-semibold text-primary text-sm truncate max-w-32">{toAirport}</span>
                  </div>
                  <i className="fas fa-edit text-tertiary ml-2 text-xs"></i>
                </div>
              </button>
            </div>

            {/* Trip Details - Compact Professional Cards - Clickable */}
            <div className="hidden md:flex items-center space-x-2">
              {/* Date Card - Clickable */}
              <button
                onClick={() => handleEditComponentToggle('dates')}
                className="flex items-center bg-blue-50 px-2.5 py-1.5 rounded-lg border border-blue-200 hover:bg-blue-100 hover:border-blue-300 transition-all duration-200 cursor-pointer"
                title="Click to edit dates"
              >
                <i className="far fa-calendar-alt text-blue-600 mr-1.5 text-xs"></i>
                <span className="text-slate-700 text-xs font-medium">
                  {selectedDates.departure ? formatDate(selectedDates.departure) : 'Jun 20'}
                  {selectedDates.return && (
                    <span> - {formatDate(selectedDates.return)}</span>
                  )}
                </span>
                <i className="fas fa-edit text-blue-400 ml-1.5 text-xs"></i>
              </button>

              {/* Passengers Card - Clickable */}
              <button
                onClick={() => handleEditComponentToggle('passengers')}
                className="flex items-center bg-green-50 px-2.5 py-1.5 rounded-lg border border-green-200 hover:bg-green-100 hover:border-green-300 transition-all duration-200 cursor-pointer"
                title="Click to edit passengers"
              >
                <i className="fas fa-users text-green-600 mr-1.5 text-xs"></i>
                <span className="text-slate-700 text-xs font-medium">
                  {getTotalPassengers()} Traveler{getTotalPassengers() !== 1 ? 's' : ''}
                </span>
                <i className="fas fa-edit text-green-400 ml-1.5 text-xs"></i>
              </button>

              {/* Class Card - Clickable */}
              <button
                onClick={() => handleEditComponentToggle('class')}
                className="flex items-center bg-purple-50 px-2.5 py-1.5 rounded-lg border border-purple-200 hover:bg-purple-100 hover:border-purple-300 transition-all duration-200 cursor-pointer"
                title="Click to edit class"
              >
                <i className="fas fa-chair text-purple-600 mr-1.5 text-xs"></i>
                <span className="text-slate-700 text-xs font-medium">
                  {classOptions.find(option => option.id === selectedClass)?.label}
                </span>
                <i className="fas fa-edit text-purple-400 ml-1.5 text-xs"></i>
              </button>
            </div>
          </div>
          <div
            className="flex items-center space-x-2 transition-all duration-500 ease-out"
            style={{
              transform: `scale(${0.98 + scrollProgress * 0.02})`,
            }}
          >
            {/* Professional Modify Search Toggle */}
            <button
              className={`flex items-center px-3 py-2 text-xs font-semibold transition-all duration-200 cursor-pointer whitespace-nowrap rounded-lg border-2 ${
                showInlineModifySearch
                  ? 'bg-slate-800 text-white border-slate-800 hover:bg-slate-900 shadow-lg transform hover:scale-105'
                  : 'bg-blue-600 text-white border-blue-600 hover:border-blue-700 hover:bg-blue-700 shadow-sm'
              }`}
              onClick={onToggleInlineModifySearch || onModifySearch}
              title={showInlineModifySearch ? 'Close modify search' : 'Modify search parameters'}
            >
              <i className={`fas ${showInlineModifySearch ? 'fa-times' : 'fa-edit'} mr-2 text-xs`}></i>
              <span className="font-medium">
                {showInlineModifySearch ? 'Close' : 'Modify Search'}
              </span>
            </button>

            {/* Quick Actions - Professional Icons */}
            <div className="hidden lg:flex items-center space-x-1">
              <button
                className="p-2 text-slate-400 hover:text-slate-600 hover:bg-slate-100 rounded-lg transition-all duration-200"
                title="Swap airports"
                onClick={() => {
                  // This could trigger a swap action if needed
                }}
              >
                <i className="fas fa-exchange-alt text-xs"></i>
              </button>
              <button
                className="p-2 text-slate-400 hover:text-slate-600 hover:bg-slate-100 rounded-lg transition-all duration-200"
                title="Save search"
                onClick={() => {
                  // This could trigger a save action if needed
                }}
              >
                <i className="fas fa-bookmark text-xs"></i>
              </button>
              <button
                className="p-2 text-slate-400 hover:text-slate-600 hover:bg-slate-100 rounded-lg transition-all duration-200"
                title="Share search"
                onClick={() => {
                  // This could trigger a share action if needed
                }}
              >
                <i className="fas fa-share-alt text-xs"></i>
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Modify Search Component */}
      {onToggleInlineModifySearch && currentSearchData && onSearchUpdate && (
        <FlightSearchProvider>
          <ModifySearchWrapper
            isOpen={showInlineModifySearch}
            onClose={onToggleInlineModifySearch}
            onSearch={onSearchUpdate}
            currentSearchData={currentSearchData}
          />
        </FlightSearchProvider>
      )}

      {/* Inline Edit Components */}
      {activeEditComponent && currentSearchData && onSearchUpdate && (
        <div ref={editComponentRef}>
          <InlineEditComponents
            activeComponent={activeEditComponent}
            currentSearchData={currentSearchData}
            onSearchUpdate={onSearchUpdate}
            onClose={() => setActiveEditComponent(null)}
            classOptions={classOptions}
            formatDate={formatDate}
          />
        </div>
      )}
    </div>
  );
};

export default FlightSearchSummary;
