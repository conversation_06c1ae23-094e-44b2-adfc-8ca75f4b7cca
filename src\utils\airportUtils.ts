import type { Airport } from '../contexts/FlightSearchContext';

export interface AirportData {
  code: string;
  city: string;
  name: string;
  country: string;
}

/**
 * Parse airport string format "City (CODE)" to Airport object
 * @param airportString - String in format "New York (JFK)" or "London (LHR)"
 * @param airportData - Array of airport data to match against
 * @returns Airport object with proper structure
 */
export const parseAirportString = (airportString: string, airportData: AirportData[]): Airport => {
  // Default empty airport
  const defaultAirport: Airport = {
    city: '',
    airport: '',
    iata: '',
    country: '',
    airportOpen: false,
  };

  if (!airportString || airportString.trim() === '') {
    return defaultAirport;
  }

  // Extract city and IATA code from string like "New York (JFK)"
  const match = airportString.match(/^(.+?)\s*\(([A-Z]{3})\)$/);
  
  if (!match) {
    // If format doesn't match, try to find by partial match
    const foundAirport = airportData.find(airport => 
      airport.city.toLowerCase().includes(airportString.toLowerCase()) ||
      airport.code.toLowerCase().includes(airportString.toLowerCase()) ||
      airport.name.toLowerCase().includes(airportString.toLowerCase())
    );

    if (foundAirport) {
      return {
        city: foundAirport.city,
        airport: foundAirport.name,
        iata: foundAirport.code,
        country: foundAirport.country,
        airportOpen: false,
      };
    }

    return defaultAirport;
  }

  const [, cityName, iataCode] = match;
  
  // Find matching airport in data
  const foundAirport = airportData.find(airport => 
    airport.code === iataCode || 
    (airport.city.toLowerCase() === cityName.toLowerCase() && airport.code === iataCode)
  );

  if (foundAirport) {
    return {
      city: foundAirport.city,
      airport: foundAirport.name,
      iata: foundAirport.code,
      country: foundAirport.country,
      airportOpen: false,
    };
  }

  // If not found in data, create from parsed string
  return {
    city: cityName.trim(),
    airport: `${cityName.trim()} Airport`, // Fallback airport name
    iata: iataCode,
    country: 'Unknown', // Will need to be updated when proper data is available
    airportOpen: false,
  };
};

/**
 * Format Airport object to display string
 * @param airport - Airport object
 * @returns Formatted string like "New York (JFK)"
 */
export const formatAirportDisplay = (airport: Airport): string => {
  if (!airport.city || !airport.iata) {
    return '';
  }
  return `${airport.city} (${airport.iata})`;
};

/**
 * Validate if airport data is complete
 * @param airport - Airport object to validate
 * @returns boolean indicating if airport is valid
 */
export const isValidAirport = (airport: Airport): boolean => {
  return !!(
    airport.city &&
    airport.airport &&
    airport.iata &&
    airport.country &&
    airport.iata.length === 3 &&
    /^[A-Z]{3}$/.test(airport.iata)
  );
};

/**
 * Create airport object from individual components
 * @param city - City name
 * @param airportName - Full airport name
 * @param iata - IATA code
 * @param country - Country name
 * @returns Airport object
 */
export const createAirport = (
  city: string,
  airportName: string,
  iata: string,
  country: string
): Airport => {
  return {
    city: city.trim(),
    airport: airportName.trim(),
    iata: iata.toUpperCase().trim(),
    country: country.trim(),
    airportOpen: false,
  };
};

/**
 * Get airport suggestions based on search term
 * @param searchTerm - Search input
 * @param airportData - Array of available airports
 * @param limit - Maximum number of suggestions
 * @returns Array of matching airports
 */
export const getAirportSuggestions = (
  searchTerm: string,
  airportData: AirportData[],
  limit: number = 10
): AirportData[] => {
  if (!searchTerm || searchTerm.length < 2) {
    return airportData.slice(0, limit);
  }

  const term = searchTerm.toLowerCase();
  
  // Priority scoring: exact matches first, then starts with, then contains
  const scored = airportData.map(airport => {
    let score = 0;
    
    // Exact matches get highest score
    if (airport.code.toLowerCase() === term) score += 100;
    if (airport.city.toLowerCase() === term) score += 90;
    
    // Starts with matches
    if (airport.code.toLowerCase().startsWith(term)) score += 80;
    if (airport.city.toLowerCase().startsWith(term)) score += 70;
    if (airport.name.toLowerCase().startsWith(term)) score += 60;
    
    // Contains matches
    if (airport.code.toLowerCase().includes(term)) score += 40;
    if (airport.city.toLowerCase().includes(term)) score += 30;
    if (airport.name.toLowerCase().includes(term)) score += 20;
    if (airport.country.toLowerCase().includes(term)) score += 10;
    
    return { airport, score };
  });

  return scored
    .filter(item => item.score > 0)
    .sort((a, b) => b.score - a.score)
    .slice(0, limit)
    .map(item => item.airport);
};

/**
 * Convert old format search data to new format
 * @param oldData - Old format flight search data
 * @param airportData - Airport data for conversion
 * @returns New format flight search data
 */
export const convertLegacySearchData = (oldData: any, airportData: AirportData[]) => {
  if (!oldData) return null;

  try {
    return {
      from: parseAirportString(oldData.fromAirport || '', airportData),
      to: parseAirportString(oldData.toAirport || '', airportData),
      depart: new Date(oldData.selectedDates?.departure || oldData.departureDate || new Date()),
      return: oldData.selectedDates?.return || oldData.returnDate 
        ? new Date(oldData.selectedDates?.return || oldData.returnDate) 
        : null,
      tripType: oldData.selectedTripType || oldData.tripType || 'roundTrip',
      passengers: oldData.passengers || { adults: 1, children: 0, infants: 0 },
      class: oldData.selectedClass || oldData.class || 'economy',
      advanced_search: {
        selectedAirlines: [],
        flightOptions: {
          directFlights: false,
          refundableFares: false,
          corporateRates: false,
        },
        services: {
          airportLounge: false,
          extraBaggage: false,
          travelInsurance: false,
        },
        stops: 'any',
        baggage: {
          carryOn: false,
          checked: false,
        },
      },
    };
  } catch (error) {
    console.error('Error converting legacy search data:', error);
    return null;
  }
};
