import React from 'react';
import type { FlightCardProps } from './types';
import { formatFlightPrice, formatSavings } from '../../../../utils/priceUtils';

const FlightCard: React.FC<FlightCardProps> = ({
  flight,
  onFlightSelect,
  onShowDetails,
  showPriceShimmer = false,
  isSelected = false,
  hideDetailsButton = false
}) => {
  return (
    <div className={`relative group flight-card-background rounded-xl shadow-sm hover:shadow-lg transition-all duration-300 border overflow-hidden ${
      isSelected
        ? 'ring-2 ring-blue-500 ring-offset-2 border-blue-500 shadow-lg'
        : 'flight-card-border hover-flight-card-hover-border'
    }`}>
      {/* Corporate Rate Badge */}
      {flight.corporateRate && (
        <div className="gradient-corporate-rate text-inverse text-xs font-medium px-4 py-1.5 rounded-t-xl flex items-center justify-between">
          <div className="flex items-center">
            <i className="fas fa-briefcase mr-1.5 text-xs"></i>
            Corporate Rate
          </div>
          <div className="flex items-center text-xs">
            <i className="fas fa-badge-check mr-1 text-xs"></i>
            Best Value
          </div>
        </div>
      )}

      <div className="p-3 lg:p-4">
        {/* Airline Info and Pricing - Compact Layout */}
        <div className="flex items-center justify-between mb-3">
          <div className="flex items-center flex-1 min-w-0">
            <i className="fas fa-plane text-lg text-tertiary mr-3 flex-shrink-0"></i>
            <div className="flex-1 min-w-0">
              <h3 className="font-semibold text-primary text-sm lg:text-base truncate">{flight.airline}</h3>
              <p className="text-xs text-secondary truncate">{flight.alliance} • Flight {flight.id}</p>
            </div>
          </div>
          <div className="text-right flex-shrink-0 ml-4">
            {showPriceShimmer ? (
              // Show shimmer for price when sh_price is false
              <div className="animate-pulse">
                <div className="h-3 bg-gray-200 rounded w-16 mb-1"></div>
                <div className="h-6 bg-gray-200 rounded w-20 mb-1"></div>
                <div className="h-3 bg-gray-200 rounded w-14"></div>
              </div>
            ) : (
              // Show actual price when sh_price is true
              <>
                <p className="text-xs text-secondary line-through">{formatFlightPrice(flight.originalPrice)}</p>
                <p className="text-lg lg:text-xl font-bold text-primary">{formatFlightPrice(flight.price)}</p>
                <p className="text-xs success-text">{formatSavings(flight.originalPrice, flight.price)}</p>
              </>
            )}
          </div>
        </div>

        {/* Flight Details - Compact Layout */}
        <div className="grid grid-cols-3 gap-3 lg:gap-4 mb-3 items-center">
          {/* Departure */}
          <div className="text-left">
            <div className="text-base lg:text-lg font-semibold text-primary">{flight.departure.time}</div>
            <div className="text-xs lg:text-sm text-secondary font-medium truncate">{flight.departure.city}</div>
            <div className="text-xs text-tertiary">Terminal {flight.departure.terminal}</div>
          </div>

          {/* Duration and Stops */}
          <div className="text-center">
            <div className="text-xs lg:text-sm font-medium text-secondary mb-1">{flight.duration}</div>
            <div className="relative px-2">
              <div className="absolute w-full top-1/2 border-t border-secondary border-dashed left-0"></div>
              <div className="relative z-10 flex items-center justify-center">
                {flight.stops === 0 ? (
                  <span className="background-primary text-xs font-medium primary-text px-2 py-0.5 rounded-full border primary-border">
                    Direct
                  </span>
                ) : (
                  <span className="background-primary text-xs font-medium text-secondary px-2 py-0.5 rounded-full border border-secondary">
                    {flight.stops} stop{flight.stops > 1 ? 's' : ''}
                  </span>
                )}
              </div>
            </div>
          </div>

          {/* Arrival */}
          <div className="text-right">
            <div className="text-base lg:text-lg font-semibold text-primary">{flight.arrival.time}</div>
            <div className="text-xs lg:text-sm text-secondary font-medium truncate">{flight.arrival.city}</div>
            <div className="text-xs text-tertiary">Terminal {flight.arrival.terminal}</div>
          </div>
        </div>

        {/* Flight Features - Compact Layout */}
        <div className="flex flex-wrap gap-1.5 lg:gap-2 mb-3">
          <div className="flex items-center neutral-background-50 group-hover:primary-background-lightest px-2 py-1 rounded transition-colors duration-200">
            <i className="fas fa-plane text-tertiary group-hover:primary-text transition-colors mr-1 text-xs"></i>
            <span className="text-xs text-secondary group-hover:primary-text transition-colors">{flight.aircraft}</span>
          </div>
          <div className="flex items-center neutral-background-50 px-2 py-1 rounded">
            <i className="fas fa-suitcase text-tertiary mr-1 text-xs"></i>
            <span className="text-xs text-secondary">{flight.baggageAllowance}</span>
          </div>
          <div className="flex items-center neutral-background-50 px-2 py-1 rounded">
            <i className="fas fa-leaf success-text mr-1 text-xs"></i>
            <span className="text-xs text-secondary">{flight.carbonEmission}</span>
          </div>
          {flight.amenities.slice(0, 2).map((amenity, index) => (
            <div key={index} className="flex items-center neutral-background-50 px-2 py-1 rounded">
              <i className="fas fa-check primary-text mr-1 text-xs"></i>
              <span className="text-xs text-secondary">{amenity}</span>
            </div>
          ))}
          {flight.amenities.length > 2 && (
            <div className="flex items-center neutral-background-50 px-2 py-1 rounded">
              <span className="text-xs text-secondary">+{flight.amenities.length - 2} more</span>
            </div>
          )}
        </div>

        {/* Action Buttons - Compact Layout */}
        <div className="flex items-center justify-between pt-3 border-t border-primary">
          <div className="flex items-center space-x-3 text-xs text-secondary">
            <div className="flex items-center">
              <i className="fas fa-users primary-text mr-1 text-xs"></i>
              <span className="font-medium primary-text">{flight.seatsAvailable}</span>
              <span className="ml-1">left</span>
            </div>
            <div className="hidden lg:flex items-center">
              <i className="fas fa-tags success-text mr-1 text-xs"></i>
              <span className="success-text font-medium text-xs">{flight.fareTypes[0]}</span>
            </div>
          </div>
          <div className="flex space-x-2">
            {!hideDetailsButton && (
              <button
                className="!rounded-button neutral-background-100 text-secondary px-3 py-1.5 text-xs font-medium hover-neutral-background-200 transition-colors duration-200 cursor-pointer whitespace-nowrap focus:outline-none focus:ring-2 focus:ring-primary-color focus:ring-offset-1"
                onClick={() => onShowDetails(flight)}
                aria-label={`View details for ${flight.airline} flight ${flight.id}`}
              >
                <i className="fas fa-info-circle mr-1"></i>
                Details
              </button>
            )}
            <button
              className={`!rounded-button px-4 py-1.5 text-xs font-medium transition-colors duration-200 cursor-pointer whitespace-nowrap focus:outline-none focus:ring-2 focus:ring-offset-1 ${
                isSelected
                  ? 'bg-green-600 text-white hover:bg-green-700 focus:ring-green-500'
                  : 'primary-background text-inverse hover-primary-background-dark focus:ring-primary-color'
              }`}
              onClick={() => onFlightSelect(flight)}
              aria-label={`${isSelected ? 'Selected' : 'Select'} ${flight.airline} flight ${flight.id} for $${flight.price}`}
            >
              <i className={`fas ${isSelected ? 'fa-check-circle' : 'fa-check'} mr-1`}></i>
              {isSelected ? 'Selected' : 'Select'}
            </button>
          </div>
        </div>

        {/* Selected Badge */}
        {isSelected && (
          <div className="absolute top-3 right-3 bg-blue-500 text-white rounded-full p-1.5 shadow-lg">
            <i className="fas fa-check text-xs"></i>
          </div>
        )}
      </div>
    </div>
  );
};

export default FlightCard;
