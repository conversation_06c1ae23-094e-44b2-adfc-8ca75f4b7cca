import React, { useState } from 'react';
import ModifySearchForm from './ModifySearchForm';
import { useFlightSearch } from '../../../contexts/FlightSearchContext';
import { formatAirportDisplay } from '../../../utils/airportUtils';

interface ModifySearchButtonProps {
    onSearch?: (searchData: any) => void;
    className?: string;
    variant?: 'button' | 'inline';
}

const ModifySearchButton: React.FC<ModifySearchButtonProps> = ({ 
    onSearch,
    className = '',
    variant = 'button'
}) => {
    const [showModal, setShowModal] = useState(false);
    const { currentSearch } = useFlightSearch();

    const handleSearch = (searchData: any) => {
        if (onSearch) {
            onSearch(searchData);
        }
        setShowModal(false);
    };

    // Get current search summary for display
    const fromAirport = currentSearch?.from ? formatAirportDisplay(currentSearch.from) : '';
    const toAirport = currentSearch?.to ? formatAirportDisplay(currentSearch.to) : '';
    const departureDate = currentSearch?.depart ? currentSearch.depart.toLocaleDateString('en-US', { 
        weekday: 'short', 
        month: 'short', 
        day: 'numeric' 
    }) : '';
    const returnDate = currentSearch?.return ? currentSearch.return.toLocaleDateString('en-US', { 
        weekday: 'short', 
        month: 'short', 
        day: 'numeric' 
    }) : '';
    const passengers = currentSearch?.passengers || { adults: 1, children: 0, infants: 0 };
    const totalPassengers = passengers.adults + passengers.children + passengers.infants;
    const selectedClass = currentSearch?.class || 'economy';

    // Class display mapping
    const classDisplayMap = {
        economy: 'Economy',
        premiumEconomy: 'Premium Economy',
        business: 'Business',
        first: 'First'
    };

    if (variant === 'inline') {
        return (
            <>
                <div className={`modify-search-inline ${className}`}>
                    {/* Current Search Summary */}
                    <div className="flex items-center gap-4 p-4 bg-white rounded-lg shadow-sm border">
                        {/* Route */}
                        <div className="flex items-center gap-2">
                            <i className="fas fa-plane-departure text-blue-600"></i>
                            <span className="font-medium text-gray-800">{fromAirport}</span>
                            <i className="fas fa-arrow-right text-gray-400"></i>
                            <i className="fas fa-plane-arrival text-green-600"></i>
                            <span className="font-medium text-gray-800">{toAirport}</span>
                        </div>

                        {/* Dates */}
                        <div className="flex items-center gap-2">
                            <i className="fas fa-calendar text-purple-600"></i>
                            <span className="text-gray-700">
                                {departureDate && returnDate ? `${departureDate} - ${returnDate}` : 
                                 departureDate ? departureDate : 'Select Dates'}
                            </span>
                        </div>

                        {/* Passengers */}
                        <div className="flex items-center gap-2">
                            <i className="fas fa-user text-orange-600"></i>
                            <span className="text-gray-700">
                                {totalPassengers} Traveler{totalPassengers > 1 ? 's' : ''}
                            </span>
                        </div>

                        {/* Class */}
                        <div className="flex items-center gap-2">
                            <i className="fas fa-crown text-gray-600"></i>
                            <span className="text-gray-700">{classDisplayMap[selectedClass]}</span>
                        </div>

                        {/* Modify Button */}
                        <button
                            onClick={() => setShowModal(true)}
                            className="ml-auto flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                        >
                            <i className="fas fa-edit"></i>
                            <span>Modify Search</span>
                        </button>
                    </div>
                </div>

                {/* Modal */}
                {showModal && (
                    <ModifySearchForm
                        onSearch={handleSearch}
                        onClose={() => setShowModal(false)}
                        isModal={true}
                    />
                )}
            </>
        );
    }

    return (
        <>
            <button
                onClick={() => setShowModal(true)}
                className={`modify-search-button ${className}`}
            >
                <i className="fas fa-edit mr-2"></i>
                Modify Search
            </button>

            {showModal && (
                <ModifySearchForm
                    onSearch={handleSearch}
                    onClose={() => setShowModal(false)}
                    isModal={true}
                />
            )}
        </>
    );
};

export default ModifySearchButton;
