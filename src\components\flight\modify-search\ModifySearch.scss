// Keyframe animations
@keyframes dropdownFadeIn {
  from {
    opacity: 0;
    transform: translateY(-8px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

@keyframes overlayFadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

// Modify Search Form Styles
.modify-search-form {
  // Remove any default margins when used inline
  &:not(.modal-content) {
    margin: 0;
    padding: 0;
  }

  .search-fields-container {
    display: flex;
    align-items: center;
    gap: 8px;
    flex-wrap: wrap;

    @media (max-width: 768px) {
      flex-direction: column;
      align-items: stretch;
      gap: 12px;
    }
  }

  .search-field-button {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px 12px;
    border-radius: 8px;
    border: 1px solid;
    transition: all 0.2s ease;
    cursor: pointer;
    font-size: 14px;
    font-weight: 500;
    white-space: nowrap;

    &:hover {
      transform: translateY(-1px);
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }

    .icon {
      font-size: 14px;
    }

    .chevron {
      font-size: 10px;
      opacity: 0.6;
    }

    // Color variants
    &.from-airport {
      background-color: #eff6ff;
      border-color: #bfdbfe;
      color: #1e40af;

      &:hover {
        background-color: #dbeafe;
      }

      .icon {
        color: #2563eb;
      }
    }

    &.to-airport {
      background-color: #f0fdf4;
      border-color: #bbf7d0;
      color: #166534;

      &:hover {
        background-color: #dcfce7;
      }

      .icon {
        color: #16a34a;
      }
    }

    &.date-selection {
      background-color: #faf5ff;
      border-color: #d8b4fe;
      color: #7c3aed;

      &:hover {
        background-color: #f3e8ff;
      }

      .icon {
        color: #8b5cf6;
      }
    }

    &.travelers-class {
      background-color: #fff7ed;
      border-color: #fed7aa;
      color: #c2410c;

      &:hover {
        background-color: #ffedd5;
      }

      .icon {
        color: #ea580c;
      }
    }

    &.class-display {
      background-color: #f9fafb;
      border-color: #d1d5db;
      color: #374151;
      cursor: pointer;

      &:hover {
        background-color: #f3f4f6;
        transform: translateY(-1px);
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      }

      .icon {
        color: #6b7280;
      }
    }

    // Input field styling when used as search
    &[type="text"] {
      padding-left: 40px;
      padding-right: 40px;
      background-color: white;

      &:focus {
        outline: none;
        border-color: #3b82f6;
        box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
      }

      &[readonly] {
        cursor: pointer;

        &:focus {
          box-shadow: none;
        }
      }

      &::placeholder {
        color: #9ca3af;
      }
    }
  }

  .swap-button {
    padding: 8px;
    color: #6b7280;
    transition: color 0.2s ease;
    cursor: pointer;

    &:hover {
      color: #2563eb;
    }
  }

  .modify-search-button {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px 16px;
    background-color: #2563eb;
    color: white;
    border-radius: 8px;
    border: none;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: background-color 0.2s ease;
    margin-left: auto;

    &:hover {
      background-color: #1d4ed8;
    }

    @media (max-width: 768px) {
      margin-left: 0;
      justify-content: center;
    }
  }
}

// Modify Search Button Styles
.modify-search-button {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  padding: 10px 20px;
  background-color: #2563eb;
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;

  &:hover {
    background-color: #1d4ed8;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(37, 99, 235, 0.3);
  }

  &:active {
    transform: translateY(0);
  }
}

// Inline Modify Search Styles
.modify-search-inline {
  .current-search-summary {
    display: flex;
    align-items: center;
    gap: 16px;
    padding: 16px;
    background-color: white;
    border-radius: 8px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    border: 1px solid #e5e7eb;

    @media (max-width: 768px) {
      flex-direction: column;
      align-items: stretch;
      gap: 12px;
    }

    .search-info-item {
      display: flex;
      align-items: center;
      gap: 8px;
      font-size: 14px;

      .icon {
        font-size: 14px;
      }

      .label {
        font-weight: 500;
        color: #374151;
      }

      .value {
        color: #6b7280;
      }
    }

    .route-info {
      display: flex;
      align-items: center;
      gap: 8px;

      .airport {
        font-weight: 500;
        color: #374151;
      }

      .arrow {
        color: #9ca3af;
        font-size: 12px;
      }
    }
  }
}

// Dropdown positioning
.dropdown-container {
  position: relative;

  // Add subtle background overlay when dropdown is open (no blur)
  &:has(.dropdown-menu) {
    &::before {
      content: '';
      position: fixed;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background-color: rgba(0, 0, 0, 0.02);
      z-index: 999;
      pointer-events: none;
      animation: overlayFadeIn 0.2s ease-out;
    }
  }

  .dropdown-menu {
    position: absolute;
    top: 100%;
    left: 0;
    margin-top: 4px;
    background-color: white;
    border-radius: 12px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15), 0 8px 16px rgba(0, 0, 0, 0.1);
    z-index: 1050; // Higher than modal overlay (1000)
    max-height: 320px;
    overflow-y: auto;
    min-width: 320px;
    border: 1px solid rgba(0, 0, 0, 0.08);

    // Smooth animations
    animation: dropdownFadeIn 0.2s ease-out;
    transform-origin: top;

    // Subtle inner shadow for depth
    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      height: 1px;
      background: linear-gradient(90deg, transparent, rgba(0, 0, 0, 0.05), transparent);
    }

    // Airport dropdown styling
    &.airport-dropdown {
      background-color: white !important;
      box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15), 0 8px 16px rgba(0, 0, 0, 0.1) !important;
      border: 1px solid rgba(0, 0, 0, 0.08) !important;
      border-radius: 12px !important;
      min-width: 300px !important;
      max-width: 400px !important;
      max-height: 400px !important;
      height: auto !important;
      animation: dropdownFadeIn 0.2s ease-out !important;

      // Add subtle inner highlight
      &::after {
        content: '';
        position: absolute;
        top: 1px;
        left: 1px;
        right: 1px;
        height: 1px;
        background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.6), transparent);
        border-radius: 12px 12px 0 0;
      }


    }

    &.calendar-dropdown {
      // Increased calendar sizing for better visibility
      width: 100% !important;
      min-width: 700px !important;
      max-width: 900px !important;
      max-height: 550px !important;
      height: auto !important;
      overflow: visible !important;

      // Absolute positioning to not affect layout flow
      position: absolute !important;
      top: 100% !important;
      left: 50% !important;
      transform: translateX(-50%) !important;
      margin-top: 4px !important;
      z-index: 1000 !important;

      // Enhanced styling
      background-color: white !important;
      box-shadow: 0 25px 50px rgba(0, 0, 0, 0.2), 0 12px 24px rgba(0, 0, 0, 0.15) !important;
      border: 1px solid rgba(0, 0, 0, 0.08) !important;
      border-radius: 16px !important;
      animation: dropdownFadeIn 0.2s ease-out !important;

      // Add subtle inner glow
      &::after {
        content: '';
        position: absolute;
        top: 1px;
        left: 1px;
        right: 1px;
        height: 2px;
        background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.8), transparent);
        border-radius: 16px 16px 0 0;
      }

      // Prevent any layout interference
      display: block !important;

      // Set CSS variable for CalendarModal height
      --calendar-dropdown-max-height: 650px;

      @media (max-width: 768px) {
        left: 0 !important;
        transform: none !important;
        right: 0 !important;
        width: calc(100vw - 32px) !important;
        min-width: 320px !important;
        max-width: calc(100vw - 32px) !important;
        max-height: calc(100vh - var(--calendar-dropdown-top, 150px) - 1rem) !important;
      }

      // Ensure calendar content fills the space
      > div {
        width: 100% !important;
        max-width: 100% !important;
      }
    }

    &.travelers-dropdown {
      // Position for travel class picker modal
      position: absolute !important;
      top: 100% !important;
      right: 0 !important;
      margin-top: 4px !important;
      z-index: 1050 !important;
      min-width: 380px !important;
      max-width: 480px !important;
      max-height: 500px !important;
      height: auto !important;
      overflow: visible !important;
      background: white !important;

      // Set CSS variable for TravelClassPicker height
      --dropdown-max-height: 480px;
      border-radius: 16px !important;
      box-shadow: 0 25px 50px rgba(0, 0, 0, 0.2), 0 12px 24px rgba(0, 0, 0, 0.15) !important;
      border: 1px solid rgba(0, 0, 0, 0.08) !important;
      animation: dropdownFadeIn 0.2s ease-out !important;

      // Add subtle inner glow
      &::after {
        content: '';
        position: absolute;
        top: 1px;
        left: 1px;
        right: 1px;
        height: 2px;
        background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.8), transparent);
        border-radius: 16px 16px 0 0;
      }

      @media (max-width: 768px) {
        left: 0 !important;
        right: auto !important;
        min-width: 320px !important;
        max-width: 360px !important;
      }
    }
  }
}

// Modal overlay
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 16px;

  .modal-content {
    background-color: white;
    border-radius: 12px;
    padding: 24px;
    max-width: 1024px;
    width: 100%;
    max-height: 90vh;
    overflow-y: auto;
    box-shadow: 0 25px 50px rgba(0, 0, 0, 0.25);

    .modal-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 24px;

      .modal-title {
        font-size: 20px;
        font-weight: 600;
        color: #111827;
        margin: 0;
      }

      .close-button {
        color: #9ca3af;
        background: none;
        border: none;
        font-size: 18px;
        cursor: pointer;
        padding: 4px;
        transition: color 0.2s ease;

        &:hover {
          color: #6b7280;
        }
      }
    }
  }
}

// Responsive adjustments
@media (max-width: 640px) {
  .modify-search-form {
    .search-fields-container {
      .search-field-button {
        flex: 1;
        justify-content: center;
      }

      .modify-search-button {
        margin-left: 0;
      }
    }
  }

  .modal-overlay {
    padding: 8px;

    .modal-content {
      padding: 16px;
      border-radius: 8px;
    }
  }
}
