import React, { useEffect } from 'react';
import './TravelClassPicker.scss';
import type { ClassType, passengers, TravellerType } from '../../../../../models/flight/flight-search.model';

interface ClassOption {
  id: string;
  label: string;
}

interface TravelClassPickerProps {
  passengers: passengers;
  selectedClass: ClassType;
  classOptions: ClassOption[];
  showDropdown: boolean;
  onPassengerChange: (type: TravellerType, value: number) => void;
  onClassChange: (classId: ClassType) => void;
  onApply: () => void;
}

const TravelClassPicker: React.FC<TravelClassPickerProps> = ({
  passengers,
  selectedClass,
  classOptions,
  showDropdown,
  onPassengerChange,
  onClassChange,
  onApply,
}) => {
  // Dynamic height calculation moved to parent component
  useEffect(() => {
    if (showDropdown) {
      const updateDropdownHeight = () => {
        const parentElement = document.querySelector('[data-travelers-dropdown]');
        if (parentElement) {
          const rect = parentElement.getBoundingClientRect();
          const spaceBelow = window.innerHeight - rect.bottom - 20;
          const dropdownTop = rect.bottom + window.scrollY;

          document.documentElement.style.setProperty('--dropdown-top', `${dropdownTop}px`);

          if (spaceBelow < 450) {
            const maxHeight = Math.max(200, spaceBelow);
            document.documentElement.style.setProperty('--dropdown-max-height', `${maxHeight}px`);
          } else {
            document.documentElement.style.setProperty('--dropdown-max-height', '450px');
          }
        }
      };

      setTimeout(updateDropdownHeight, 10);
      window.addEventListener('resize', updateDropdownHeight);
      window.addEventListener('scroll', updateDropdownHeight);

      return () => {
        window.removeEventListener('resize', updateDropdownHeight);
        window.removeEventListener('scroll', updateDropdownHeight);
      };
    }
  }, [showDropdown]);

  if (!showDropdown) return null;

  const renderNumberOptions = (
    value: number,
    type: TravellerType,
    maxAllowed: number,
    isAdults: boolean = false
  ) => {
    const startNumber = isAdults ? 1 : 0;
    let gridLength: number;

    if (type === 'adult') {
      gridLength = 9; // Renders 1-9
    } else if (type === 'child') {
      gridLength = 9; // Renders 0-8
    } else {
      gridLength = 5; // Renders 0-4 for infants
    }

    return (
      <div className="number-grid">
        {Array.from({ length: gridLength }, (_, i) => {
          const number = startNumber + i;
          return (
            <button
              key={number}
              type="button"
              className={`
                number-option
                ${value === number ? 'selected' : ''}
                ${number > maxAllowed ? 'disabled' : ''}
              `}
              onClick={() => {
                if (number <= maxAllowed) {
                  onPassengerChange(type, number);
                }
              }}
              disabled={number > maxAllowed}
            >
              {number}
            </button>
          );
        })}
      </div>
    );
  };

  return (
    <div className="bg-white rounded-xl shadow-xl border border-gray-200 w-full traveller-selection">
      <div className="modal-container">
        {/* Title */}
        <div className="mb-4">
          <h3 className="modal-title">Travellers</h3>
        </div>

        {/* Adults */}
        <div className="traveller-section">
          <div className="section-header">
            <p className="section-title">
              Adults <span className="section-subtitle">12 yrs or above</span>
            </p>
          </div>
          {renderNumberOptions(
            passengers.adult,
            'adult',
            9 - passengers.child - passengers.infant,
            true
          )}
        </div>

        {/* Children */}
        <div className="traveller-section">
          <div className="section-header">
            <p className="section-title">
              Children <span className="section-subtitle">2 - 12 Years</span>
            </p>
          </div>
          {renderNumberOptions(
            passengers.child,
            'child',
            Math.min(8, 9 - passengers.adult - passengers.infant)
          )}
        </div>

        {/* Infants */}
        <div className="traveller-section">
          <div className="section-header">
            <p className="section-title">
              Infants <span className="section-subtitle">0 - 2 Years</span>
            </p>
          </div>
          {renderNumberOptions(
            passengers.infant,
            'infant',
            Math.min(4, passengers.adult, 9 - passengers.adult - passengers.child)
          )}
        </div>

        {/* Group Booking */}
        <div className="group-booking-notice">
          <p>
            Planning a trip for <strong>more than 9 travellers?</strong>
          </p>
          <a href="#" onClick={(e) => e.preventDefault()}>
            Create Group Booking
          </a>
        </div>

        {/* Class Selection */}
        <div className="mb-4">
          <h4 className="section-title">Class</h4>
          <div className="class-selection-container">
            {classOptions.map((cls) => (
              <button
                key={cls.id}
                type="button"
                className={`class-selection-item ${selectedClass === cls.id ? 'active' : ''}`}
                onClick={() => onClassChange(cls.id as ClassType)}
              >
                {cls.label}
              </button>
            ))}
          </div>
        </div>

        {/* Apply Button */}
        <button type="button" className="apply-button" onClick={onApply}>
          Done
        </button>
      </div>
    </div>
  );
};

export default TravelClassPicker;
