import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import '@testing-library/jest-dom';
import {
  AirlineIcon,
  FlightDurationChart,
  MultiCityFlightCard,
  SegmentHeader,
  TripSummary,
  type MultiCityFlight,
  type MultiCitySegment
} from '../index';

// Mock data for testing
const mockFlight: MultiCityFlight = {
  id: 'test-flight-1',
  airline: { name: 'Test Airlines', code: 'TA' },
  flightNumber: 'TA123',
  departure: {
    city: 'New York',
    code: 'JFK',
    time: '10:00',
    date: 'July 2, 2025',
    terminal: 'T4',
  },
  arrival: {
    city: 'London',
    code: 'LHR',
    time: '22:00',
    date: 'July 2, 2025',
    terminal: 'T5',
  },
  duration: '8h 00m',
  stops: 0,
  stopDetails: [],
  aircraft: 'Boeing 777',
  fare: {
    type: 'Economy',
    price: 500,
    currency: 'USD',
    refundable: false,
    baggage: '1 x 23kg',
  },
};

const mockSegment: MultiCitySegment = {
  id: 'test-segment-1',
  origin: { city: 'New York', code: 'JFK' },
  destination: { city: 'London', code: 'LHR' },
  date: 'July 2, 2025',
  flights: [mockFlight],
};

describe('Multi-City Components', () => {
  describe('AirlineIcon', () => {
    it('renders airline code correctly', () => {
      render(<AirlineIcon code="TA" />);
      expect(screen.getByText('TA')).toBeInTheDocument();
    });

    it('applies correct size classes', () => {
      const { container } = render(<AirlineIcon code="TA" size="lg" />);
      const icon = container.firstChild as HTMLElement;
      expect(icon).toHaveClass('w-10', 'h-10');
    });
  });

  describe('FlightDurationChart', () => {
    it('renders duration chart', () => {
      const { container } = render(<FlightDurationChart duration="8h 00m" />);
      const chart = container.querySelector('.bg-teal-500');
      expect(chart).toBeInTheDocument();
    });
  });

  describe('MultiCityFlightCard', () => {
    const mockProps = {
      flight: mockFlight,
      isSelected: false,
      onSelect: jest.fn(),
      onToggleDetails: jest.fn(),
      showDetails: false,
    };

    it('renders flight information correctly', () => {
      render(<MultiCityFlightCard {...mockProps} />);
      
      expect(screen.getByText('Test Airlines')).toBeInTheDocument();
      expect(screen.getByText('TA123')).toBeInTheDocument();
      expect(screen.getByText('$500')).toBeInTheDocument();
      expect(screen.getByText('10:00')).toBeInTheDocument();
      expect(screen.getByText('22:00')).toBeInTheDocument();
      expect(screen.getByText('JFK')).toBeInTheDocument();
      expect(screen.getByText('LHR')).toBeInTheDocument();
    });

    it('calls onSelect when select button is clicked', () => {
      const onSelect = jest.fn();
      render(<MultiCityFlightCard {...mockProps} onSelect={onSelect} />);
      
      const selectButton = screen.getByText('Select');
      fireEvent.click(selectButton);
      
      expect(onSelect).toHaveBeenCalledWith(mockFlight);
    });

    it('shows selected state correctly', () => {
      render(<MultiCityFlightCard {...mockProps} isSelected={true} />);
      
      expect(screen.getByText('Selected')).toBeInTheDocument();
    });

    it('toggles details when view details is clicked', () => {
      const onToggleDetails = jest.fn();
      render(<MultiCityFlightCard {...mockProps} onToggleDetails={onToggleDetails} />);
      
      const detailsButton = screen.getByText('View details');
      fireEvent.click(detailsButton);
      
      expect(onToggleDetails).toHaveBeenCalledWith(mockFlight.id);
    });
  });

  describe('SegmentHeader', () => {
    const mockProps = {
      segment: mockSegment,
      selectedFlight: null,
      isCollapsed: false,
      onToggleCollapse: jest.fn(),
    };

    it('renders segment information correctly', () => {
      render(<SegmentHeader {...mockProps} />);
      
      expect(screen.getByText('JFK')).toBeInTheDocument();
      expect(screen.getByText('LHR')).toBeInTheDocument();
      expect(screen.getByText('(New York - London)')).toBeInTheDocument();
      expect(screen.getByText('July 2, 2025 • 1 flights available')).toBeInTheDocument();
    });

    it('shows price range when no flight is selected', () => {
      render(<SegmentHeader {...mockProps} />);

      expect(screen.getByText(/Price Range/i)).toBeInTheDocument();
    });

    it('shows selected flight information when flight is selected', () => {
      render(<SegmentHeader {...mockProps} selectedFlight={mockFlight} />);
      
      expect(screen.getByText('Hide Details')).toBeInTheDocument();
    });
  });

  describe('TripSummary', () => {
    const mockProps = {
      segments: [mockSegment],
      selectedFlights: {},
      totalPrice: 0,
      onBookItinerary: jest.fn(),
      onEmailItinerary: jest.fn(),
    };

    it('shows empty state when no flights selected', () => {
      render(<TripSummary {...mockProps} />);
      
      expect(screen.getByText('Select flights to build your itinerary')).toBeInTheDocument();
    });

    it('shows selected flights and total price', () => {
      const selectedFlights = { 'test-segment-1': mockFlight };
      render(<TripSummary {...mockProps} selectedFlights={selectedFlights} totalPrice={500} />);
      
      expect(screen.getByText('JFK → LHR')).toBeInTheDocument();
      expect(screen.getByText('$500')).toBeInTheDocument();
      expect(screen.getByText('Total')).toBeInTheDocument();
    });

    it('calls booking function when book button is clicked', () => {
      const onBookItinerary = jest.fn();
      const selectedFlights = { 'test-segment-1': mockFlight };
      render(<TripSummary {...mockProps} selectedFlights={selectedFlights} onBookItinerary={onBookItinerary} />);
      
      const bookButton = screen.getByText('Book Itinerary');
      fireEvent.click(bookButton);
      
      expect(onBookItinerary).toHaveBeenCalled();
    });
  });
});
