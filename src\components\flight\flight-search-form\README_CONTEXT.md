# Flight Search Form with Context & LocalStorage

This document describes the enhanced FlightSearchForm with context-based state management and localStorage persistence.

## 🚀 Features

### ✅ **Context-Based State Management**
- Centralized state management using React Context
- Automatic localStorage persistence
- Search history tracking
- Form validation

### ✅ **Enhanced Data Types**
- Proper TypeScript interfaces for all data structures
- Airport objects with detailed information
- Advanced search options with comprehensive types
- Date handling with proper Date objects

### ✅ **LocalStorage Integration**
- Automatic saving of search data
- Search history with timestamps
- Persistent state across browser sessions
- Automatic data restoration on page load

### ✅ **Clear Buttons**
- Clear buttons for airport selection inputs
- Conditional display (only when content exists)
- Proper event handling to prevent dropdown opening

### ✅ **Automatic Navigation**
- Automatic navigation to flight results page on valid search
- Search data passed via React Router state
- Redirect protection for direct access to results page
- Backward compatibility with optional onSearch callback

## 📁 File Structure

```
src/
├── contexts/
│   └── FlightSearchContext.tsx          # Main context with types and logic
├── utils/
│   └── airportUtils.ts                  # Airport parsing and utility functions
├── components/flight/flight-search-form/
│   ├── FlightSearchForm.tsx             # Updated form component
│   ├── FlightSearchFormWithProvider.tsx # Form wrapped with provider
│   └── README_CONTEXT.md               # This documentation
└── examples/
    └── FlightSearchExample.tsx         # Usage example
```

## 🔧 Data Types

### **Main Search Data Structure**
```typescript
interface FlightSearchData {
  from: Airport;
  to: Airport;
  depart: Date;
  return: Date | null;
  advanced_search: AdvancedSearch;
  tripType: 'roundTrip' | 'oneWay' | 'multiCity';
  passengers: {
    adults: number;
    children: number;
    infants: number;
  };
  class: 'economy' | 'premiumEconomy' | 'business' | 'first';
}
```

### **Airport Structure**
```typescript
interface Airport {
  city: string;
  airport: string;
  iata: string;
  country: string;
  airportOpen: boolean;
}
```

### **Advanced Search Options**
```typescript
interface AdvancedSearch {
  selectedAirlines: string[];
  flightOptions: {
    directFlights: boolean;
    refundableFares: boolean;
    corporateRates: boolean;
  };
  services: {
    airportLounge: boolean;
    extraBaggage: boolean;
    travelInsurance: boolean;
  };
  priceRange?: {
    min: number;
    max: number;
  };
  departureTime?: {
    earliest: string;
    latest: string;
  };
  arrivalTime?: {
    earliest: string;
    latest: string;
  };
  stops?: 'any' | 'nonstop' | '1stop' | '2+stops';
  duration?: {
    max: number; // in hours
  };
  baggage?: {
    carryOn: boolean;
    checked: boolean;
  };
}
```

## 🎯 Usage

### **Basic Usage with Automatic Navigation**
```tsx
import FlightSearchFormWithProvider from './components/flight/flight-search-form/FlightSearchFormWithProvider';

const MyComponent = () => {
  // No onSearch callback needed - form handles navigation automatically
  return (
    <FlightSearchFormWithProvider />
  );
};
```

### **Usage with Optional Callback**
```tsx
import FlightSearchFormWithProvider from './components/flight/flight-search-form/FlightSearchFormWithProvider';

const MyComponent = () => {
  const handleSearch = (searchData) => {
    console.log('Search data:', searchData);
    // Optional: Handle additional logic (analytics, etc.)
    // Navigation happens automatically after this callback
  };

  return (
    <FlightSearchFormWithProvider onSearch={handleSearch} />
  );
};
```

### **Using the Context Directly**
```tsx
import { useFlightSearch } from './contexts/FlightSearchContext';

const MyComponent = () => {
  const { 
    currentSearch, 
    updateSearch, 
    saveSearch, 
    isSearchValid,
    getRecentSearches 
  } = useFlightSearch();

  // Access and modify search data
  const handleUpdateAirport = () => {
    updateSearch({
      from: {
        city: 'New York',
        airport: 'John F. Kennedy International Airport',
        iata: 'JFK',
        country: 'United States',
        airportOpen: false
      }
    });
  };

  return (
    <div>
      <p>Current search: {JSON.stringify(currentSearch)}</p>
      <button onClick={handleUpdateAirport}>Update Airport</button>
      <button onClick={saveSearch} disabled={!isSearchValid()}>
        Save Search
      </button>
    </div>
  );
};
```

### **Receiving Search Data in Flight List Page**
```tsx
import { useLocation, useNavigate } from 'react-router-dom';

const FlightListPage = () => {
  const location = useLocation();
  const navigate = useNavigate();

  // Extract search data from navigation state
  const searchData = location.state?.searchData;
  const legacySearchData = location.state?.legacySearchData;

  // Redirect if no search data
  useEffect(() => {
    if (!searchData && !legacySearchData) {
      navigate('/flight/search', { replace: true });
    }
  }, [searchData, legacySearchData, navigate]);

  // Use search data for flight search API calls
  useEffect(() => {
    if (searchData) {
      console.log('Search parameters:', {
        from: searchData.from,
        to: searchData.to,
        departure: searchData.depart,
        return: searchData.return,
        passengers: searchData.passengers,
        class: searchData.class,
        tripType: searchData.tripType,
        advancedOptions: searchData.advanced_search
      });

      // Make API calls with search data
      // fetchFlights(searchData);
    }
  }, [searchData]);

  return (
    <div>
      {/* Flight list content */}
    </div>
  );
};
```

## 🔄 Context Methods

### **updateSearch(data: Partial<FlightSearchData>)**
Updates the current search data with partial data.

### **saveSearch()**
Saves the current search to localStorage and adds it to search history.

### **isSearchValid(): boolean**
Validates if the current search data is complete and valid.

### **getRecentSearches(limit?: number): FlightSearchHistory[]**
Returns recent searches from history.

### **loadSearch(searchId: string)**
Loads a specific search from history.

### **clearSearch()**
Clears the current search data.

### **deleteFromHistory(searchId: string)**
Removes a search from history.

## 💾 LocalStorage Keys

- `flightSearchData` - Current search data
- `flightSearchHistory` - Search history array

## 🔧 Utility Functions

### **parseAirportString(airportString, airportData): Airport**
Converts string format "City (CODE)" to Airport object.

### **formatAirportDisplay(airport): string**
Converts Airport object to display string.

### **isValidAirport(airport): boolean**
Validates airport data completeness.

## 🎨 Clear Button Features

- **Conditional Display**: Only shows when input has content
- **Event Prevention**: Prevents dropdown from opening when clearing
- **Visual Feedback**: Red hover color indicates destructive action
- **Accessibility**: Tooltip explains button function

## 🔄 Migration from Old Format

The system automatically handles legacy search data format conversion using the `convertLegacySearchData` utility function.

## 🚨 Important Notes

1. **Provider Required**: Always wrap components with `FlightSearchProvider` or use `FlightSearchFormWithProvider`
2. **Date Objects**: All dates are stored as Date objects, not strings
3. **Validation**: Use `isSearchValid()` before saving or submitting searches
4. **History Limit**: Search history is limited to 20 items
5. **Browser Support**: Requires localStorage support
6. **Navigation**: Form automatically navigates to `/flight/results` on valid search
7. **Router Required**: Requires React Router setup for navigation functionality
8. **State Passing**: Search data is passed via React Router state, not URL parameters

## 🐛 Troubleshooting

### **Context Not Available Error**
```
Error: useFlightSearch must be used within a FlightSearchProvider
```
**Solution**: Wrap your component with `FlightSearchProvider` or use `FlightSearchFormWithProvider`.

### **Date Parsing Issues**
**Solution**: Ensure dates are properly converted to Date objects using `new Date()`.

### **Airport Data Not Saving**
**Solution**: Ensure airport data includes all required fields (city, airport, iata, country).
