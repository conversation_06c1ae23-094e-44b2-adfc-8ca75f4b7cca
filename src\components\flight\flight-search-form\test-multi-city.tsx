import React from 'react';
import SearchFormFlight from './SearchFormFlight';
import type { flight_search_form } from '../../../models/flight/flight-search.model';

// Test component to verify multi-city functionality
const TestMultiCityLayout: React.FC = () => {
  const handleSearch = (searchData: flight_search_form) => {
    console.log('Multi-city search data:', searchData);
    alert(`Multi-city search with ${searchData.trips.length} trips`);
  };

  return (
    <div className="p-8 bg-gray-100 min-h-screen">
      <h1 className="text-2xl font-bold mb-6 text-center">
        Multi-City Flight Search Test
      </h1>
      
      <div className="max-w-4xl mx-auto">
        <SearchFormFlight 
          onSearch={handleSearch}
          compact={false}
        />
      </div>

      <div className="mt-8 max-w-4xl mx-auto bg-white p-6 rounded-lg shadow">
        <h2 className="text-lg font-semibold mb-4">Test Instructions:</h2>
        <ol className="list-decimal list-inside space-y-2 text-sm">
          <li>Select "Multi-City" trip type to see the new layout</li>
          <li>Verify the first line contains: Airport selections (From/To) and Departure date</li>
          <li>Verify the second line contains: "Add City/Stop" button</li>
          <li>Verify the third line contains: Advanced Search and Travel Class</li>
          <li>Click "Add City/Stop" to add more trips</li>
          <li>Verify each trip has a remove button (except when only 1 trip)</li>
          <li>Switch back to "One Way" or "Round Trip" to see the original layout</li>
        </ol>
      </div>

      <div className="mt-6 max-w-4xl mx-auto bg-blue-50 p-6 rounded-lg">
        <h2 className="text-lg font-semibold mb-4 text-blue-800">Expected Multi-City Layout:</h2>
        <div className="space-y-3 text-sm text-blue-700">
          <div className="bg-white p-3 rounded border-l-4 border-blue-500">
            <strong>Line 1:</strong> From Airport | Swap | To Airport | Departure Date | Remove (if multiple trips)
          </div>
          <div className="bg-white p-3 rounded border-l-4 border-green-500">
            <strong>Line 2:</strong> Add City/Stop Button (centered, only shown for last trip)
          </div>
          <div className="bg-white p-3 rounded border-l-4 border-purple-500">
            <strong>Line 3:</strong> Advanced Search | Travel Class (shown once for all trips)
          </div>
        </div>
      </div>
    </div>
  );
};

export default TestMultiCityLayout;
