# Flight API Service Documentation

## 📋 Overview

The Flight API Service provides individual callable functions for all flight-related operations. Each function uses a common axios instance with shared authentication, error handling, and interceptors.

## 🏗️ Architecture

```
flight-api.service.ts
├── Common Axios Instance (shared)
├── Airport Operations
├── Flight Search Operations  
├── Flight Details Operations
├── Configuration Operations
├── Pricing Operations
├── Additional Services
├── Booking Operations
└── Fare Comparison Operations
```

## 🚀 Quick Start

### Import Methods

```typescript
// Modern approach - Import specific functions
import { searchAirport, callUnifiedSearch, getFlightInfo } from 'services/api/flight-api.service';

// Or import all functions
import * as FlightApi from 'services/api/flight-api.service';

// Legacy compatibility
import FlightApiService from 'services/api';
```

### Basic Usage

```typescript
// Search airports
const airports = await searchAirport({ search_text: 'Delhi' });

// Perform flight search
const searchResults = await callUnifiedSearch(searchBody);

// Get flight details
const flightInfo = await getFlightInfo(detailsBody);
```

## 🔍 Flight Search Flow

### 1. **Standard Search Workflow**

```mermaid
graph TD
    A[User Input] --> B[callUnifiedSearch]
    B --> C{Has TUI?}
    C -->|Yes| D[Call search_list/]
    C -->|No| E[Call search/]
    E --> F[Get TUI]
    F --> G[Call search_list/]
    D --> H[Return Results]
    G --> H
```

### 2. **Search Methods Hierarchy**

```typescript
// 🎯 RECOMMENDED: Unified Search (handles complete workflow)
const results = await callUnifiedSearch(searchBody);

// 🔄 CACHE OPTIMIZED: Seamless Refresh (tries cache first)
const results = await callSeamlessRefreshSearch(searchBody);

// ⏱️ GUARANTEED RESULTS: Polling Search (ensures completion)
const results = await callPollingSearch(searchBody, 10, 1000);

// 📋 DIRECT: Express Search (requires existing TUI)
const results = await getExpressSearch({ TUI: 'existing-tui', ClientID: '' });
```

## 📚 API Functions Reference

### 🏢 Airport Operations

#### `searchAirport(body)`
Search airports by query string with autocomplete.

```typescript
const airports = await searchAirport({
  search_text: 'Delhi'
});

// Returns: AirportList[]
// [{ code: 'DEL', city_name: 'Delhi', name: 'Indira Gandhi International' }]
```

#### `getAllAirports(limit?)`
Get paginated list of all airports.

```typescript
const airports = await getAllAirports(200);

// Returns: AirportList[]
// Paginated list of airports with metadata
```

### ✈️ Flight Search Operations

#### `callUnifiedSearch(body)` ⭐ **RECOMMENDED**
Complete search workflow: search → search_list

```typescript
const searchBody = {
  SecType: "DOM",
  FareType: "ON", // "ON" = One-way, "RT" = Round-trip
  ADT: 1,
  CHD: 0,
  INF: 0,
  Cabin: "E",
  Source: "WEB",
  Mode: "LIVE",
  ClientID: "",
  IsMultipleCarrier: true,
  IsRefundable: false,
  preferedAirlines: null,
  TUI: "",
  Trips: [{
    Origin: "DEL",
    Destination: "BOM", 
    DepartureDate: "2024-12-25"
  }],
  Parameters: {
    Currency: "INR",
    Language: "en"
  }
};

const results = await callUnifiedSearch(searchBody);
```

#### `callSeamlessRefreshSearch(body)`
Cache-optimized search with fallback to unified search.

```typescript
const results = await callSeamlessRefreshSearch(searchBody);
// Tries search_refresh/ first, falls back to unified search
```

#### `callPollingSearch(body, maxAttempts?, pollInterval?)`
Ensures results with automatic polling mechanism.

```typescript
const results = await callPollingSearch(
  searchBody,
  10,    // Max attempts
  1000   // Poll interval (ms)
);
// Polls until completion or max attempts reached
```

#### `getExpressSearch(body)`
Direct search_list call with existing TUI.

```typescript
const results = await getExpressSearch({
  TUI: "existing-tui-string",
  ClientID: ""
});
```

### 📋 Flight Details Operations

#### `getFlightInfo(body)`
Get detailed flight information including segments, fare rules, etc.

```typescript
const flightInfo = await getFlightInfo({
  TUI: "search-tui",
  Index: "flight-index",
  ClientID: ""
});
```

#### `getFlightSSR(body)`
Get Special Service Requests (meals, seats, baggage).

```typescript
const ssrOptions = await getFlightSSR({
  TUI: "search-tui",
  Index: "flight-index",
  ClientID: ""
});
```

#### `getFlightFareRule(body)`
Get fare rules and restrictions.

```typescript
const fareRules = await getFlightFareRule({
  TUI: "search-tui",
  Index: "flight-index",
  ClientID: ""
});
```

### ⚙️ Configuration Operations

#### `getWebSettings()`
Get application configuration and settings.

```typescript
const settings = await getWebSettings();
// Returns: WebSettings with app configuration
```

### 💰 Pricing Operations

#### `getTripValues()`
Get smart pricing configuration data.

```typescript
const pricingData = await getTripValues();
```

#### `callSmartPricer(body)`
Get intelligent pricing suggestions.

```typescript
const pricing = await callSmartPricer({
  route: "DEL-BOM",
  date: "2024-12-25"
});
```

#### `callGetsPrice(body)`
Get detailed pricing breakdown.

```typescript
const priceDetails = await callGetsPrice({
  TUI: "search-tui",
  Index: "flight-index"
});
```

### 🎫 Booking Operations

#### `bookNowApi(body)`
Create a new flight booking.

```typescript
const booking = await bookNowApi({
  flight_booking: {
    TUI: "search-tui",
    Trips: [{
      Journey: [{
        Index: "flight-index"
      }]
    }]
  },
  passenger_details: [
    {
      Title: "Mr",
      FirstName: "John",
      LastName: "Doe",
      DateOfBirth: "1990-01-01",
      Gender: "M",
      PassportNumber: "A1234567",
      PassportExpiry: "2030-01-01"
    }
  ]
});
```

#### `getBookingList()`
Get user's booking history.

```typescript
const bookings = await getBookingList();
// Returns: BookingList[]
```

#### `getBookingDetails(bookingReference)`
Get detailed booking information.

```typescript
const booking = await getBookingDetails("BOOKING123");
// Returns: DetailedBookingResponse
```

### 📊 Fare Comparison Operations

#### `getFareComparison(body)`
Compare fares across different options.

```typescript
const comparison = await getFareComparison({
  route: "DEL-BOM",
  currentFare: 5000,
  departureDate: "2024-12-25",
  airline: "6E"
});
```

#### `getFareCalendar(body)`
Get fare calendar for flexible dates.

```typescript
const calendar = await getFareCalendar({
  route: "DEL-BOM", 
  startDate: "2024-12-20",
  endDate: "2024-12-30"
});
```

## 🔄 Complete Flight Booking Flow

### Step 1: Search Airports
```typescript
const airports = await searchAirport({ search_text: 'Delhi' });
const origin = airports[0].code; // 'DEL'
```

### Step 2: Search Flights
```typescript
const searchBody = {
  SecType: "DOM",
  FareType: "ON",
  ADT: 1, CHD: 0, INF: 0,
  Cabin: "E",
  Source: "WEB", Mode: "LIVE", ClientID: "",
  IsMultipleCarrier: true, IsRefundable: false,
  preferedAirlines: null, TUI: "",
  Trips: [{ Origin: "DEL", Destination: "BOM", DepartureDate: "2024-12-25" }],
  Parameters: { Currency: "INR", Language: "en" }
};

const searchResults = await callUnifiedSearch(searchBody);
```

### Step 3: Get Flight Details
```typescript
const selectedFlight = searchResults.Trips[0].Journey[0];
const flightInfo = await getFlightInfo({
  TUI: searchResults.TUI,
  Index: selectedFlight.Index,
  ClientID: ""
});
```

### Step 4: Get SSR Options (Optional)
```typescript
const ssrOptions = await getFlightSSR({
  TUI: searchResults.TUI,
  Index: selectedFlight.Index,
  ClientID: ""
});
```

### Step 5: Create Booking
```typescript
const booking = await bookNowApi({
  flight_booking: {
    TUI: searchResults.TUI,
    Trips: [{
      Journey: [{ Index: selectedFlight.Index }]
    }]
  },
  passenger_details: [/* passenger info */]
});
```

## ⚠️ Error Handling

All functions throw errors that should be handled:

```typescript
try {
  const results = await callUnifiedSearch(searchBody);
  // Handle success
} catch (error) {
  // Handle error
  console.error('Search failed:', error.message);
}
```

## 🔧 Common Axios Instance

All functions use the same axios instance with:
- ✅ Automatic token management
- ✅ Request/response interceptors  
- ✅ Error handling and retries
- ✅ Response metadata injection
- ✅ Authentication headers

## 📝 Notes

- All functions are **individual and callable**
- **No console logs** - clean production code
- **TypeScript support** with proper interfaces
- **Backward compatibility** maintained
- **Common axios instance** shared across all functions
- **Professional error handling** without noise
