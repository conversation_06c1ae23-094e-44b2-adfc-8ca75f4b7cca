import { useCallback, useEffect, useRef, useState } from "react";
import { set, useForm } from "react-hook-form";
import { useLocation } from "react-router-dom";
import type {
  ClassType,
  flight_search_form,
  flight_trip,
} from "../../../models/flight/flight-search.model";
import TripTypeSelector from "./components/trip-type-selecter/TripTypeSelector";
import type { Airline,Airport,TripTypeValue} from "../../../models/flight/common-flight.model";
import SearchButton from "./components/SearchButton";
import RecentSearches from "./components/RecentSearches";
import { getPopularAirportFromLocal } from "../../../helper/flight/flight-search-helper";
import {getAirportApi,getPopularAirportApi} from "../../../api/flight/search-service";
import AirportSearch from "./components/airport-search/AirportSearch";
import AdvancedSearchContent, {
  type advancedSearchData,
  type FlightOptions,
  type Services,
} from "./components/advanced-search/AdvancedSearchContent";
import CalendarModal from "./components/multi-date-picker/CalendarModal";
import { formatShortFriendlyDate } from "../../utils/date-utils";
import TravelClassPicker from "./components/travel-class/TravelClassPicker";
import { useAlert } from "../../utils/alert/Alert";
import { useFlightSearch, type FlightSearchData } from "../../../contexts/FlightSearchContext";

export interface FlightSearchFormProps {
  onSearch: (searchData: flight_search_form) => void;
  compact?: boolean;
}

const tripTypes: Array<{ id: TripTypeValue; label: string }> = [
  { id: "RT", label: "Round Trip" },
  { id: "ON", label: "One Way" },
  { id: "MC", label: "Multi-City" },
];

const classOptions = [
  { id: "E", label: "Economy" },
  { id: "PE", label: "Premium Economy" },
  { id: "B", label: "Business" },
  { id: "F", label: "First Class" },
];

const dummyAirlines = [
  {
    name: "Emirates",
    iata: "EK",
    country: "United Arab Emirates",
    rating: 4.8,
  },
  {
    name: "Qatar Airways",
    iata: "QR",
    country: "Qatar",
    rating: 4.7,
  },
  {
    name: "Singapore Airlines",
    iata: "SQ",
    country: "Singapore",
    rating: 4.9,
  },
  {
    name: "Lufthansa",
    iata: "LH",
    country: "Germany",
    rating: 4.2,
  },
  {
    name: "Turkish Airlines",
    iata: "TK",
    country: "Turkey",
    rating: 4.4,
  },
  {
    name: "IndiGo",
    iata: "6E",
    country: "India",
    rating: 3.9,
  },
  {
    name: "British Airways",
    iata: "BA",
    country: "United Kingdom",
    rating: 4.3,
  },
  {
    name: "Air France",
    iata: "AF",
    country: "France",
    rating: 4.0,
  },
  {
    name: "Delta Air Lines",
    iata: "DL",
    country: "United States",
    rating: 4.1,
  },
  {
    name: "Japan Airlines",
    iata: "JL",
    country: "Japan",
    rating: 4.6,
  },
];

function SearchFormFlight({ onSearch, compact }: FlightSearchFormProps) {
  const { fire } = useAlert();
  const location = useLocation();

  // Flight search context
  const { currentSearch, updateSearch, isLoading } = useFlightSearch();

  // Helper function to safely parse dates in local timezone
  const parseLocalDate = useCallback((dateInput: string | Date | null): Date | null => {
    if (!dateInput) return null;

    if (dateInput instanceof Date) return dateInput;

    // If it's an ISO string, parse it properly to avoid timezone issues
    if (typeof dateInput === 'string') {
      // Check if it's an ISO string (contains 'T' or 'Z')
      if (dateInput.includes('T') || dateInput.includes('Z')) {
        return new Date(dateInput);
      }

      // If it's a date string in YYYY-MM-DD format, parse in local timezone
      const dateMatch = dateInput.match(/^(\d{4})-(\d{2})-(\d{2})$/);
      if (dateMatch) {
        const [, year, month, day] = dateMatch;
        return new Date(parseInt(year), parseInt(month) - 1, parseInt(day));
      }

      // Fallback to regular Date parsing
      return new Date(dateInput);
    }

    return null;
  }, []);

  // Helper function to format dates in local timezone (YYYY-MM-DD)
  const formatLocalDate = useCallback((date: Date): string => {
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    return `${year}-${month}-${day}`;
  }, []);

  // Helper function to validate and fix dates that are in the past
  const validateAndFixDate = useCallback((date: Date | null, fallbackDate?: Date, minDate?: Date): Date => {
    if (!date) {
      return fallbackDate || new Date();
    }

    const today = new Date();
    // Set time to start of day for accurate comparison
    const todayStart = new Date(today.getFullYear(), today.getMonth(), today.getDate());
    const dateStart = new Date(date.getFullYear(), date.getMonth(), date.getDate());

    // Determine the minimum allowed date (either today or a provided minimum date)
    const minAllowedDate = minDate ?
      new Date(Math.max(todayStart.getTime(), new Date(minDate.getFullYear(), minDate.getMonth(), minDate.getDate()).getTime())) :
      todayStart;

    // If the date is before the minimum allowed date, return the minimum allowed date
    if (dateStart < minAllowedDate) {
      const fixedDate = new Date(minAllowedDate.getFullYear(), minAllowedDate.getMonth(), minAllowedDate.getDate());
      console.log(`📅 Date ${formatLocalDate(date)} is invalid (before ${formatLocalDate(minAllowedDate)}), updating to: ${formatLocalDate(fixedDate)}`);
      return fixedDate;
    }

    return date;
  }, [formatLocalDate]);

  const [showToast, setShowToast] = useState<boolean>(false);
  const [airportSearchTerm, setAirportSearchTerm] = useState<string>("");
  const [backupAirportList, setBackupAirportList] = useState<Airport[]>([]);
  const [airportList, setAirportList] = useState<Airport[]>([]);
  const [airlineList, setAirlineList] = useState<Airline[]>(dummyAirlines);
  const [isAirportShimmer, setIsAirportShimmer] = useState<boolean>(false);
  const [showAdvancedSearch, setShowAdvancedSearch] = useState<boolean>(false);
  const [dateSelectionState, setDateSelectionState] = useState<
    "DT" | "RT" | null
  >("DT");
  const [isTravelPopUpOpen, setIsTravelPopUpOpen] = useState<boolean>(false);
  const [searchData, setSearchData] = useState<advancedSearchData>({
    airlines: [],
    services: {
      airportLounge: false,
      extraBaggage: {
        enabled: false,
        weight: 23, // Default weight
        isCustom: false,
      },
      travelInsurance: false,
    },
    flight_option: {
      directFlights: false,
      refundableFares: false,
      corporateRates: false,
    },
  });
  // refs
  const fromAirportDropdownRefs = useRef<(HTMLDivElement | null)[]>([]);
  const toAirportDropdownRefs = useRef<(HTMLDivElement | null)[]>([]);
  const travelersClassDropdownRef = useRef<HTMLDivElement>(null);
  const formContainerRef = useRef<HTMLDivElement>(null);

  // form
  const {
    setValue,
    getValues,
    watch,
    formState: { errors },
  } = useForm<flight_search_form>();
  const formArray = watch();
  const trips = watch("trips");
  const tripError = errors.trips?.length ? errors.trips : undefined;
  const travellers = watch("travellers");

  // advanced search
  const handleAirlineToggle = useCallback(
    (airlineName: string) => {
      setSearchData((prevData) => {
        const existingAirlineIndex = prevData.airlines.findIndex(
          (airline) => airline.name === airlineName
        );

        let updatedAirlines: Airline[];

        if (existingAirlineIndex !== -1) {
          // Remove airline if it exists
          updatedAirlines = prevData.airlines.filter(
            (airline) => airline.name !== airlineName
          );
        } else {
          const airlineToAdd = airlineList.find(
            (airline) => airline.name === airlineName
          );
          if (airlineToAdd) {
            updatedAirlines = [...prevData.airlines, airlineToAdd];
          } else {
            updatedAirlines = prevData.airlines;
          }
        }

        return {
          ...prevData,
          airlines: updatedAirlines,
        };
      });
    },
    [airlineList]
  );

  // Handle flight options toggle with proper typing
  const handleFlightOptionToggle = useCallback(
    (option: keyof FlightOptions) => {
      setSearchData((prevData: advancedSearchData) => ({
        ...prevData,
        flight_option: {
          ...prevData.flight_option,
          [option]: !prevData.flight_option[option],
        },
      }));
    },
    []
  );

  // Handle services toggle (for simple boolean services)
  const handleServiceToggle = useCallback((service: keyof Services) => {
    // Only handle simple boolean services here
    if (service === "extraBaggage") {
      console.warn("Use handleExtraBaggageChange for extraBaggage instead");
      return;
    }

    setSearchData((prevData) => ({
      ...prevData,
      services: {
        ...prevData.services,
        [service]:
          !prevData.services[service as keyof Omit<Services, "extraBaggage">],
      },
    }));
  }, []);

  // Handle extra baggage changes with proper typing
  const handleExtraBaggageChange = useCallback(
    (enabled: boolean, weight?: number, isCustom?: boolean) => {
      setSearchData((prevData) => ({
        ...prevData,
        services: {
          ...prevData.services,
          extraBaggage: {
            enabled,
            weight: weight ?? prevData.services.extraBaggage.weight,
            isCustom: isCustom ?? prevData.services.extraBaggage.isCustom,
          },
        },
      }));
    },
    []
  );

  // Handle reset filters with toast notification
  const handleResetFilters = useCallback(() => {
    setSearchData({
      airlines: [],
      services: {
        airportLounge: false,
        extraBaggage: {
          enabled: false,
          weight: 23,
          isCustom: false,
        },
        travelInsurance: false,
      },
      flight_option: {
        directFlights: false,
        refundableFares: false,
        corporateRates: false,
      },
    });

    // Show toast notification
    setShowToast(true);
    setTimeout(() => {
      setShowToast(false);
    }, 3000);
  }, []);

  const getPopularAirportList = useCallback(async () => {
    setIsAirportShimmer(true);
    try {
      const response = await getPopularAirportApi();
      setAirportList(response.airports);
      setBackupAirportList(response.airports);
      localStorage.setItem("popularAirport", JSON.stringify(response.airports));
      setIsAirportShimmer(false);
    } catch (eroor) {
      console.log(eroor);
    }
  }, []);

  const getAdvancedSearchCount = useCallback(() => {
    const airlineCount = searchData.airlines.length;
    const flightOptionsCount = Object.values(searchData.flight_option).filter(
      Boolean
    ).length;
    const servicesCount = Object.values(searchData.services).filter(
      Boolean
    ).length;

    return airlineCount + flightOptionsCount + servicesCount;
  }, [searchData]);

  const handleAirportSearch = useCallback(
    async (searchTerm: string) => {
      setIsAirportShimmer(true);
      try {
        const result = await getAirportApi(searchTerm);
        setAirportList(result.results);
        setBackupAirportList(result.results);
        setIsAirportShimmer(false);

        // Filter new airports not already in backupAirportList
        const newAirports = result.results.filter(
          (airport) =>
            !backupAirportList.some(
              (existing) => existing.code === airport.code
            )
        );

        if (newAirports.length > 0) {
          setBackupAirportList((prev) => [...prev, ...newAirports]);
        }
      } catch (error) {
        console.log(error);
        setIsAirportShimmer(false); // ensure shimmer turns off on error
      }
    },
    [backupAirportList]
  );

  const handleAirportSearchChange = useCallback(
    async (e: React.ChangeEvent<HTMLInputElement>) => {
      const searchTerm = e.target.value;
      setAirportSearchTerm(searchTerm);

      if (!searchTerm.trim()) {
        // If no input, show all from backup list
        setAirportList(backupAirportList);
        return;
      }

      const filteredLocal = backupAirportList.filter(
        (airport) =>
          airport.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
          airport.city_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
          airport.code.toLowerCase().includes(searchTerm.toLowerCase())
      );

      if (filteredLocal.length >= 5) {
        setAirportList(filteredLocal.slice(0, 10));
      } else {
        await handleAirportSearch(searchTerm);
      }
    },
    [backupAirportList, handleAirportSearch]
  );

  const handleSwapAirports = useCallback(
    (tripIndex: number) => {
      const trip = trips[tripIndex];
      const from = trip.from;
      const to = trip.to;

      // Swap `from` and `to`
      setValue(`trips.${tripIndex}.from`, to);
      setValue(`trips.${tripIndex}.to`, from);
    },
    [setValue, trips]
  );

  const handleClearAirportSelect = useCallback(
    (index: number, dest: string) => {
      if (dest === "from") {
        setValue(`trips.${index}.from.city`, "");
        setValue(`trips.${index}.from.airport`, "");
        setValue(`trips.${index}.from.country`, "");
        setValue(`trips.${index}.from.iata`, "");
        setValue(`trips.${index}.from.airportOpen`, true);
      } else if (dest === "to") {
        setValue(`trips.${index}.to.city`, "");
        setValue(`trips.${index}.to.airport`, "");
        setValue(`trips.${index}.to.country`, "");
        setValue(`trips.${index}.to.iata`, "");
        setValue(`trips.${index}.to.airportOpen`, true);
      }
  },[setValue]);

  // Multi-city trip management functions
  const addTrip = useCallback(() => {
    const currentTrips = getValues("trips");
    const lastTrip = currentTrips[currentTrips.length - 1];

    const newTrip: flight_trip = {
      from: {
        city: lastTrip?.to?.city || "",
        airport: lastTrip?.to?.airport || "",
        iata: lastTrip?.to?.iata || "",
        country: lastTrip?.to?.country || "",
        airportOpen: false,
      },
      to: {
        city: "",
        airport: "",
        iata: "",
        country: "",
        airportOpen: false,
      },
      depart: new Date(),
      return: null,
      isCalenderOpen: false,
    };

    setValue("trips", [...currentTrips, newTrip]);
  }, [getValues, setValue]);

  const removeTrip = useCallback((index: number) => {
    const currentTrips = getValues("trips");
    if (currentTrips.length > 1) {
      const updatedTrips = currentTrips.filter((_, i) => i !== index);
      setValue("trips", updatedTrips);
    }
  }, [getValues, setValue]);

  // Convert FlightSearchData to flight_search_form format
  const convertContextToFormData = useCallback((searchData: FlightSearchData): flight_search_form => {
    const fareType: TripTypeValue =
      searchData.tripType === 'roundTrip' ? 'RT' :
      searchData.tripType === 'oneWay' ? 'ON' : 'MC';

    const cabin: ClassType =
      searchData.class === 'economy' ? 'E' :
      searchData.class === 'premiumEconomy' ? 'PE' :
      searchData.class === 'business' ? 'B' : 'F';

    let trips: flight_trip[];

    // Handle multi-city trips if available
    if (searchData.tripType === 'multiCity' && searchData.trips && searchData.trips.length > 0) {
      trips = searchData.trips.map(trip => ({
        from: {
          city: trip.from.city,
          airport: trip.from.airport,
          iata: trip.from.iata,
          country: trip.from.country,
          airportOpen: false,
        },
        to: {
          city: trip.to.city,
          airport: trip.to.airport,
          iata: trip.to.iata,
          country: trip.to.country,
          airportOpen: false,
        },
        depart: validateAndFixDate(trip.depart instanceof Date ? trip.depart : parseLocalDate(trip.depart)),
        isCalenderOpen: false,
        return: trip.return ? validateAndFixDate(
          trip.return instanceof Date ? trip.return : parseLocalDate(trip.return),
          undefined,
          trip.depart instanceof Date ? trip.depart : (parseLocalDate(trip.depart) || undefined)
        ) : null,
      }));
    } else {
      // Handle single trip (one-way/round-trip) using legacy fields
      trips = [{
        from: {
          city: searchData.from.city,
          airport: searchData.from.airport,
          iata: searchData.from.iata,
          country: searchData.from.country,
          airportOpen: false,
        },
        to: {
          city: searchData.to.city,
          airport: searchData.to.airport,
          iata: searchData.to.iata,
          country: searchData.to.country,
          airportOpen: false,
        },
        depart: validateAndFixDate(searchData.depart instanceof Date ? searchData.depart : parseLocalDate(searchData.depart)),
        isCalenderOpen: false,
        return: searchData.return ? validateAndFixDate(
          searchData.return instanceof Date ? searchData.return : parseLocalDate(searchData.return),
          undefined,
          searchData.depart instanceof Date ? searchData.depart : (parseLocalDate(searchData.depart) || undefined)
        ) : null,
      }];
    }

    return {
      FareType: fareType,
      travellers: {
        adult: searchData.passengers.adults,
        child: searchData.passengers.children,
        infant: searchData.passengers.infants,
      },
      cabin,
      SecType: fareType,
      trips,
    };
  }, [parseLocalDate, validateAndFixDate]);

  const setInitialSearchData = useCallback(() => {
    console.log('🔄 Loading initial search data...');

    // Priority 1: Navigation state (from modify search or other navigation)
    const navSearchData = location.state?.preservedSearch || location.state?.searchData;
    if (navSearchData) {
      console.log('📍 Found search data in navigation state:', navSearchData);
      try {
        const formData = convertContextToFormData(navSearchData);
        setValue("FareType", formData.FareType);
        setValue("travellers", formData.travellers);
        setValue("cabin", formData.cabin);
        setValue("SecType", formData.SecType);
        setValue("trips", formData.trips);
        console.log('✅ Loaded search data from navigation state');
        return;
      } catch (e) {
        console.error('❌ Error converting navigation search data:', e);
      }
    }

    // Priority 2: Context (current search)
    if (currentSearch && !isLoading) {
      console.log('📦 Found search data in context:', currentSearch);
      try {
        const formData = convertContextToFormData(currentSearch);
        setValue("FareType", formData.FareType);
        setValue("travellers", formData.travellers);
        setValue("cabin", formData.cabin);
        setValue("SecType", formData.SecType);
        setValue("trips", formData.trips);
        console.log('✅ Loaded search data from context');
        return;
      } catch (e) {
        console.error('❌ Error converting context search data:', e);
      }
    }

    // Priority 3: Legacy localStorage (fallback for backward compatibility)
    // Note: This is now handled by FlightSearchContext migration, but kept for safety
    const recentSearch = localStorage.getItem("recentFlightSearch");
    if (recentSearch) {
      console.log('📂 Found legacy search data in localStorage (will be migrated)');
      try {
        const parsedSearch: flight_search_form = JSON.parse(recentSearch);
        // Revive date strings into Date objects and validate they're not in the past
        const revivedTrips = parsedSearch.trips.map((trip) => {
          const departDate = validateAndFixDate(parseLocalDate(trip.depart));
          return {
            ...trip,
            depart: departDate,
            return: trip.return ? validateAndFixDate(parseLocalDate(trip.return), undefined, departDate) : null,
          };
        });

        setValue("FareType", parsedSearch.FareType);
        setValue("travellers", parsedSearch.travellers);
        setValue("cabin", parsedSearch.cabin);
        setValue("SecType", parsedSearch.SecType);
        setValue("trips", revivedTrips);

        // Clean up legacy data after successful load
        localStorage.removeItem("recentFlightSearch");
        console.log('✅ Loaded and cleaned up legacy search data from localStorage');
        return;
      } catch (e) {
        console.error("❌ Error parsing legacy recentFlightSearch:", e);
        // Clean up corrupted legacy data
        localStorage.removeItem("recentFlightSearch");
      }
    }

    // Priority 4: Default values
    console.log('📝 No saved search data found, using defaults');
    const today = new Date();
    const defaultTrip: flight_trip = {
      from: {
        city: "Kochi",
        airport: "Cochin International Airport",
        iata: "COK",
        country: "India",
        airportOpen: false,
      },
      to: {
        city: "Dubai",
        airport: "Dubai International Airport",
        iata: "DXB",
        country: "United Arab Emirates",
        airportOpen: false,
      },
      depart: today,
      isCalenderOpen: false,
      return: null,
    };

    setValue("FareType", "ON");
    setValue("travellers", {
      adult: 1,
      child: 0,
      infant: 0,
    });
    setValue("cabin", "E");
    setValue("SecType", "ON");
    setValue("trips", [defaultTrip]);
    console.log('✅ Set default search data');
  }, [setValue, location.state, currentSearch, isLoading, convertContextToFormData, parseLocalDate, validateAndFixDate]);

  const handleCloseCalender = useCallback(
    (index: number) => {
      setValue(`trips.${index}.isCalenderOpen`, false);
    },
    [setValue]
  );

  const toggleCalenderModal = useCallback((index: number, type: "DT" | "RT") => {
      const currentState = trips[index].isCalenderOpen;
      let newValue = false
      if (currentState){
        newValue = false
      }else{
        newValue = true
        setDateSelectionState(type);
      }
      setValue(`trips.${index}.isCalenderOpen`, newValue);

  },[setValue,trips]);

  const handleDateChange = useCallback(
    (date: string, index: number) => {
      const currentReturn = trips[index].return;
      const currentDeparture = trips[index].depart;

      // Create date in local timezone to avoid timezone issues
      // Parse the date string (YYYY-MM-DD) and create a local date
      const [year, month, day] = date.split('-').map(Number);
      const selectedDate = new Date(year, month - 1, day); // month is 0-indexed

      const delayedClose = () => {
        setTimeout(() => handleCloseCalender(index), 300); // 300ms delay
      };

      if (formArray.FareType === "ON" || formArray.FareType === "MC") {
        setValue(`trips.${index}.depart`, selectedDate);
        delayedClose();
        return;
      }

      if (dateSelectionState === "DT") {
        setValue(`trips.${index}.depart`, selectedDate);

        if (currentReturn instanceof Date) {
          if (selectedDate > currentReturn) {
            setValue(`trips.${index}.return`, null);
            setDateSelectionState("RT");
          } else {
            delayedClose(); // valid return, close modal
          }
        } else {
          setDateSelectionState("RT"); // no return yet, wait for it
        }
        return;
      }

      if (dateSelectionState === "RT") {
        if (
          currentDeparture instanceof Date &&
          selectedDate < currentDeparture
        ) {
          setValue(`trips.${index}.depart`, selectedDate);
          setValue(`trips.${index}.return`, null);
        } else {
          setValue(`trips.${index}.return`, selectedDate);
        }
        delayedClose();
      }
    },
    [
      dateSelectionState,
      formArray.FareType,
      handleCloseCalender,
      setValue,
      trips,
      setDateSelectionState,
    ]
  );

  const setPreviouseSearch = useCallback(
    (searchData: flight_search_form) => {
      // Revive date strings into Date objects and validate they're not in the past
      const revivedTrips = searchData.trips.map((trip) => {
        const departDate = validateAndFixDate(parseLocalDate(trip.depart));
        return {
          ...trip,
          depart: departDate,
          return: trip.return ? validateAndFixDate(parseLocalDate(trip.return), undefined, departDate) : null,
        };
      });

      setValue("travellers", searchData.travellers);
      setValue("cabin", searchData.cabin);
      setValue("SecType", searchData.SecType);
      setValue("trips", revivedTrips);
      setValue("FareType", searchData.FareType);
    },
    [setValue, parseLocalDate, validateAndFixDate]
  );

  const handleTripTypeChange = useCallback(
    (value: TripTypeValue) => {
      const current = getValues("FareType");

      if (current !== value) {
        const currentForm = getValues();

        // Save the current form to session storage
        if (current === "MC") {
          sessionStorage.setItem(
            "RecentMultiCitySearch",
            JSON.stringify(currentForm)
          );
        } else if (current === "RT") {
          sessionStorage.setItem(
            "RecentRoundTripSearch",
            JSON.stringify(currentForm)
          );
        }

        if (value === "MC") {
          const lastSearch = sessionStorage.getItem("RecentMultiCitySearch");
          if (lastSearch) {
            fire({
              icon: "info",
              title: "Load Previous Search",
              text: "Would you like to load your previous multi-city search?",
              confirmButtonText: "OK",
              position: "center",
              onConfirm: () => {
                try {
                  const parsed = JSON.parse(lastSearch);
                  setPreviouseSearch(parsed);
                } catch (e) {
                  console.error("Failed to parse previous search", e);
                }
              },
            });
          } else {
            setValue("trips.0.return", null);
          }
        } else if (value === "RT" || value === "ON") {
          const currentTrips = getValues("trips");
          const firstTrip = currentTrips[0];

          let returnDate: Date | null = null;

          if (value === "RT") {
            const lastRTSearch = sessionStorage.getItem("RecentRoundTripSearch");
            if (lastRTSearch) {
              try {
                const parsed = JSON.parse(lastRTSearch);
                if (parsed?.trips?.[0]?.return) {
                  returnDate = validateAndFixDate(parseLocalDate(parsed.trips[0].return));
                } else {
                  returnDate = new Date(); // fallback to today
                }
              } catch (e) {
                console.error("Failed to parse round trip search from localStorage", e );
                returnDate = new Date(); // fallback if parsing fails
              }
            } else {
              returnDate = new Date(); // fallback if no data
            }
          }

          if (firstTrip) {
            const updatedTrip = {
              ...firstTrip,
              return: value === "ON" ? null : returnDate,
            };

            setValue("trips", [updatedTrip]);
          } else {
            console.warn("No trip found to reset");
          }
        }

        setValue("FareType", value);
      }
  },[getValues, setValue, fire, setPreviouseSearch, parseLocalDate, validateAndFixDate]);

  // Convert flight_search_form to FlightSearchData format for context
  const convertFormToContextData = useCallback((formData: flight_search_form): FlightSearchData => {
    const firstTrip = formData.trips[0];
    const tripType = formData.FareType === 'RT' ? 'roundTrip' as const :
                    formData.FareType === 'ON' ? 'oneWay' as const : 'multiCity' as const;

    const baseData = {
      // Legacy fields for backward compatibility (use first trip)
      from: {
        city: firstTrip.from.city,
        airport: firstTrip.from.airport,
        iata: firstTrip.from.iata,
        country: firstTrip.from.country,
        airportOpen: false,
      },
      to: {
        city: firstTrip.to.city,
        airport: firstTrip.to.airport,
        iata: firstTrip.to.iata,
        country: firstTrip.to.country,
        airportOpen: false,
      },
      depart: firstTrip.depart,
      return: firstTrip.return,
      tripType,
      passengers: {
        adults: formData.travellers.adult,
        children: formData.travellers.child,
        infants: formData.travellers.infant,
      },
      class: formData.cabin === 'E' ? 'economy' as const :
             formData.cabin === 'PE' ? 'premiumEconomy' as const :
             formData.cabin === 'B' ? 'business' as const : 'first' as const,
      advanced_search: {
        selectedAirlines: [],
        flightOptions: {
          directFlights: false,
          refundableFares: false,
          corporateRates: false,
        },
        services: {
          airportLounge: false,
          extraBaggage: false,
          travelInsurance: false,
        },
        stops: 'any' as const,
        baggage: {
          carryOn: false,
          checked: false,
        },
      },
    };

    // Add trips array for multi-city searches
    if (tripType === 'multiCity' && formData.trips.length > 1) {
      return {
        ...baseData,
        trips: formData.trips.map(trip => ({
          from: {
            city: trip.from.city,
            airport: trip.from.airport,
            iata: trip.from.iata,
            country: trip.from.country,
            airportOpen: false,
          },
          to: {
            city: trip.to.city,
            airport: trip.to.airport,
            iata: trip.to.iata,
            country: trip.to.country,
            airportOpen: false,
          },
          depart: trip.depart,
          return: trip.return,
        })),
      };
    }

    return baseData;
  }, []);

  const handleSearch = useCallback(() => {
    const formData = getValues();
    console.log('🔍 Search initiated from SearchFormFlight:', formData);

    // Convert and update context
    try {
      const contextData = convertFormToContextData(formData);
      updateSearch(contextData);
      console.log('✅ Updated search context with:', contextData);
    } catch (error) {
      console.error('❌ Error updating search context:', error);
    }

    // Call parent's onSearch callback
    onSearch(formData);
  }, [onSearch, getValues, updateSearch, convertFormToContextData]);

  const toggleAirportSelecter = useCallback(
    (tripIndex: number, direction: "from" | "to") => {
      setAirportSearchTerm("");
      const current = getValues(`trips.${tripIndex}.${direction}.airportOpen`);
      setValue(`trips.${tripIndex}.${direction}.airportOpen`, !current);
    },
    [getValues, setValue]
  );

  const updateSecType = useCallback(() => {
    const allCountriesSame = formArray.trips.every(
      (flight: flight_trip) =>
        flight.from.country.toLowerCase() === flight.to.country.toLowerCase()
    );
    if (allCountriesSame) {
      setValue("SecType", "D");
    } else {
      setValue("SecType", "I");
    }
  }, [formArray.trips, setValue]);

  const scrollToOptimalPosition = (modalHeight: number = 700) => {
    // Close advanced search when any modal opens
    if (showAdvancedSearch) {
      setShowAdvancedSearch(false);
    }

    if (!formContainerRef.current) return;

    const formRect = formContainerRef.current.getBoundingClientRect();
    const viewportHeight = window.innerHeight;
    const formTop = formRect.top + window.scrollY;

    // Calculate optimal scroll position for larger calendar modal
    // We want the modal to be centered in the viewport
    const headerOffset = 40; // Account for any fixed headers
    const bottomPadding = 40; // Padding from bottom of viewport
    const availableSpace = viewportHeight - headerOffset - bottomPadding;

    let targetScrollY;

    // For larger calendar modal, we want to center it in the viewport
    if (modalHeight > availableSpace * 0.85) {
      // If modal is very large, position it near the top with some padding
      targetScrollY = Math.max(0, formTop - headerOffset);
    } else {
      // Center the modal in the viewport by positioning the form appropriately
      const modalCenterOffset = (viewportHeight - modalHeight) / 2;
      targetScrollY = Math.max(0, formTop - modalCenterOffset + 80);
    }

    // Smooth scroll to position with a slight delay to ensure modal is rendered
    setTimeout(() => {
      window.scrollTo({
        top: targetScrollY,
        behavior: "smooth",
      });
    }, 50);
  };

  const handleAirportChange = useCallback(
    (index: number, dest: string, airport: Airport) => {
      const form = trips[index];
      if (dest === "from") {
        if (airport.code !== form.to.iata) {
          setValue(`trips.${index}.from.city`, airport.city_name);
          setValue(`trips.${index}.from.airport`, airport.name);
          setValue(`trips.${index}.from.country`, airport.country);
          setValue(`trips.${index}.from.iata`, airport.code);
          setValue(`trips.${index}.from.airportOpen`, false);
          updateSecType();
        }
      } else if (dest === "to") {
        if (airport.code !== form.from.iata) {
          setValue(`trips.${index}.to.city`, airport.city_name);
          setValue(`trips.${index}.to.airport`, airport.name);
          setValue(`trips.${index}.to.country`, airport.country);
          setValue(`trips.${index}.to.iata`, airport.code);
          setValue(`trips.${index}.to.airportOpen`, false);
          updateSecType();
        }
      }
    },
    [setValue, trips, updateSecType]
  );

  const handleClassChange = useCallback(
    (value: ClassType) => {
      setValue("cabin", value);
    },
    [setValue]
  );

  const handleTravelerChange = useCallback(
    (item: "adult" | "child" | "infant", value: number) => {
      setValue(`travellers.${item}`, value);
  },[setValue]);

  const getTotalPassengers = () => {
    return travellers.adult + travellers.child + travellers.infant;
  };

  const getTravelersClassText = () => {
    return `${getTotalPassengers()} Traveler${
      getTotalPassengers() !== 1 ? "s" : ""
    }, ${classOptions.find((option) => option.id === formArray.cabin)?.label}`;
  };

  const handleRecentSearchSelect = useCallback(
    (data: flight_search_form) => {
      setValue("FareType", data.FareType);
      setValue("travellers", data.travellers);
      setValue("cabin", data.cabin);
      setValue("SecType", data.SecType);
      setValue("trips", data.trips);
  },[setValue]);

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      const target = event.target as Node;

      // Close airport dropdowns for all trips
      trips?.forEach((trip, index) => {
        const fromRef = fromAirportDropdownRefs.current[index];
        if (trip.from.airportOpen && fromRef && !fromRef.contains(target)) {
          setValue(`trips.${index}.from.airportOpen`, false);
        }

        const toRef = toAirportDropdownRefs.current[index];
        if (trip.to.airportOpen && toRef && !toRef.contains(target)) {
          setValue(`trips.${index}.to.airportOpen`, false);
        }
      });

      // Close calendars for all trips
      trips?.forEach((trip, index) => {
        if (trip.isCalenderOpen) {
          const calendarWrapper = document.querySelector(`[data-calendar-dropdown]:nth-of-type(${index + 1}) .calendar-modal-wrapper`);
          if (calendarWrapper && !calendarWrapper.contains(target)) {
            setValue(`trips.${index}.isCalenderOpen`, false);
          }
        }
      });

      // Close travelers/class dropdown
      if (
        isTravelPopUpOpen &&
        travelersClassDropdownRef.current &&
        !travelersClassDropdownRef.current.contains(target)
      ) {
        setIsTravelPopUpOpen(false);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => document.removeEventListener("mousedown", handleClickOutside);
  }, [trips, setValue, isTravelPopUpOpen]);

  useEffect(() => {
    setInitialSearchData();
  }, [setInitialSearchData]);

  useEffect(() => {
    const popularAirport = getPopularAirportFromLocal();
    if (popularAirport && popularAirport.length > 0) {
      setAirportList(popularAirport);
    } else {
      getPopularAirportList();
    }
  }, [getPopularAirportList]);

  return (
    <div className={`${compact ? "" : "max-w-7xl mx-auto px-4 py-8"}`}>
      <div
        ref={formContainerRef}
        className={`${
          compact ? "compact-form" : "bg-white rounded-2xl shadow-lg p-4 md:p-6"
        }`}
      >
        <TripTypeSelector
          tripTypes={tripTypes}
          selectedTripType={watch("FareType")}
          onTripTypeChange={(tripType: TripTypeValue) => {
            handleTripTypeChange(tripType);
          }}
          compact={compact}
        />
        {/* Multi-City Specific Layout */}
        {formArray.FareType === "MC" ? (
          <div className="multi-city-form">
            {trips &&
              trips.length > 0 &&
              trips.map((trip, index) => (
                <div className="multi-city-trip-section" key={index}>
                  {/* First Line: Airport selections and departure date */}
                  <div
                    className={`flex flex-col lg:flex-row lg:items-end gap-4 lg:gap-3 ${
                      compact ? "mb-3" : "mb-4"
                    }`}
                  >
                    {/* From */}
                <div
                  className="relative group flex-1"
                  ref={(el) => {
                    fromAirportDropdownRefs.current[index] = el;
                  }}
                >
                  <label className="block text-sm font-medium text-gray-700 mb-2 flex items-center">
                    <i className="fas fa-map-marker-alt text-blue-600 mr-2 text-xs"></i>
                    From
                  </label>
                  <div className="relative">
                    <div className="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none">
                      <i className="fas fa-plane-departure text-gray-400"></i>
                    </div>
                    <input
                      type="text"
                      className={`w-full pl-12 pr-16 py-3 border-2 rounded-xl text-sm focus:ring-2 focus:ring-blue-500/20 focus:border-blue-500 transition-all ${
                        tripError && tripError[index]?.from?.message
                          ? "border-red-500 focus:border-red-500"
                          : "border-gray-200"
                      }`}
                      placeholder="City or airport"
                      value={trip.from.airportOpen ? airportSearchTerm : trip.from.city ? (trip.from.city + ` (${trip.from.iata})`) : ''}
                      onChange={(e) => {
                        handleAirportSearchChange(e);
                      }}
                      onClick={() => {
                        toggleAirportSelecter(index, "from");
                      }}
                    />
                    <div className="absolute inset-y-0 right-0 pr-3 flex items-center space-x-1">
                      {trip.from.airport && (
                        <button
                          className="!rounded-button text-gray-400 hover:text-red-500 cursor-pointer whitespace-nowrap p-1"
                          onClick={() =>
                            handleClearAirportSelect(index, "from")
                          }
                          title="Clear selection"
                        >
                          <i className="fas fa-times text-xs"></i>
                        </button>
                      )}
                      <button
                        className="!rounded-button text-gray-400 hover:text-gray-600 cursor-pointer whitespace-nowrap p-1"
                        onClick={() => {
                          toggleAirportSelecter(index, "from");
                          if (!trip.from.airportOpen) {
                            setTimeout(() => scrollToOptimalPosition(300), 100);
                          }
                        }}
                      >
                        <i
                          className={`fas fa-chevron-down transition-transform ${
                            trip.from.airportOpen ? "rotate-180" : ""
                          }`}
                        ></i>
                      </button>
                    </div>
                  </div>
                  {trip.from.airportOpen && (
                    <div className="absolute left-0 mt-1 w-full bg-white rounded-xl shadow-xl z-[9999] max-h-80 overflow-y-auto">
                      <AirportSearch
                        searchTerm={airportSearchTerm}
                        airports={airportList}
                        showDropdown={trip.from.airportOpen}
                        onAirportSelect={(airport) =>
                          handleAirportChange(index, "from", airport)
                        }
                        icon="fas fa-map-marker-alt"
                        isLoading={isAirportShimmer}
                        disabledAirportCode={trip.to.iata}
                      />
                    </div>
                  )}
                  {/* Error message for From airport */}
                  {tripError && tripError[index]?.from?.message && (
                    <div className="mt-1 text-sm text-red-600 flex items-center">
                      <i className="fas fa-exclamation-circle mr-1"></i>
                      {tripError[index]?.from?.message}
                    </div>
                  )}
                </div>

                {/* Swap Button - Inline with fields */}
                <div className="hidden sm:flex items-end pb-3">
                  <button
                    type="button"
                    className="!rounded-button w-8 h-8 flex items-center justify-center bg-white border-2 border-gray-200 rounded-full text-gray-400 hover:text-blue-600 hover:border-blue-300 hover:bg-blue-50 transition-all cursor-pointer shadow-sm hover:shadow-md"
                    onClick={() => handleSwapAirports(index)}
                    title="Swap departure and destination airports"
                  >
                    <i className="fas fa-exchange-alt text-xs"></i>
                  </button>
                </div>

                {/* To */}
                <div
                  className="relative group flex-1"
                  ref={(el) => {
                    toAirportDropdownRefs.current[index] = el;
                  }}
                >
                  <label className="block text-sm font-medium text-gray-700 mb-2 flex items-center">
                    <i className="fas fa-map-marker text-blue-600 mr-2 text-xs"></i>
                    To
                  </label>
                  <div className="relative">
                    <div className="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none">
                      <i className="fas fa-plane-arrival text-gray-400"></i>
                    </div>
                    <input
                      type="text"
                      className={`w-full pl-12 pr-16 py-3 border-2 rounded-xl text-sm focus:ring-2 focus:ring-blue-500/20 focus:border-blue-500 transition-all ${
                        tripError && tripError[index]?.to?.message
                          ? "border-red-500 focus:border-red-500"
                          : "border-gray-200"
                      }`}
                      placeholder="City or airport"
                      value={trip.to.airportOpen ? airportSearchTerm : trip.to.city ? (trip.to.city + ` (${trip.to.iata})`) : ''}
                      onChange={(e) => {
                        handleAirportSearchChange(e);
                      }}
                      onClick={() => {
                        toggleAirportSelecter(index, "to");
                      }}
                    />
                    <div className="absolute inset-y-0 right-0 pr-3 flex items-center space-x-1">
                      {trip.to.airport && (
                        <button
                          className="!rounded-button text-gray-400 hover:text-red-500 cursor-pointer whitespace-nowrap p-1"
                          onClick={() => handleClearAirportSelect(index, "to")}
                          title="Clear selection"
                        >
                          <i className="fas fa-times text-xs"></i>
                        </button>
                      )}
                      <button
                        className="!rounded-button text-gray-400 hover:text-gray-600 cursor-pointer whitespace-nowrap p-1"
                        onClick={() => {
                          toggleAirportSelecter(index, "from");
                          if (!trip.from.airportOpen) {
                            setTimeout(() => scrollToOptimalPosition(300), 100);
                          }
                        }}
                      >
                        <i
                          className={`fas fa-chevron-down transition-transform ${
                            trip.to.airportOpen ? "rotate-180" : ""
                          }`}
                        ></i>
                      </button>
                    </div>
                  </div>
                  {trip.to.airportOpen && (
                    <div className="absolute left-0 mt-1 w-full bg-white rounded-xl shadow-xl z-[9999] max-h-80 overflow-y-auto">
                      <AirportSearch
                        searchTerm={airportSearchTerm}
                        airports={airportList}
                        showDropdown={trip.to.airportOpen}
                        onAirportSelect={(airport) =>
                          handleAirportChange(index, "to", airport)
                        }
                        icon="fas fa-map-marker-alt"
                        isLoading={isAirportShimmer}
                        disabledAirportCode={trip.from.iata}
                      />
                    </div>
                  )}
                  {/* Error message for To airport */}
                  {tripError && tripError[index]?.to?.message && (
                    <div className="mt-1 text-sm text-red-600 flex items-center">
                      <i className="fas fa-exclamation-circle mr-1"></i>
                      {tripError && tripError[index]?.to?.message}
                    </div>
                  )}
                </div>

                    {/* Departure Date - Multi-city specific */}
                    <div className="flex-1 relative" data-calendar-dropdown>
                      <label className="block text-sm font-medium text-gray-700 mb-2 flex items-center">
                        <i className="fas fa-calendar-alt text-blue-600 mr-2 text-xs"></i>
                        Departure
                      </label>
                      <div className="relative">
                        <div className="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none z-10">
                          <i className="fas fa-plane-departure text-gray-400"></i>
                        </div>
                        <input
                          type="text"
                          className={`w-full pl-12 pr-12 py-3 border-2 rounded-xl text-sm focus:ring-2 focus:ring-blue-500/20 focus:border-blue-500 transition-all cursor-pointer flex items-center ${
                            tripError && tripError[index]?.depart
                              ? "border-red-500 focus:border-red-500"
                              : "border-gray-200"
                          }`}
                          value={formatShortFriendlyDate(trip.depart)}
                          onClick={() => {
                            setDateSelectionState("DT");
                            toggleCalenderModal(index, "DT");
                          }}
                          readOnly
                        />
                        <div className="absolute inset-y-0 right-0 pr-4 flex items-center pointer-events-none z-10">
                          <i className="fas fa-chevron-down text-gray-400"></i>
                        </div>
                      </div>
                      {/* Error message for departure date */}
                      {tripError && tripError[index]?.depart?.message && (
                        <div className="mt-1 text-sm text-red-600 flex items-center">
                          <i className="fas fa-exclamation-circle mr-1"></i>
                          {tripError[index]?.depart?.message}
                        </div>
                      )}

                      {/* Calendar Modal positioned under departure date */}
                      {trip.isCalenderOpen && (
                        <div className="absolute right-0 mt-1 bg-white rounded-xl shadow-xl z-[9999] overflow-y-auto w-auto calendar-modal-wrapper">
                          <CalendarModal
                            showCalendar={trip.isCalenderOpen}
                            selectedTripType={formArray.FareType}
                            dateSelectionState={dateSelectionState}
                            departureDate={
                              trip.depart ? formatLocalDate(trip.depart) : ""
                            }
                            returnDate={
                              trip.return ? formatLocalDate(trip.return) : ""
                            }
                            onDateSelect={(date) => handleDateChange(date, index)}
                            onClose={() => handleCloseCalender(index)}
                          />
                        </div>
                      )}
                    </div>

                    {/* Remove Trip Button - Only show if more than 1 trip */}
                    {trips.length > 1 && (
                      <div className="flex items-end pb-3">
                        <button
                          type="button"
                          className="!rounded-button w-8 h-8 flex items-center justify-center bg-white border-2 border-red-200 rounded-full text-red-400 hover:text-red-600 hover:border-red-300 hover:bg-red-50 transition-all cursor-pointer shadow-sm hover:shadow-md"
                          onClick={() => removeTrip(index)}
                          title="Remove this trip"
                        >
                          <i className="fas fa-times text-xs"></i>
                        </button>
                      </div>
                    )}
                  </div>

                  {/* Second Line: Add City/Stop Button - Only show for last trip */}
                  {index === trips.length - 1 && (
                    <div className={`flex justify-center ${compact ? "mb-3" : "mb-4"}`}>
                      <button
                        type="button"
                        className="!rounded-button flex items-center px-6 py-3 bg-blue-600 hover:bg-blue-700 text-white rounded-xl text-sm font-medium transition-all cursor-pointer shadow-sm hover:shadow-md"
                        onClick={addTrip}
                        title="Add another city/stop"
                      >
                        <i className="fas fa-plus mr-2 text-xs"></i>
                        <span>Add City/Stop</span>
                      </button>
                    </div>
                  )}
                </div>
              ))}

            {/* Third Line: Advanced Search and Travel Class - Only show once for all trips */}
            <div className={`flex flex-col lg:flex-row lg:items-end gap-4 lg:gap-6 ${compact ? "mb-4" : "mb-6"}`}>
              {/* Advanced Search Toggle */}
              <div className="flex-1">
                <button
                  type="button"
                  className={`!rounded-button w-full flex items-center justify-center px-4 py-3 border-2 rounded-xl text-sm transition-all cursor-pointer h-[52px] ${
                    showAdvancedSearch
                      ? "border-blue-500 bg-blue-50 text-blue-700"
                      : "border-gray-200 hover:border-gray-300 text-gray-700 hover:bg-gray-50"
                  }`}
                  onClick={() => setShowAdvancedSearch(!showAdvancedSearch)}
                  title="Advanced Search Options"
                >
                  <i
                    className={`fas fa-sliders-h mr-2 text-xs ${
                      showAdvancedSearch ? "text-blue-600" : "text-gray-400"
                    }`}
                  ></i>
                  <span className="font-medium">Advanced Search</span>
                  {getAdvancedSearchCount() > 0 && (
                    <span className="ml-2 bg-blue-600 text-white text-xs font-semibold px-1.5 py-0.5 rounded-full min-w-[18px] text-center">
                      {getAdvancedSearchCount()}
                    </span>
                  )}
                  <i
                    className={`fas fa-chevron-down ml-2 text-xs transition-transform ${
                      showAdvancedSearch
                        ? "rotate-180 text-blue-600"
                        : "text-gray-400"
                    }`}
                  ></i>
                </button>
              </div>

              {/* Travel Class */}
              <div
                className="flex-1 relative"
                ref={travelersClassDropdownRef}
                data-travelers-dropdown
              >
                <label className="block text-sm font-medium text-gray-700 mb-2 flex items-center">
                  <i className="fas fa-users text-blue-600 mr-2 text-xs"></i>
                  Travelers & Class
                </label>
                <button
                  type="button"
                  className="!rounded-button w-full flex items-center justify-between pl-4 pr-4 py-3 border-2 border-gray-200 rounded-xl text-sm focus:ring-2 focus:ring-blue-500/20 focus:border-blue-500 transition-all cursor-pointer"
                  onClick={() => {
                    setIsTravelPopUpOpen(!isTravelPopUpOpen);
                  }}
                >
                  <div className="flex items-center">
                    <i className="fas fa-users text-gray-400 mr-3"></i>
                    <span>{getTravelersClassText()}</span>
                  </div>
                  <i
                    className={`fas fa-chevron-down text-gray-400 transition-transform ${
                      isTravelPopUpOpen ? "rotate-180" : ""
                    }`}
                  ></i>
                </button>
                {isTravelPopUpOpen && (
                  <div className="absolute left-0 right-0 mt-1 bg-white rounded-xl shadow-xl z-[9999] overflow-y-auto travel-class-wrapper">
                    <TravelClassPicker
                      passengers={formArray.travellers}
                      selectedClass={formArray.cabin}
                      classOptions={classOptions}
                      showDropdown={isTravelPopUpOpen}
                      onPassengerChange={handleTravelerChange}
                      onClassChange={handleClassChange}
                      onApply={() => setIsTravelPopUpOpen(false)}
                    />
                  </div>
                )}
              </div>
            </div>

            {/* Advanced Search Options - Appears below the third line */}
            {showAdvancedSearch && (
              <AdvancedSearchContent
                data={searchData}
                showToast={showToast}
                airlines={airlineList}
                onAirlineToggle={handleAirlineToggle}
                onFlightOptionToggle={handleFlightOptionToggle}
                onServiceToggle={handleServiceToggle}
                onExtraBaggageChange={handleExtraBaggageChange}
                onResetFilters={handleResetFilters}
              />
            )}
          </div>
        ) : (
          /* Regular One-Way/Round-Trip Layout */
          trips &&
          trips.length > 0 &&
          trips.map((trip, index) => (
            <div className="flight-search-section" key={index}>
              <div
                className={`flex flex-col sm:flex-row sm:items-end gap-4 sm:gap-3 ${
                  compact ? "mb-3" : "mb-4"
                }`}
              >
                {/* From */}
                <div
                  className="relative group flex-1"
                  ref={(el) => {
                    fromAirportDropdownRefs.current[index] = el;
                  }}
                >
                  <label className="block text-sm font-medium text-gray-700 mb-2 flex items-center">
                    <i className="fas fa-map-marker-alt text-blue-600 mr-2 text-xs"></i>
                    From
                  </label>
                  <div className="relative">
                    <div className="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none">
                      <i className="fas fa-plane-departure text-gray-400"></i>
                    </div>
                    <input
                      type="text"
                      className={`w-full pl-12 pr-16 py-3 border-2 rounded-xl text-sm focus:ring-2 focus:ring-blue-500/20 focus:border-blue-500 transition-all ${
                        tripError && tripError[index]?.from?.message
                          ? "border-red-500 focus:border-red-500"
                          : "border-gray-200"
                      }`}
                      placeholder="City or airport"
                      value={trip.from.airportOpen ? airportSearchTerm : trip.from.city ? (trip.from.city + ` (${trip.from.iata})`) : ''}
                      onChange={(e) => {
                        handleAirportSearchChange(e);
                      }}
                      onClick={() => {
                        toggleAirportSelecter(index, "from");
                      }}
                    />
                    <div className="absolute inset-y-0 right-0 pr-3 flex items-center space-x-1">
                      {trip.from.airport && (
                        <button
                          className="!rounded-button text-gray-400 hover:text-red-500 cursor-pointer whitespace-nowrap p-1"
                          onClick={() =>
                            handleClearAirportSelect(index, "from")
                          }
                          title="Clear selection"
                        >
                          <i className="fas fa-times text-xs"></i>
                        </button>
                      )}
                      <button
                        className="!rounded-button text-gray-400 hover:text-gray-600 cursor-pointer whitespace-nowrap p-1"
                        onClick={() => {
                          toggleAirportSelecter(index, "from");
                          if (!trip.from.airportOpen) {
                            setTimeout(() => scrollToOptimalPosition(300), 100);
                          }
                        }}
                      >
                        <i
                          className={`fas fa-chevron-down transition-transform ${
                            trip.from.airportOpen ? "rotate-180" : ""
                          }`}
                        ></i>
                      </button>
                    </div>
                  </div>
                  {trip.from.airportOpen && (
                    <div className="absolute left-0 mt-1 w-full bg-white rounded-xl shadow-xl z-[9999] max-h-80 overflow-y-auto">
                      <AirportSearch
                        searchTerm={airportSearchTerm}
                        airports={airportList}
                        showDropdown={trip.from.airportOpen}
                        onAirportSelect={(airport) =>
                          handleAirportChange(index, "from", airport)
                        }
                        icon="fas fa-map-marker-alt"
                        isLoading={isAirportShimmer}
                        disabledAirportCode={trip.to.iata}
                      />
                    </div>
                  )}
                  {/* Error message for From airport */}
                  {tripError && tripError[index]?.from?.message && (
                    <div className="mt-1 text-sm text-red-600 flex items-center">
                      <i className="fas fa-exclamation-circle mr-1"></i>
                      {tripError[index]?.from?.message}
                    </div>
                  )}
                </div>

                {/* Swap Button - Inline with fields */}
                <div className="hidden sm:flex items-end pb-3">
                  <button
                    type="button"
                    className="!rounded-button w-8 h-8 flex items-center justify-center bg-white border-2 border-gray-200 rounded-full text-gray-400 hover:text-blue-600 hover:border-blue-300 hover:bg-blue-50 transition-all cursor-pointer shadow-sm hover:shadow-md"
                    onClick={() => handleSwapAirports(index)}
                    title="Swap departure and destination airports"
                  >
                    <i className="fas fa-exchange-alt text-xs"></i>
                  </button>
                </div>

                {/* To */}
                <div
                  className="relative group flex-1"
                  ref={(el) => {
                    toAirportDropdownRefs.current[index] = el;
                  }}
                >
                  <label className="block text-sm font-medium text-gray-700 mb-2 flex items-center">
                    <i className="fas fa-map-marker text-blue-600 mr-2 text-xs"></i>
                    To
                  </label>
                  <div className="relative">
                    <div className="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none">
                      <i className="fas fa-plane-arrival text-gray-400"></i>
                    </div>
                    <input
                      type="text"
                      className={`w-full pl-12 pr-16 py-3 border-2 rounded-xl text-sm focus:ring-2 focus:ring-blue-500/20 focus:border-blue-500 transition-all ${
                        tripError && tripError[index]?.to?.message
                          ? "border-red-500 focus:border-red-500"
                          : "border-gray-200"
                      }`}
                      placeholder="City or airport"
                      value={trip.to.airportOpen ? airportSearchTerm : trip.to.city ? (trip.to.city + ` (${trip.to.iata})`) : ''}
                      onChange={(e) => {
                        handleAirportSearchChange(e);
                      }}
                      onClick={() => {
                        toggleAirportSelecter(index, "to");
                      }}
                    />
                    <div className="absolute inset-y-0 right-0 pr-3 flex items-center space-x-1">
                      {trip.to.airport && (
                        <button
                          className="!rounded-button text-gray-400 hover:text-red-500 cursor-pointer whitespace-nowrap p-1"
                          onClick={() => handleClearAirportSelect(index, "to")}
                          title="Clear selection"
                        >
                          <i className="fas fa-times text-xs"></i>
                        </button>
                      )}
                      <button
                        className="!rounded-button text-gray-400 hover:text-gray-600 cursor-pointer whitespace-nowrap p-1"
                        onClick={() => {
                          toggleAirportSelecter(index, "to");
                          if (!trip.to.airportOpen) {
                            setTimeout(() => scrollToOptimalPosition(300), 100);
                          }
                        }}
                      >
                        <i
                          className={`fas fa-chevron-down transition-transform ${
                            trip.to.airportOpen ? "rotate-180" : ""
                          }`}
                        ></i>
                      </button>
                    </div>
                  </div>
                  {trip.to.airportOpen && (
                    <div className="absolute left-0 mt-1 w-full bg-white rounded-xl shadow-xl z-[9999] max-h-80 overflow-y-auto">
                      <AirportSearch
                        searchTerm={airportSearchTerm}
                        airports={airportList}
                        showDropdown={trip.to.airportOpen}
                        onAirportSelect={(airport) =>
                          handleAirportChange(index, "to", airport)
                        }
                        icon="fas fa-map-marker-alt"
                        isLoading={isAirportShimmer}
                        disabledAirportCode={trip.from.iata}
                      />
                    </div>
                  )}
                  {/* Error message for To airport */}
                  {tripError && tripError[index]?.to?.message && (
                    <div className="mt-1 text-sm text-red-600 flex items-center">
                      <i className="fas fa-exclamation-circle mr-1"></i>
                      {tripError && tripError[index]?.to?.message}
                    </div>
                  )}
                </div>

                {/* Advanced Search Toggle - Compact Button Style */}
                <div className="flex items-end">
                  <button
                    type="button"
                    className={`!rounded-button flex items-center px-4 py-3 border-2 rounded-xl text-sm transition-all cursor-pointer whitespace-nowrap h-[52px] ${
                      showAdvancedSearch
                        ? "border-blue-500 bg-blue-50 text-blue-700"
                        : "border-gray-200 hover:border-gray-300 text-gray-700 hover:bg-gray-50"
                    }`}
                    onClick={() => setShowAdvancedSearch(!showAdvancedSearch)}
                    title="Advanced Search Options"
                  >
                    <i
                      className={`fas fa-sliders-h mr-2 text-xs ${
                        showAdvancedSearch ? "text-blue-600" : "text-gray-400"
                      }`}
                    ></i>
                    <span className="font-medium">Options</span>
                    {getAdvancedSearchCount() > 0 && (
                      <span className="ml-2 bg-blue-600 text-white text-xs font-semibold px-1.5 py-0.5 rounded-full min-w-[18px] text-center">
                        {getAdvancedSearchCount()}
                      </span>
                    )}
                    <i
                      className={`fas fa-chevron-down ml-2 text-xs transition-transform ${
                        showAdvancedSearch
                          ? "rotate-180 text-blue-600"
                          : "text-gray-400"
                      }`}
                    ></i>
                  </button>
                </div>
              </div>

              {/* Advanced Search Options - Appears right below airport selection */}
              {showAdvancedSearch && (
                <AdvancedSearchContent
                  data={searchData}
                  showToast={showToast}
                  airlines={airlineList}
                  onAirlineToggle={handleAirlineToggle}
                  onFlightOptionToggle={handleFlightOptionToggle}
                  onServiceToggle={handleServiceToggle}
                  onExtraBaggageChange={handleExtraBaggageChange}
                  onResetFilters={handleResetFilters}
                />
              )}

              {/* Date Selection and Travelers/Class - Redesigned Layout */}
              <div
                className={`relative grid grid-cols-1 md:grid-cols-2 gap-4 md:gap-6 ${
                  compact ? "mb-4" : "mb-6"
                }`}
              >
                {/* Dates Container */}
                <div className="w-full">
                  {/* Date Container - Improved Alignment */}
                  <div className="flex items-start space-x-4">
                    {/* Departure Date */}
                    <div className="flex-1 relative" data-calendar-dropdown>
                      <label className="block text-sm font-medium text-gray-700 mb-2 flex items-center">
                        <i className="fas fa-calendar-alt text-blue-600 mr-2 text-xs"></i>
                        Departure
                      </label>
                      <div className="relative">
                        <div className="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none z-10">
                          <i className="fas fa-plane-departure text-gray-400"></i>
                        </div>
                        <input
                          type="text"
                          className={`w-full pl-12 pr-12 py-3 border-2 rounded-xl text-sm focus:ring-2 focus:ring-blue-500/20 focus:border-blue-500 transition-all cursor-pointer flex items-center ${
                            tripError && tripError[index]?.depart
                              ? "border-red-500 focus:border-red-500"
                              : "border-gray-200"
                          }`}
                          value={formatShortFriendlyDate(trip.depart)}
                          onClick={() => {
                            setDateSelectionState("DT");
                            toggleCalenderModal(index, "DT");
                          }}
                          readOnly
                        />
                        <div className="absolute inset-y-0 right-0 pr-4 flex items-center pointer-events-none z-10">
                          <i className="fas fa-chevron-down text-gray-400"></i>
                        </div>
                      </div>
                      {/* Error message for departure date */}
                      {tripError && tripError[index]?.depart?.message && (
                        <div className="mt-1 text-sm text-red-600 flex items-center">
                          <i className="fas fa-exclamation-circle mr-1"></i>
                          {tripError[index]?.depart?.message}
                        </div>
                      )}

                      {/* Calendar Modal positioned under departure date */}
                      {trip.isCalenderOpen && (
                        <div className="absolute left-0 right-0 mt-1 bg-white rounded-xl shadow-xl z-[9999] overflow-y-auto min-w-[700px] md:left-0 md:right-auto md:w-auto md:min-w-[700px] md:max-w-[900px] calendar-modal-wrapper">
                          <CalendarModal
                            showCalendar={trip.isCalenderOpen}
                            selectedTripType={formArray.FareType}
                            dateSelectionState={dateSelectionState}
                            departureDate={
                              trip.depart ? formatLocalDate(trip.depart) : ""
                            }
                            returnDate={
                              trip.return ? formatLocalDate(trip.return) : ""
                            }
                            onDateSelect={(date) => handleDateChange(date, index)}
                            onClose={() => handleCloseCalender(index)}
                          />
                        </div>
                      )}

                    </div>

                    {/* Return Date */}
                    {formArray.FareType === "RT" ? (
                      <div className="flex-1 relative">
                        <label className="block text-sm font-medium text-gray-700 mb-2 flex items-center">
                          <i className="fas fa-calendar-alt text-blue-600 mr-2 text-xs"></i>
                          Return
                        </label>
                        <div className="relative">
                          <div className="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none z-10">
                            <i className="fas fa-plane-arrival text-gray-400"></i>
                          </div>
                          <input
                            type="text"
                            className={`w-full pl-12 pr-12 py-3 border-2 rounded-xl text-sm focus:ring-2 focus:ring-blue-500/20 focus:border-blue-500 transition-all cursor-pointer flex items-center ${
                              tripError && tripError[index]?.return
                                ? "border-red-500 focus:border-red-500"
                                : "border-gray-200"
                            }`}
                            value={formatShortFriendlyDate(trip.return)}
                            onClick={() => {
                              setDateSelectionState("RT");
                              toggleCalenderModal(index, "RT");
                            }}
                            readOnly
                          />
                          <div className="absolute inset-y-0 right-0 pr-4 flex items-center pointer-events-none z-10">
                            <i className="fas fa-chevron-down text-gray-400"></i>
                          </div>
                        </div>
                        {/* Error message for return date */}
                        {tripError && tripError[index]?.return && (
                          <div className="mt-1 text-sm text-red-600 flex items-center">
                            <i className="fas fa-exclamation-circle mr-1"></i>
                            {tripError[index]?.return?.message}
                          </div>
                        )}

                        {/* Trip Duration below return date - Enhanced styling */}
                        {trip.depart && trip.return && (
                            <div className="mt-3 px-1">
                              <div className="inline-flex items-center bg-blue-50 px-3 py-1.5 rounded-lg border border-blue-200">
                                <i className="fas fa-calendar-check text-blue-600 mr-2 text-xs"></i>
                                <span className="text-xs font-semibold text-blue-700">
                                  {(() => {
                                    // Use the Date objects directly to avoid timezone issues
                                    const departureDate = new Date(
                                      trip.depart.getFullYear(),
                                      trip.depart.getMonth(),
                                      trip.depart.getDate()
                                    );

                                    const returnDate = new Date(
                                      trip.return.getFullYear(),
                                      trip.return.getMonth(),
                                      trip.return.getDate()
                                    );

                                    const timeDiff =
                                      returnDate.getTime() -
                                      departureDate.getTime();
                                    const days = Math.ceil(
                                      timeDiff / (1000 * 60 * 60 * 24)
                                    );

                                    if (days === 0) {
                                      return "Same-day return trip";
                                    } else {
                                      return `${days} day${
                                        days !== 1 ? "s" : ""
                                      } trip`;
                                    }
                                  })()}
                                </span>
                              </div>
                            </div>
                          )}
                      </div>
                    ) : (
                      <div className="flex-1 relative">
                        <label className="block text-sm font-medium text-gray-700 mb-2 flex items-center">
                          <i className="fas fa-calendar-alt text-blue-600 mr-2 text-xs opacity-50"></i>
                          Return
                        </label>
                        <div className="relative">
                          <div className="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none z-10">
                            <i className="fas fa-plane-arrival text-gray-400 opacity-50"></i>
                          </div>
                          <input
                            type="text"
                            className="w-full pl-12 pr-12 py-3 border-2 border-gray-200 rounded-xl text-sm bg-gray-50 opacity-60 text-gray-400 flex items-center"
                            value="N/A"
                            readOnly
                          />
                          <div className="absolute inset-y-0 right-0 pr-4 flex items-center pointer-events-none z-10">
                            <i className="fas fa-ban text-gray-400 opacity-50"></i>
                          </div>
                        </div>
                      </div>
                    )}
                  </div>
                </div>

                {/* Travelers and Class Combined */}
                <div
                  className="relative w-full"
                  ref={travelersClassDropdownRef}
                  data-travelers-dropdown
                >
                  <label className="block text-sm font-medium text-gray-700 mb-2 flex items-center">
                    <i className="fas fa-users text-blue-600 mr-2 text-xs"></i>
                    Travelers & Class
                  </label>
                  <button
                    type="button"
                    className="!rounded-button w-full flex items-center justify-between pl-4 pr-4 py-3 border-2 border-gray-200 rounded-xl text-sm focus:ring-2 focus:ring-blue-500/20 focus:border-blue-500 transition-all cursor-pointer"
                    onClick={() => {
                      setIsTravelPopUpOpen(!isTravelPopUpOpen);
                    }}
                  >
                    <div className="flex items-center">
                      <i className="fas fa-users text-gray-400 mr-3"></i>
                      <span>{getTravelersClassText()}</span>
                    </div>
                    <i
                      className={`fas fa-chevron-down text-gray-400 transition-transform ${
                        isTravelPopUpOpen ? "rotate-180" : ""
                      }`}
                    ></i>
                  </button>
                  {isTravelPopUpOpen && (
                    <div className="absolute left-0 right-0 mt-1 bg-white rounded-xl shadow-xl z-[9999] overflow-y-auto travel-class-wrapper">
                      <TravelClassPicker
                        passengers={formArray.travellers}
                        selectedClass={formArray.cabin}
                        classOptions={classOptions}
                        showDropdown={isTravelPopUpOpen}
                        onPassengerChange={handleTravelerChange}
                        onClassChange={handleClassChange}
                        onApply={() => setIsTravelPopUpOpen(false)}
                      />
                    </div>
                  )}
                </div>
              </div>
            </div>
          ))
        )}

        <SearchButton onSearch={handleSearch} />

        {/* Recent Searches */}
        <RecentSearches onSearchSelect={handleRecentSearchSelect} />
      </div>
    </div>
  );
}

export default SearchFormFlight;
