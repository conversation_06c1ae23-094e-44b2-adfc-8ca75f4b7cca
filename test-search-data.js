// Test script to add search data to localStorage
// Run this in browser console to simulate a search

const testSearchData = {
  from: {
    city: 'New York',
    airport: 'John F. Kennedy International Airport',
    iata: 'JFK',
    country: 'United States',
    airportOpen: false
  },
  to: {
    city: 'Los Angeles',
    airport: 'Los Angeles International Airport',
    iata: 'LAX',
    country: 'United States',
    airportOpen: false
  },
  depart: new Date('2024-07-15').toISOString(),
  return: new Date('2024-07-22').toISOString(),
  tripType: 'roundTrip',
  passengers: {
    adults: 2,
    children: 0,
    infants: 0
  },
  class: 'economy',
  advanced_search: {
    selectedAirlines: [],
    flightOptions: {
      directFlights: false,
      refundableFares: false,
      corporateRates: false
    },
    services: {
      airportLounge: false,
      extraBaggage: false,
      travelInsurance: false
    },
    stops: 'any',
    baggage: {
      carryOn: false,
      checked: false
    }
  }
};

localStorage.setItem('flightSearchData', JSON.stringify(testSearchData));
console.log('Test search data added to localStorage');
console.log('Now navigate to: http://localhost:5174/flight/list');
