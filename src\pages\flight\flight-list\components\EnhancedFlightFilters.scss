/* Enhanced Flight Filters Styling */
@use '../../../../styles/variables' as *;

.enhanced-flight-filters {
  overflow: hidden;

  * {
    box-sizing: border-box;
  }
  /* Custom scrollbar styling */
  .custom-scrollbar {
    /* Hide scrollbar by default, show on hover */
    scrollbar-width: none; /* Firefox */
    -ms-overflow-style: none; /* IE and Edge */

    &::-webkit-scrollbar {
      width: 0px;
      background: transparent;
    }

    /* Show minimal scrollbar on hover */
    &:hover {
      &::-webkit-scrollbar {
        width: 4px;
      }

      &::-webkit-scrollbar-track {
        background: transparent;
      }

      &::-webkit-scrollbar-thumb {
        background: rgba(0, 0, 0, 0.2);
        border-radius: 2px;

        &:hover {
          background: rgba(0, 0, 0, 0.3);
        }
      }

      /* Firefox */
      scrollbar-width: thin;
      scrollbar-color: rgba(0, 0, 0, 0.2) transparent;
    }

    /* Focus state for accessibility */
    &:focus {
      &::-webkit-scrollbar {
        width: 4px;
      }

      &::-webkit-scrollbar-thumb {
        background: $primary_color;
        opacity: 0.6;
      }

      scrollbar-width: thin;
      scrollbar-color: $primary_color transparent;
    }

    /* Smooth scrolling */
    scroll-behavior: smooth;
  }

  /* Responsive width adjustments */
  @media (max-width: 1400px) {
    max-width: 320px;
  }

  @media (max-width: 1200px) {
    max-width: 280px;
  }

  @media (max-width: 1024px) {
    max-width: 260px;
  }

  @media (max-width: 768px) {
    max-width: 100%;
    width: 100%;
  }

  /* Prevent grid items from overflowing */
  .filter-buttons-grid button {
    min-width: 0;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  /* Filter section spacing */
  .filter-section {
    &:not(:last-child) {
      margin-bottom: 1.25rem;
    }

    &:last-child {
      margin-bottom: 0;
    }
  }

  /* Horizontal filter buttons */
  .filter-buttons-horizontal {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;

    button {
      flex: 0 0 auto;
      white-space: nowrap;
      min-width: fit-content;
      padding: 0.5rem 0.75rem;
      font-size: 0.875rem;
      font-weight: 500;
      border-radius: 0.375rem;
      transition: all 0.15s ease;

      &:hover {
        transform: none;
        box-shadow: none;
      }
    }
  }

  /* Grid filter buttons */
  .filter-buttons-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 0.5rem;
    overflow: hidden;

    button {
      padding: 0.75rem 0.5rem;
      font-size: 0.8rem;
      font-weight: 500;
      border-radius: 0.375rem;
      transition: all 0.15s ease;
      min-height: 3.5rem;
      min-width: 0;
      overflow: hidden;

      &:hover {
        transform: none;
        box-shadow: none;
      }

      .flex {
        gap: 0.25rem;
        overflow: hidden;
      }

      i {
        font-size: 0.875rem;
        opacity: 0.8;
        flex-shrink: 0;
      }

      span {
        line-height: 1.2;
        overflow: hidden;
        text-overflow: ellipsis;

        &.font-medium {
          font-weight: 600;
        }

        &.opacity-75 {
          opacity: 0.7;
          font-size: 0.75rem;
        }
      }
    }

    @media (max-width: 480px) {
      grid-template-columns: 1fr;
    }
  }

  /* Checkbox list styling */
  .checkbox-list {
    max-height: 160px;
    overflow-y: auto;

    &.custom-scrollbar {
      padding-right: 0.25rem;
      margin-right: -0.25rem;
    }

    label {
      display: flex;
      align-items: center;
      padding: 0.5rem 0;
      cursor: pointer;
      transition: background-color 0.15s ease;
      border-radius: 0.25rem;

      &:hover {
        background-color: rgba(59, 130, 246, 0.05);
      }

      input[type="checkbox"] {
        margin-right: 0.5rem;
        flex-shrink: 0;
        width: 0.875rem;
        height: 0.875rem;
        accent-color: #2563eb;
      }

      span {
        font-size: 0.875rem;
        font-weight: 400;
        line-height: 1.25;
        word-break: break-word;
        color: #374151;
      }
    }
  }

  /* Price range slider styling */
  .price-range-slider {
    overflow: hidden;

    input[type="range"] {
      width: 100%;
      height: 8px;
      border-radius: 4px;
      background: #e5e7eb;
      outline: none;
      -webkit-appearance: none;

      &::-webkit-slider-thumb {
        -webkit-appearance: none;
        appearance: none;
        width: 20px;
        height: 20px;
        border-radius: 50%;
        background: $primary_color;
        cursor: pointer;
        border: 2px solid white;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
      }

      &::-moz-range-thumb {
        width: 20px;
        height: 20px;
        border-radius: 50%;
        background: $primary_color;
        cursor: pointer;
        border: 2px solid white;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
      }
    }

    .flex {
      justify-content: space-between;
      margin-top: 0.5rem;
      overflow: hidden;

      span {
        font-size: 0.75rem;
        color: #6b7280;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        flex-shrink: 1;
        min-width: 0;
      }
    }
  }

  /* Filter header styling */
  .filter-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 1rem;

    h3 {
      font-size: 1.125rem;
      font-weight: 600;
      color: #111827;
      margin: 0;
    }

    button {
      font-size: 0.75rem;
      font-weight: 500;
      color: #2563eb;
      background: none;
      border: none;
      cursor: pointer;
      transition: color 0.2s ease;

      &:hover {
        color: #1d4ed8;
      }
    }
  }

  /* Filter section titles */
  .filter-section-title {
    font-size: 0.875rem;
    font-weight: 500;
    color: #111827;
    margin-bottom: 0.5rem;
    display: block;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  /* Active filters count */
  .active-filters-count {
    padding-top: 1rem;
    border-top: 1px solid #e5e7eb;

    .count-text {
      font-size: 0.75rem;
      color: #6b7280;
      text-align: center;
    }
  }
}
