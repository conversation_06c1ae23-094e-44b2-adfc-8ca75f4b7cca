import type {
  FlightListItem,
  FlightFilterState,
  SortOption,
  PaginationState,
  AirlineFilter,
  ConnectionAirportFilter
} from '../models/flight/flight-list-models';
import { timeToMinutes, durationToMinutes } from './flightDataTransformer';
import { applySorting, parseDurationToMinutes } from './flightSortingUtils';

/**
 * Apply filters to flight list
 */
export const applyFilters = (flights: FlightListItem[], filters: FlightFilterState): FlightListItem[] => {
  return flights.filter(flight => {
    // Stops filter
    const selectedStops = filters.stops.filter(stop => stop.isSelected).map(stop => stop.value);
    if (selectedStops.length > 0) {
      const flightStops = flight.stops;
      const matchesStops = selectedStops.some(stopValue => {
        if (stopValue === 2) {
          // 2+ stops
          return flightStops >= 2;
        }
        return flightStops === stopValue;
      });
      if (!matchesStops) return false;
    }

    // Refundable filter
    if (filters.refundable.isSelected && !flight.refundable) {
      return false;
    }

    // Departure time filter
    const selectedDepartureTimes = filters.departureTime.filter(time => time.isSelected);
    if (selectedDepartureTimes.length > 0) {
      const departureMinutes = timeToMinutes(flight.departure.time);
      const matchesDepartureTime = selectedDepartureTimes.some(timeRange => 
        departureMinutes >= timeRange.min && departureMinutes < timeRange.max
      );
      if (!matchesDepartureTime) return false;
    }

    // Arrival time filter
    const selectedArrivalTimes = filters.arrivalTime.filter(time => time.isSelected);
    if (selectedArrivalTimes.length > 0) {
      const arrivalMinutes = timeToMinutes(flight.arrival.time);
      const matchesArrivalTime = selectedArrivalTimes.some(timeRange => 
        arrivalMinutes >= timeRange.min && arrivalMinutes < timeRange.max
      );
      if (!matchesArrivalTime) return false;
    }

    // Airlines filter
    const selectedAirlines = filters.airlines.filter(airline => airline.isSelected).map(airline => airline.code);
    if (selectedAirlines.length > 0 && !selectedAirlines.includes(flight.airline.code)) {
      return false;
    }

    // Price range filter
    if (flight.price.amount > filters.priceRange.currentValue) {
      return false;
    }

    // Connection airports filter
    const selectedConnectionAirports = filters.connectionAirports
      .filter(airport => airport.isSelected)
      .map(airport => airport.code);
    if (selectedConnectionAirports.length > 0) {
      const flightConnectionAirports = flight.connections.map(conn => conn.airport);
      const hasMatchingConnection = selectedConnectionAirports.some(airportCode => 
        flightConnectionAirports.includes(airportCode)
      );
      if (!hasMatchingConnection) return false;
    }

    return true;
  });
};

/**
 * Sort flights based on sort option - now uses the standardized applySorting function
 * @deprecated Use applySorting from flightSortingUtils.ts instead
 */
export const sortFlights = (flights: FlightListItem[], sortOption: SortOption): FlightListItem[] => {
  console.warn('sortFlights is deprecated. Use applySorting from flightSortingUtils.ts instead');
  return applySorting(flights, sortOption);
};

/**
 * Apply pagination to flight list
 */
export const paginateFlights = (
  flights: FlightListItem[], 
  pagination: PaginationState
): { paginatedFlights: FlightListItem[]; updatedPagination: PaginationState } => {
  const totalItems = flights.length;
  const totalPages = Math.ceil(totalItems / pagination.itemsPerPage);
  
  // Ensure current page is valid
  const currentPage = Math.min(Math.max(1, pagination.currentPage), totalPages || 1);
  
  const startIndex = (currentPage - 1) * pagination.itemsPerPage;
  const endIndex = startIndex + pagination.itemsPerPage;
  const paginatedFlights = flights.slice(startIndex, endIndex);
  
  const updatedPagination: PaginationState = {
    ...pagination,
    currentPage,
    totalItems,
    totalPages
  };
  
  return { paginatedFlights, updatedPagination };
};

/**
 * Update filter state with new airline filters
 */
export const updateAirlineFilters = (
  currentFilters: FlightFilterState,
  availableAirlines: AirlineFilter[]
): FlightFilterState => {
  // Preserve existing selections
  const existingSelections = new Map(
    currentFilters.airlines.map(airline => [airline.code, airline.isSelected])
  );
  
  const updatedAirlines = availableAirlines.map(airline => ({
    ...airline,
    isSelected: existingSelections.get(airline.code) || false
  }));
  
  return {
    ...currentFilters,
    airlines: updatedAirlines
  };
};

/**
 * Update filter state with new connection airport filters
 */
export const updateConnectionAirportFilters = (
  currentFilters: FlightFilterState,
  availableAirports: ConnectionAirportFilter[]
): FlightFilterState => {
  // Preserve existing selections
  const existingSelections = new Map(
    currentFilters.connectionAirports.map(airport => [airport.code, airport.isSelected])
  );
  
  const updatedAirports = availableAirports.map(airport => ({
    ...airport,
    isSelected: existingSelections.get(airport.code) || false
  }));
  
  return {
    ...currentFilters,
    connectionAirports: updatedAirports
  };
};

/**
 * Update price range filter based on available flights
 */
export const updatePriceRangeFilter = (
  currentFilters: FlightFilterState,
  flights: FlightListItem[]
): FlightFilterState => {
  if (flights.length === 0) {
    return currentFilters;
  }
  
  const prices = flights.map(flight => flight.price.amount);
  const min = Math.min(...prices);
  const max = Math.max(...prices);
  
  // Keep current value if it's within the new range, otherwise set to max
  const currentValue = currentFilters.priceRange.currentValue;
  const newCurrentValue = currentValue >= min && currentValue <= max ? currentValue : max;
  
  return {
    ...currentFilters,
    priceRange: {
      min: Math.floor(min),
      max: Math.ceil(max),
      currentValue: newCurrentValue
    }
  };
};

/**
 * Reset all filters to default state
 */
export const resetFilters = (filters: FlightFilterState): FlightFilterState => {
  return {
    ...filters,
    stops: filters.stops.map(stop => ({ ...stop, isSelected: false })),
    refundable: { ...filters.refundable, isSelected: false },
    departureTime: filters.departureTime.map(time => ({ ...time, isSelected: false })),
    arrivalTime: filters.arrivalTime.map(time => ({ ...time, isSelected: false })),
    airlines: filters.airlines.map(airline => ({ ...airline, isSelected: false })),
    connectionAirports: filters.connectionAirports.map(airport => ({ ...airport, isSelected: false })),
    priceRange: {
      ...filters.priceRange,
      currentValue: filters.priceRange.max
    }
  };
};

/**
 * Get count of active filters
 */
export const getActiveFilterCount = (filters: FlightFilterState): number => {
  let count = 0;
  
  // Count selected stops
  count += filters.stops.filter(stop => stop.isSelected).length;
  
  // Count refundable filter
  if (filters.refundable.isSelected) count++;
  
  // Count selected departure times
  count += filters.departureTime.filter(time => time.isSelected).length;
  
  // Count selected arrival times
  count += filters.arrivalTime.filter(time => time.isSelected).length;
  
  // Count selected airlines
  count += filters.airlines.filter(airline => airline.isSelected).length;
  
  // Count selected connection airports
  count += filters.connectionAirports.filter(airport => airport.isSelected).length;
  
  // Count price range if not at maximum
  if (filters.priceRange.currentValue < filters.priceRange.max) count++;
  
  return count;
};

/**
 * Check if any filters are active
 */
export const hasActiveFilters = (filters: FlightFilterState): boolean => {
  return getActiveFilterCount(filters) > 0;
};
