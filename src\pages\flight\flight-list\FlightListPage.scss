/* Flight List Page Styling */
@use '../../../styles/variables' as *;

.flight-list-page {
  /* Page should start below the fixed header */
  padding-top: 64px; /* Height of header (adjust as needed) */

  @media (min-width: 640px) {
    padding-top: 72px; /* Larger header on bigger screens */
  }

  /* Sticky Modify Search Styling */
  .sticky-modify-search {
    /* Ensure full width and proper positioning */
    width: 100% !important;
    max-width: 100% !important;

    /* Smooth transitions */
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

    /* When fixed (scrolled) - ABOVE header */
    &.is-scrolled {
      /* Ensure it's above everything including headers */
      z-index: 9999 !important;

      /* Full width positioning - ABOVE header */
      position: fixed !important;
      top: 0 !important;
      left: 0 !important;
      right: 0 !important;

      /* Enhanced backdrop and shadow */
      backdrop-filter: blur(12px) saturate(1.2);
      background-color: rgba(255, 255, 255, 0.98) !important;
      border-bottom: 2px solid rgba(59, 130, 246, 0.2);

      /* Enhanced shadow for depth */
      box-shadow:
        0 4px 20px -2px rgba(0, 0, 0, 0.1),
        0 8px 40px -4px rgba(0, 0, 0, 0.15),
        0 0 0 1px rgba(255, 255, 255, 0.05);

      /* Compact padding when sticky */
      .modify-search-container {
        padding-top: 0.5rem !important;
        padding-bottom: 0.5rem !important;

        @media (min-width: 1024px) {
          padding-top: 0.75rem !important;
          padding-bottom: 0.75rem !important;
        }
      }
    }

    /* When not scrolled (relative) - BELOW header */
    &:not(.is-scrolled) {
      position: relative;
      z-index: 10; /* Below header (header has z-50) */
      background-color: rgba(255, 255, 255, 1);
      border-bottom: 1px solid rgba(229, 231, 235, 1);
      box-shadow: none;
      margin-top: 0; /* No margin - appears right below header */

      .modify-search-container {
        padding-top: 0.75rem;
        padding-bottom: 0.75rem;

        @media (min-width: 1024px) {
          padding-top: 1rem;
          padding-bottom: 1rem;
        }
      }
    }
    
    /* Container styling */
    .modify-search-container {
      width: 100%;
      max-width: 1920px;
      margin: 0 auto;
      padding-left: 1rem;
      padding-right: 1rem;
      
      @media (min-width: 1024px) {
        padding-left: 1.5rem;
        padding-right: 1.5rem;
      }
      
      @media (min-width: 1280px) {
        padding-left: 2rem;
        padding-right: 2rem;
      }
      
      /* Ensure modify search form takes full width */
      .modify-search-form {
        width: 100%;
        max-width: 100%;
      }
    }
  }
  
  /* Main content area adjustments */
  .main-content {
    /* Initially no padding - modify search is below header */
    padding-top: 0;

    /* Add top padding only when modify search is sticky (covering header) */
    &.has-sticky-search {
      padding-top: 80px; /* Adjust based on modify search height when sticky */

      @media (min-width: 1024px) {
        padding-top: 90px;
      }
    }
  }
  
  /* Responsive adjustments */
  @media (max-width: 768px) {
    .sticky-modify-search {
      &.is-scrolled {
        /* Slightly reduced backdrop blur on mobile for performance */
        backdrop-filter: blur(8px) saturate(1.1);
        
        .modify-search-container {
          padding-left: 0.75rem !important;
          padding-right: 0.75rem !important;
        }
      }
    }
    
    .main-content.has-sticky-search {
      padding-top: 70px; /* Only when sticky (covering header) */
    }
  }

  @media (max-width: 480px) {
    .sticky-modify-search {
      &.is-scrolled {
        backdrop-filter: blur(6px) saturate(1.05);

        .modify-search-container {
          padding-left: 0.5rem !important;
          padding-right: 0.5rem !important;
        }
      }
    }

    .main-content.has-sticky-search {
      padding-top: 65px; /* Only when sticky (covering header) */
    }
  }
}

/* Global z-index management */
.flight-list-page {
  /* Ensure proper stacking context */
  position: relative;
  z-index: 1;

  /* Header should be visible initially, but covered when modify search is sticky */
  header {
    z-index: 50 !important; /* Below sticky modify search (10000) but above normal content */
  }

  /* Modals should be above sticky modify search */
  .modal-overlay {
    z-index: 10001 !important;
  }

  /* Dropdowns should be above sticky modify search */
  .dropdown-menu {
    z-index: 10002 !important;
  }

  /* Alerts should be above everything */
  .alert-container {
    z-index: 10003 !important;
  }
}

/* Force header to have lower z-index */
header {
  position: fixed !important;
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  z-index: 50 !important; /* Keep header at 50 */
}

/* Force ALL headers to be below our sticky search */
header, .header, [class*="header"] {
  z-index: 50 !important;
}

/* Force our sticky search to be above EVERYTHING */
.sticky-modify-search.is-scrolled {
  z-index: 999999 !important;
  position: fixed !important;
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
}

/* Target the specific header classes */
.background-primary, .z-50 {
  z-index: 50 !important;
}

/* Nuclear option - force ALL fixed elements except our sticky search to be below */
*:not(.sticky-modify-search) {
  &[style*="position: fixed"], &[style*="position:fixed"] {
    z-index: 50 !important;
  }
}

/* Override all possible Tailwind z-index classes */
.z-10, .z-20, .z-30, .z-40, .z-50, .z-auto {
  z-index: 50 !important;
}

/* Force override any header with higher z-index */
header.z-50, header[class*="z-"], .header.z-50, .header[class*="z-"] {
  z-index: 50 !important;
}

/* Ultimate nuclear option - force our sticky search to maximum z-index */
.flight-list-page .sticky-modify-search.is-scrolled,
div.sticky-modify-search.is-scrolled,
.sticky-modify-search.is-scrolled {
  z-index: 2147483647 !important; /* Maximum possible z-index value */
  position: fixed !important;
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  width: 100vw !important;
}

/* Force reduce header z-index when our sticky search is active */
body:has(.sticky-modify-search.is-scrolled) header,
body:has(.sticky-modify-search.is-scrolled) .header {
  z-index: 1 !important;
}

/* Alternative approach - hide header when sticky search is active */
.flight-list-page:has(.sticky-modify-search.is-scrolled) header {
  z-index: 1 !important;
}

/* Ensure sticky modify search overrides header when scrolled */
.flight-list-page .sticky-modify-search.is-scrolled {
  z-index: 9999 !important;
  position: fixed !important;
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  width: 100% !important;
}

/* Animation keyframes */
@keyframes slideInFromTop {
  from {
    transform: translateY(-100%);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes slideOutToTop {
  from {
    transform: translateY(0);
    opacity: 1;
  }
  to {
    transform: translateY(-100%);
    opacity: 0;
  }
}

/* Smooth scroll behavior for the page */
html {
  scroll-behavior: smooth;
}

/* Ensure no horizontal overflow */
.flight-list-page {
  overflow-x: hidden;
  width: 100%;
  max-width: 100vw;
}

/* Additional specificity for sticky modify search */
.flight-list-page {
  .sticky-modify-search {
    &.is-scrolled {
      position: fixed !important;
      top: 0px !important;
      left: 0px !important;
      right: 0px !important;
      z-index: 99999 !important;
      width: 100vw !important;
      max-width: 100vw !important;

      /* Professional styling */
      background-color: rgba(255, 255, 255, 0.98) !important;
      backdrop-filter: blur(12px) saturate(1.2) !important;
      border-bottom: 2px solid rgba(59, 130, 246, 0.2) !important;

      /* Enhanced shadow to show it's above everything */
      box-shadow:
        0 4px 20px -2px rgba(0, 0, 0, 0.1),
        0 8px 40px -4px rgba(0, 0, 0, 0.15),
        0 0 0 1px rgba(255, 255, 255, 0.1) !important;
    }
  }
}

/* Force override any conflicting styles */
.sticky-modify-search.is-scrolled {
  position: fixed !important;
  top: 0px !important;
  left: 0px !important;
  right: 0px !important;
  z-index: 99999 !important;
  background-color: rgba(255, 255, 255, 0.98) !important;
  border-bottom: 2px solid rgba(59, 130, 246, 0.2) !important;
}

/* Even more specific override */
div.sticky-modify-search.is-scrolled {
  position: fixed !important;
  top: 0px !important;
  left: 0px !important;
  right: 0px !important;
  z-index: 99999 !important;
  background-color: rgba(255, 255, 255, 0.98) !important;
  border-bottom: 2px solid rgba(59, 130, 246, 0.2) !important;
  width: 100vw !important;
  max-width: 100vw !important;
}

/* Ultimate override */
.flight-list-page div.sticky-modify-search.is-scrolled {
  position: fixed !important;
  top: 0px !important;
  left: 0px !important;
  right: 0px !important;
  z-index: 99999 !important;
  background-color: rgba(255, 255, 255, 0.98) !important;
  border-bottom: 2px solid rgba(59, 130, 246, 0.2) !important;
  width: 100vw !important;
  max-width: 100vw !important;
}
