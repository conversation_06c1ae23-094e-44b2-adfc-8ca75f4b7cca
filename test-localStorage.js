// Test script to verify localStorage functionality
// Run this in the browser console to test localStorage persistence

console.log('🧪 Testing localStorage functionality...');

// Check if there's existing data
const existingData = localStorage.getItem('flightSearchData');
console.log('📦 Existing localStorage data:', existingData);

if (existingData) {
    const parsed = JSON.parse(existingData);
    console.log('📋 Parsed data:', parsed);
} else {
    console.log('📝 No existing data found');
}

// Create test data
const testSearchData = {
    from: {
        city: 'Paris',
        airport: 'Charles de Gaulle Airport',
        iata: 'CDG',
        country: 'France',
        airportOpen: false
    },
    to: {
        city: 'Tokyo',
        airport: 'Haneda Airport',
        iata: 'HND',
        country: 'Japan',
        airportOpen: false
    },
    depart: new Date('2024-07-15'),
    return: new Date('2024-07-22'),
    tripType: 'roundTrip',
    passengers: {
        adults: 2,
        children: 1,
        infants: 0
    },
    class: 'business',
    advanced_search: {
        selectedAirlines: ['Air France', 'Japan Airlines'],
        flightOptions: {
            directFlights: true,
            refundableFares: false,
            corporateRates: true
        },
        services: {
            airportLounge: true,
            extraBaggage: false,
            travelInsurance: true
        }
    }
};

console.log('💾 Saving test data to localStorage...');
localStorage.setItem('flightSearchData', JSON.stringify(testSearchData));

console.log('✅ Test data saved! Refresh the page to see if it loads correctly.');
console.log('🔍 Expected results:');
console.log('  - FROM airport: Paris (CDG)');
console.log('  - TO airport: Tokyo (HND)');
console.log('  - Departure: Jul 15');
console.log('  - Return: Jul 22');
console.log('  - Trip type: Round Trip');
console.log('  - Passengers: 2 adults, 1 child');
console.log('  - Class: Business');
