import React from 'react';
import AirlineIcon from './AirlineIcon';
import FlightDurationChart from './FlightDurationChart';
import type { MultiCityFlightCardProps } from './types';

const MultiCityFlightCard: React.FC<MultiCityFlightCardProps> = ({
  flight,
  isSelected,
  onSelect,
  onToggleDetails,
  showDetails
}) => {
  return (
    <div
      className={`border rounded-lg overflow-hidden bg-white hover:shadow-md transition-all duration-300 ${
        isSelected
          ? "border-blue-500 ring-1 ring-blue-500"
          : "border-gray-200"
      }`}
    >
      <div className="p-3">
        {/* Flight header */}
        <div className="flex justify-between items-center mb-2">
          <div className="flex items-center">
            <AirlineIcon code={flight.airline.code} />
            <div className="ml-2">
              <div className="text-sm font-medium">
                {flight.airline.name}
              </div>
              <div className="text-xs text-gray-500">
                {flight.flightNumber}
              </div>
            </div>
          </div>
          <div className="text-right">
            <div className="font-bold text-base">
              ${flight.fare.price}
            </div>
            <div className="text-xs text-gray-500">
              {flight.fare.type}
            </div>
          </div>
        </div>

        {/* Flight details */}
        <div className="flex justify-between items-center mb-2">
          <div>
            <div className="text-base font-medium">
              {flight.departure.time}
            </div>
            <div className="text-xs font-medium">
              {flight.departure.code}
            </div>
            <div className="text-[11px] text-gray-500">
              T{flight.departure.terminal}
            </div>
          </div>
          <div className="flex flex-col items-center flex-1 px-3">
            <div className="text-[11px] text-gray-500">
              {flight.duration}
            </div>
            <FlightDurationChart duration={flight.duration} />
            <div className="text-[11px] text-gray-500">
              {flight.stops === 0
                ? "Nonstop"
                : `${flight.stops} stop${flight.stops > 1 ? "s" : ""}`}
            </div>
          </div>
          <div>
            <div className="text-base font-medium text-right">
              {flight.arrival.time}
            </div>
            <div className="text-xs font-medium text-right">
              {flight.arrival.code}
            </div>
            <div className="text-[11px] text-gray-500 text-right">
              T{flight.arrival.terminal}
            </div>
          </div>
        </div>

        {/* Flight info */}
        <div className="flex justify-between items-center text-[11px] text-gray-500 mb-2">
          <div>
            <i className="fas fa-plane mr-1"></i>
            {flight.aircraft}
          </div>
          <div>
            <i className="fas fa-suitcase mr-1"></i>
            {flight.fare.baggage}
          </div>
          <div>
            {flight.fare.refundable ? (
              <span className="text-green-500">
                <i className="fas fa-check-circle mr-1"></i>
                Refundable
              </span>
            ) : (
              <span className="text-red-400">
                <i className="fas fa-times-circle mr-1"></i>
                Non-refundable
              </span>
            )}
          </div>
        </div>

        {/* Expandable fare details */}
        {showDetails && (
          <div className="mt-3 p-3 bg-gray-50 rounded-md text-sm">
            <h4 className="font-medium mb-2">Fare Details</h4>
            <div className="grid grid-cols-2 gap-2">
              <div>
                <div className="font-medium">Baggage Allowance:</div>
                <div>{flight.fare.baggage}</div>
              </div>
              <div>
                <div className="font-medium">Fare Rules:</div>
                <div>
                  {flight.fare.refundable
                    ? "Refundable with fee"
                    : "Non-refundable"}
                </div>
              </div>
              <div>
                <div className="font-medium">Seat Selection:</div>
                <div>
                  {flight.fare.type === "Economy" ? "Paid" : "Included"}
                </div>
              </div>
              <div>
                <div className="font-medium">Meal:</div>
                <div>
                  {flight.fare.type === "Economy"
                    ? "Purchase onboard"
                    : "Included"}
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Actions */}
        <div className="flex justify-between items-center mt-2">
          <button
            onClick={() => onToggleDetails(flight.id)}
            className="text-blue-500 hover:text-blue-700 text-xs cursor-pointer"
          >
            {showDetails ? "Hide details" : "View details"}
          </button>
          <button
            onClick={() => onSelect(flight)}
            className={`px-3 py-1 text-sm rounded-button whitespace-nowrap cursor-pointer ${
              isSelected
                ? "bg-blue-500 hover:bg-blue-600 text-white"
                : "bg-blue-500 hover:bg-blue-600 text-white"
            }`}
          >
            {isSelected ? "Selected" : "Select"}
          </button>
        </div>
      </div>
    </div>
  );
};

export default MultiCityFlightCard;
