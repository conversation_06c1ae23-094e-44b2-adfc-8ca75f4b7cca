import { baseApiService } from './base-api.service';

// Common axios instance from base service
const api = baseApiService.getAxiosInstance();
const tokenManager = baseApiService.getTokenManager();

// ==================== REGISTRATION OPERATIONS ====================

/**
 * Register new user
 */
export const registerSubmit = async (body: any): Promise<{id: number, email: string}> => {
  try {
    const response = await api.post('auth/register/', body);
    return response.data;
  } catch (error) {
    throw error;
  }
};

// ==================== LOGIN OPERATIONS ====================

/**
 * Initiate login process (request OTP)
 */
export const loginSubmit = async (body: {email: string, userId: number | null}): Promise<{id: number, email: string}> => {
  try {
    const response = await api.get(`auth/get_login_otp/${body.userId}`);
    return response.data;
  } catch (error) {
    throw error;
  }
};

// ==================== OTP VERIFICATION OPERATIONS ====================

/**
 * Verify OTP for registration
 */
export const verifyOtp = async (body: {email: string, otp: string}): Promise<{
  message: string;
  verified: boolean;
  access_token?: string;
}> => {
  try {
    const response = await api.post(`auth/otp_verify`, body);

    if (response.data.verified && response.data.access_token) {
      tokenManager.setToken(response.data.access_token);
    }

    return response.data;
  } catch (error) {
    throw error;
  }
};

/**
 * Verify OTP for login
 */
export const verifyLoginOtp = async (body: {email: string, otp: string}): Promise<{
  message: string;
  verified: boolean;
  access_token?: string;
}> => {
  try {
    const response = await api.post(`auth/login?email=${body.email}&otp=${body.otp}`, body);

    if (response.data.verified && response.data.access_token) {
      tokenManager.setToken(response.data.access_token);
    }

    return response.data;
  } catch (error) {
    throw error;
  }
};

// ==================== TOKEN MANAGEMENT OPERATIONS ====================

/**
 * Set authentication token
 */
export const setToken = (token: string): void => {
  tokenManager.setToken(token);
};

/**
 * Get current authentication token
 */
export const getToken = (): string | null => {
  return tokenManager.getToken();
};

/**
 * Clear authentication token
 */
export const clearToken = (): void => {
  tokenManager.clearToken();
};

/**
 * Check if current token is valid
 */
export const isTokenValid = (): boolean => {
  return tokenManager.isTokenValid();
};

// ==================== SESSION MANAGEMENT ====================

/**
 * Check if user is authenticated
 */
export const isAuthenticated = (): boolean => {
  const token = getToken();
  return token !== null && isTokenValid();
};

/**
 * Logout user (clear token and session data)
 */
export const logout = (): void => {
  clearToken();
};

/**
 * Refresh authentication token if needed
 */
export const refreshTokenIfNeeded = async (): Promise<boolean> => {
  try {
    if (!isTokenValid()) {
      clearToken();
      return false;
    }
    return true;
  } catch (error) {
    clearToken();
    return false;
  }
};
