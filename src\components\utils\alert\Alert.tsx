"use client";

import React, { createContext, useState, useContext, useRef, useCallback, useEffect } from "react";
import ReactDOM from "react-dom";
import { CheckCircle, XCircle, Info } from "lucide-react";
import './alert.scss'

interface AlertContextType {
  fire: (options: AlertOptions) => void;
}
export interface AlertOptions {
  position?: "top-right" | "top-left" | "bottom-right" | "bottom-left" | "center";
  icon?: "success" | "error" | "info";
  title?: string;
  text?: string;
  autoClose?: number; // Time in milliseconds
  initialValue?: string;
  placeholder?: string;
  confirmButtonText?: string;
  cancelButtonText?: string;
  showInput?: boolean;
  inputType?: "text" | "textarea" | "email" | "password";
  onConfirm?: (inputValue: string) => void;
  onCancel?: () => void;
  onClose?: () => void;
}

const AlertContext = createContext<AlertContextType | null>(null);

export const AlertProvider = ({ children }: { children: React.ReactNode }) => {
  const [alert, setAlert] = useState<AlertOptions | null>(null);
  const [inputValue,setInputValue] = useState<string>('')
  const timerRef = useRef<number | null>(null);
  const alertRef = useRef<HTMLDivElement>(null);


  const close = useCallback(() => {
    const currentAlert = alert;
    setAlert(null);
    setInputValue("");
    if (timerRef.current) {
      clearTimeout(timerRef.current);
      timerRef.current = null;
    }
    // Call onClose callback if provided
    currentAlert?.onClose?.();
  }, [alert]);

  // Wrap `fire` in useCallback and include `close` in its dependencies.
  const fire = useCallback(({
    position = "center",
    icon = "info",
    title = "",
    text = "",
    autoClose = 0,
    initialValue = "",
    placeholder = "",
    confirmButtonText = "",
    cancelButtonText = "",
    showInput = false,
    inputType = "text",
    onConfirm,
    onCancel,
    onClose,
  }: AlertOptions) => {
    // Clear any existing timer
    if (timerRef.current) {
      clearTimeout(timerRef.current);
      timerRef.current = null;
    }

    setAlert({
      position,
      icon,
      title,
      text,
      confirmButtonText,
      cancelButtonText,
      initialValue,
      placeholder,
      showInput,
      inputType,
      autoClose,
      onConfirm,
      onCancel,
      onClose,
    });
    setInputValue(initialValue || "");

    if (autoClose > 0) {
      timerRef.current = window.setTimeout(() => close(), autoClose);
    }
  }, [close]);

  // Keyboard support
  useEffect(() => {
    if (!alert) return;

    const handleKeyDown = (event: KeyboardEvent) => {
      // Prevent keyboard shortcuts when user is typing in input fields
      const isInputFocused = document.activeElement?.tagName === 'INPUT' ||
                            document.activeElement?.tagName === 'TEXTAREA';

      if (event.key === "Escape") {
        event.preventDefault();
        if (alert.cancelButtonText) {
          alert.onCancel?.();
        }
        close();
      } else if (event.key === "Enter" && !isInputFocused) {
        event.preventDefault();
        if (alert.confirmButtonText) {
          alert.onConfirm?.(inputValue);
          close();
        } else if (!alert.cancelButtonText) {
          close();
        }
      }
    };

    document.addEventListener("keydown", handleKeyDown);
    return () => document.removeEventListener("keydown", handleKeyDown);
  }, [alert, close, inputValue]);

  // Focus management
  useEffect(() => {
    if (alert && alertRef.current) {
      // Focus the alert dialog for accessibility
      alertRef.current.focus();
    }
  }, [alert]);
  const getPositionClass = (position: string | undefined) => {
    switch (position) {
      case "top-right":
        return "items-start justify-end";
      case "top-left":
        return "items-start justify-start";
      case "bottom-right":
        return "items-end justify-end";
      case "bottom-left":
        return "items-end justify-start";
      case "center":
      default:
        return " items-center justify-center";
    }
  };


  return (
    <AlertContext.Provider value={{ fire }}>
      {children}
      {alert &&
        ReactDOM.createPortal(
          <div
            style={{ zIndex: 2000 }}
            className={`fixed inset-0 bg-black bg-opacity-60 backdrop-blur-sm flex ${getPositionClass(alert.position)} transition-all duration-300`}
            onClick={close}
            role="dialog"
            aria-modal="true"
            aria-labelledby={alert.title ? "alert-title" : undefined}
            aria-describedby={alert.text ? "alert-description" : undefined}
          >
            <div
              ref={alertRef}
              className="alert-box bg-slate-800 rounded-lg shadow-lg w-96 p-6 flex flex-col items-center border border-slate-700"
              onClick={(e) => e.stopPropagation()}
              style={{
                animation: alert.autoClose
                  ? `countdown ${alert.autoClose}ms linear forwards`
                  : undefined,
              }}
              role="alertdialog"
              tabIndex={-1}
            >
              {/* Icon Section */}
              <div className="rounded-full flex items-center justify-center mb-4 alert-icons">
                {alert.icon === "success" && (
                  <CheckCircle className="w-16 h-16 text-green-500" />
                )}
                {alert.icon === "error" && (
                  <XCircle className="w-16 h-16 text-red-500" />
                )}
                {alert.icon === "info" && (
                  <Info className="w-16 h-16 text-blue-500" />
                )}
              </div>

              {/* Title and Text */}
              {alert.title && (
                <h2 id="alert-title" className="text-lg font-semibold text-center mb-2 text-white">
                  {alert.title}
                </h2>
              )}
              {alert.text && (
                <p id="alert-description" className="text-sm text-slate-300 text-center mb-6">
                  {alert.text}
                </p>
              )}

              {/* Input Section */}
              {(alert.showInput || alert.initialValue) && (
                <div className="w-full mb-4">
                  {alert.inputType === "textarea" ? (
                    <textarea
                      onChange={(e) => setInputValue(e.target.value)}
                      value={inputValue}
                      placeholder={alert.placeholder}
                      className="w-full px-3 py-2 bg-slate-700 border border-slate-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-white placeholder-slate-400 text-sm resize-vertical"
                      rows={4}
                      aria-label={alert.placeholder || "Input field"}
                    />
                  ) : (
                    <input
                      type={alert.inputType || "text"}
                      onChange={(e) => setInputValue(e.target.value)}
                      value={inputValue}
                      placeholder={alert.placeholder}
                      className="w-full px-3 py-2 bg-slate-700 border border-slate-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-white placeholder-slate-400 text-sm"
                      aria-label={alert.placeholder || "Input field"}
                    />
                  )}
                </div>
              )}
              {/* Buttons Section */}
              <div className="flex flex-col sm:flex-row gap-3 w-full sm:justify-end">
                {alert.cancelButtonText && (
                  <button
                    onClick={() => {
                      alert.onCancel?.();
                      close();
                    }}
                    className="px-6 py-3 border-2 border-slate-600 text-slate-300 rounded-lg hover:bg-slate-700 hover:border-slate-500 hover:text-white transition-all duration-200 font-medium flex-1 sm:flex-none sm:min-w-[120px] focus:ring-2 focus:ring-slate-500 focus:ring-offset-2 focus:ring-offset-slate-800"
                    aria-label={`Cancel: ${alert.cancelButtonText}`}
                  >
                    {alert.cancelButtonText}
                  </button>
                )}
                {alert.confirmButtonText && (
                  <button
                    onClick={() => {
                      alert.onConfirm?.(inputValue);
                      close();
                    }}
                    className={`px-6 py-3 rounded-lg font-medium text-white transition-all duration-200 flex-1 sm:flex-none sm:min-w-[120px] ${
                      alert.icon === 'error'
                        ? 'bg-red-600 hover:bg-red-700 focus:ring-2 focus:ring-red-500 focus:ring-offset-2 focus:ring-offset-slate-800'
                        : 'bg-blue-600 hover:bg-blue-700 focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 focus:ring-offset-slate-800'
                    }`}
                    aria-label={`Confirm: ${alert.confirmButtonText}`}
                  >
                    {alert.confirmButtonText}
                  </button>
                )}

                {/* Default close button if no other buttons */}
                {!alert.confirmButtonText && !alert.cancelButtonText && (
                  <button
                    onClick={close}
                    className="bg-blue-600 hover:bg-blue-700 px-6 py-3 rounded-lg font-medium text-white transition-all duration-200 focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 focus:ring-offset-slate-800"
                    aria-label="Close alert"
                  >
                    OK
                  </button>
                )}
              </div>
            </div>
          </div>,
          document.body
        )}
    </AlertContext.Provider>
  );
};

export const useAlert = (): AlertContextType => {
  const context = useContext(AlertContext);
  if (!context) {
    throw new Error("useAlert must be used within an AlertProvider");
  }
  return context;
};
