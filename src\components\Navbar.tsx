import { useState } from 'react'

function Navbar() {
    const [activeTab, setActiveTab] = useState('flights');

    const tabs = [
        { id: 'flights', label: 'Flights', icon: 'fa-plane' },
        { id: 'stay', label: 'Stay', icon: 'fa-hotel' },
        { id: 'visa', label: 'Visa', icon: 'fa-passport' },
        { id: 'cruise', label: 'Cruise', icon: 'fa-ship' },
        { id: 'holiday', label: 'Holiday', icon: 'fa-umbrella-beach' }
    ];



    return (
        <div className="bg-white shadow-sm">
            <div className="max-w-7xl mx-auto">
                <div className="flex overflow-x-auto">
                    {tabs.map(tab => (
                        <button
                            key={tab.id}
                            className={`!rounded-button flex items-center space-x-2 px-6 py-4 border-b-2 whitespace-nowrap cursor-pointer transition-colors ${activeTab === tab.id
                                ? 'border-blue-600 text-blue-600'
                                : 'border-transparent text-gray-600 hover:text-gray-900'
                                }`}
                            onClick={() => setActiveTab(tab.id)}
                        >
                            <i className={`fas ${tab.icon}`}></i>
                            <span className="hidden sm:inline">{tab.label}</span>
                        </button>
                    ))}
                </div>
            </div>
        </div>
    )
}

export default Navbar
