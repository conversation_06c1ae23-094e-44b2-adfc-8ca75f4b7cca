import React from 'react';

interface Airport {
  code: string;
  city: string;
  name: string;
  country: string;
}

interface SimpleAirportDropdownProps {
  airports: Airport[];
  onAirportSelect: (airport: string) => void;
  icon: string;
}

const SimpleAirportDropdown: React.FC<SimpleAirportDropdownProps> = ({
  airports,
  onAirportSelect,
  icon
}) => {
  return (
    <div className="bg-white rounded-xl shadow-xl border border-gray-200 py-2 min-w-80 max-h-80 overflow-y-auto">
      <div className="px-3 py-2 text-xs font-medium text-gray-500 border-b border-gray-100">
        Popular Airports
      </div>
      {airports.map((airport, index) => (
        <button
          key={index}
          className="w-full flex items-start px-3 py-2.5 hover:bg-gray-50 cursor-pointer text-left transition-colors"
          onClick={() => onAirportSelect(`${airport.city} (${airport.code})`)}
        >
          <div className="mr-3 mt-0.5">
            <i className={`${icon} text-gray-400`}></i>
          </div>
          <div>
            <div className="font-medium text-gray-800">{airport.city} ({airport.code})</div>
            <div className="text-xs text-gray-500">{airport.name}, {airport.country}</div>
          </div>
        </button>
      ))}
    </div>
  );
};

export default SimpleAirportDropdown;
