import type { FlightResponse, Journey, Trip } from '../models/flight/flight-list.model';
import type { FlightListItem, Connection, FareRestrictions } from '../models/flight/flight-list-models';

/**
 * Transform API Journey data to FlightListItem interface
 */
export const transformJourneyToFlightListItem = (journey: Journey, tripIndex: number = 0, sh_price: boolean = true): FlightListItem => {
  // Parse departure and arrival times
  const departureTime = new Date(journey.DepartureTime);
  const arrivalTime = new Date(journey.ArrivalTime);
  
  // Format time as HH:MM
  const formatTime = (date: Date): string => {
    return date.toLocaleTimeString('en-US', { 
      hour: '2-digit', 
      minute: '2-digit', 
      hour12: false 
    });
  };

  // Format date as YYYY-MM-DD
  const formatDate = (date: Date): string => {
    return date.toISOString().split('T')[0];
  };

  // Transform connections
  const connections: Connection[] = journey.Connections?.map(conn => ({
    airport: conn.Airport,
    airportName: conn.ArrAirportName,
    arrivalTime: '', // Would need to be calculated from connection data
    departureTime: '', // Would need to be calculated from connection data
    layoverDuration: conn.Duration,
    terminal: {
      arrival: undefined,
      departure: undefined
    }
  })) || [];

  // Parse fare restrictions
  const fareRestrictions: FareRestrictions | undefined = journey.fareRestrictions ? {
    fareIdentifier: journey.fareRestrictions.fareIdentifier,
    fareFamily: journey.fareRestrictions.fareFamily,
    requiresBundling: journey.fareRestrictions.requiresBundling,
    compatibleFareIds: journey.fareRestrictions.compatibleFareIds,
    specialFareWarning: journey.fareRestrictions.specialFareWarning,
    isSpecialReturn: journey.fareRestrictions.isSpecialReturn
  } : undefined;

  // Determine refundable status
  const isRefundable = journey.Refundable === 'true' || journey.Refundable === 'Y' || journey.Refundable === true;

  return {
    id: journey.Index || `${journey.FlightNo}-${tripIndex}`,
    flightNumber: journey.FlightNo || 'Unknown',
    airline: {
      code: journey.VAC || journey.MAC || journey.Provider || 'XX',
      name: journey.AirlineName || 'Unknown Airline',
      logo: journey.VACAirlineLogo || journey.MACAirlineLogo || ''
    },
    departure: {
      airport: journey.From,
      terminal: journey.DepartureTerminal || 'N/A',
      city: journey.FromName || journey.FromCity || journey.From,
      time: formatTime(departureTime),
      date: formatDate(departureTime)
    },
    arrival: {
      airport: journey.To,
      terminal: journey.ArrivalTerminal || 'N/A',
      city: journey.ToName || journey.ToCity || journey.To,
      time: formatTime(arrivalTime),
      date: formatDate(arrivalTime)
    },
    duration: journey.Duration || calculateDuration(departureTime, arrivalTime),
    stops: journey.Stops || 0,
    connections,
    price: {
      amount: Math.round(journey.TotalFare || journey.GrossFare || 0),
      currency: 'INR', // Default currency, should be configurable
      baseAmount: Math.round(journey.NetFare || journey.TotalFare || journey.GrossFare || 0),
      taxes: Math.round((journey.TotalFare || journey.GrossFare || 0) - (journey.NetFare || journey.TotalFare || journey.GrossFare || 0))
    },
    availability: {
      seats: journey.Seats || 9,
      cabinClass: journey.Cabin || 'Economy'
    },
    refundable: isRefundable,
    baggage: {
      cabin: journey.Inclusions?.Baggage || '7 kg',
      checked: journey.Inclusions?.PieceDescription?.toString() || '15 kg'
    },
    fareType: journey.PrivateFareType || 'REGULAR',
    fareRestrictions,
    isVisible: journey.isVisible !== false,
    isSelected: journey.isSelect || false,
    sh_price: sh_price, // Whether price is finalized (true) or needs shimmer (false)
    performanceData: {
      cacheHit: false, // Will be set by the calling function
      liveRefresh: false, // Will be set by the calling function
      responseTimeMs: 0 // Will be set by the calling function
    }
  };
};

/**
 * Transform API FlightResponse to FlightListItem arrays
 */
export const transformFlightResponse = (
  response: FlightResponse,
  performanceData?: { cacheHit: boolean; responseTimeMs: number }
) => {
  if (!response.Trips || response.Trips.length === 0) {
    return {
      outboundFlights: [],
      returnFlights: [],
      multiCityFlights: [],
      totalResults: 0
    };
  }

  const outboundFlights: FlightListItem[] = [];
  const returnFlights: FlightListItem[] = [];
  const multiCityFlights: FlightListItem[][] = [];

  response.Trips.forEach((trip: Trip, tripIndex: number) => {
    if (!trip.Journey || trip.Journey.length === 0) return;

    const transformedFlights = trip.Journey.map(journey => {
      const flight = transformJourneyToFlightListItem(journey, tripIndex, response.sh_price);

      // Add performance data if available
      if (performanceData && flight.performanceData) {
        flight.performanceData.cacheHit = performanceData.cacheHit;
        flight.performanceData.responseTimeMs = performanceData.responseTimeMs;
        flight.performanceData.liveRefresh = !performanceData.cacheHit;
      }

      return flight;
    });

    if (tripIndex === 0) {
      // First trip is outbound
      outboundFlights.push(...transformedFlights);
    } else if (tripIndex === 1) {
      // Second trip is return (for round-trip)
      returnFlights.push(...transformedFlights);
    } else {
      // Additional trips for multi-city
      multiCityFlights.push(transformedFlights);
    }
  });

  return {
    outboundFlights,
    returnFlights,
    multiCityFlights,
    totalResults: outboundFlights.length + returnFlights.length + 
                  multiCityFlights.reduce((sum, flights) => sum + flights.length, 0)
  };
};

/**
 * Calculate duration between two dates
 */
const calculateDuration = (departure: Date, arrival: Date): string => {
  const diffMs = arrival.getTime() - departure.getTime();
  const hours = Math.floor(diffMs / (1000 * 60 * 60));
  const minutes = Math.floor((diffMs % (1000 * 60 * 60)) / (1000 * 60));
  return `${hours}h ${minutes}m`;
};

/**
 * Extract unique airlines from flight data for filter generation
 */
export const extractAirlinesFromFlights = (flights: FlightListItem[]) => {
  const airlineMap = new Map();
  
  flights.forEach(flight => {
    if (!airlineMap.has(flight.airline.code)) {
      airlineMap.set(flight.airline.code, {
        code: flight.airline.code,
        name: flight.airline.name,
        isSelected: false,
        logo: flight.airline.logo
      });
    }
  });
  
  return Array.from(airlineMap.values());
};

/**
 * Extract unique connection airports from flight data
 */
export const extractConnectionAirportsFromFlights = (flights: FlightListItem[]) => {
  const airportMap = new Map();
  
  flights.forEach(flight => {
    flight.connections.forEach(connection => {
      if (!airportMap.has(connection.airport)) {
        airportMap.set(connection.airport, {
          code: connection.airport,
          name: connection.airportName,
          isSelected: false
        });
      }
    });
  });
  
  return Array.from(airportMap.values());
};

/**
 * Calculate price range from flight data
 */
export const calculatePriceRange = (flights: FlightListItem[]) => {
  if (flights.length === 0) {
    return { min: 0, max: 100000, currentValue: 100000 };
  }
  
  const prices = flights.map(flight => flight.price.amount);
  const min = Math.min(...prices);
  const max = Math.max(...prices);
  
  return {
    min: Math.floor(min),
    max: Math.ceil(max),
    currentValue: Math.ceil(max)
  };
};

/**
 * Convert time string (HH:MM) to minutes from midnight
 */
export const timeToMinutes = (timeStr: string): number => {
  const [hours, minutes] = timeStr.split(':').map(Number);
  return hours * 60 + minutes;
};

/**
 * Convert minutes from midnight to time string (HH:MM)
 */
export const minutesToTime = (minutes: number): string => {
  const hours = Math.floor(minutes / 60);
  const mins = minutes % 60;
  return `${hours.toString().padStart(2, '0')}:${mins.toString().padStart(2, '0')}`;
};

/**
 * Parse duration string to minutes
 */
export const durationToMinutes = (duration: string): number => {
  const match = duration.match(/(\d+)h?\s*(\d+)?m?/);
  if (!match) return 0;
  
  const hours = parseInt(match[1]) || 0;
  const minutes = parseInt(match[2]) || 0;
  return hours * 60 + minutes;
};
