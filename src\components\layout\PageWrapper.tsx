import React from 'react';

interface PageWrapperProps {
  children: React.ReactNode;
  className?: string;
  fullHeight?: boolean;
  background?: 'white' | 'gray' | 'blue' | 'transparent';
  padding?: 'none' | 'sm' | 'md' | 'lg';
}

const PageWrapper: React.FC<PageWrapperProps> = ({
  children,
  className = '',
  fullHeight = true,
  background = 'gray',
  padding = 'md'
}) => {
  const backgroundClasses = {
    white: 'background-primary',
    gray: 'background-secondary',
    blue: 'primary-background-lightest',
    transparent: 'bg-transparent'
  };

  const paddingClasses = {
    none: '',
    sm: 'py-2',
    md: 'py-6',
    lg: 'py-12'
  };

  return (
    <div 
      className={`
        ${fullHeight ? 'min-h-screen' : ''}
        ${backgroundClasses[background]}
        ${paddingClasses[padding]}
        ${className}
      `.trim()}
    >
      {children}
    </div>
  );
};

export default PageWrapper;
