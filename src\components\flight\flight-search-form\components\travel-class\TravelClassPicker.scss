/* TravelClassPicker Component Styles */

/* Travel class wrapper - Applied to parent container */
.travel-class-wrapper {
  /* Smart height calculation - use available space or fallback */
  max-height: var(--dropdown-max-height, 450px);

  /* Custom scrollbar styling - more visible (same as CalendarModal) */
  &::-webkit-scrollbar {
    width: 8px;
  }

  &::-webkit-scrollbar-track {
    background: #f1f5f9;
    border-radius: 4px;
    margin: 4px 0;
  }

  &::-webkit-scrollbar-thumb {
    background: #94a3b8;
    border-radius: 4px;
    border: 1px solid #e2e8f0;

    &:hover {
      background: #64748b;
    }
  }

  /* Firefox scrollbar */
  scrollbar-width: thin;
  scrollbar-color: #94a3b8 #f1f5f9;

  /* Responsive adjustments */
  @media (max-width: 768px) {
    max-height: calc(100vh - var(--dropdown-top, 150px) - 1rem);
  }
}

.traveller-selection {
  /* Position handled by parent wrapper */
  /* Number grid styling - Single horizontal line */
  .number-grid {
    display: flex;
    flex-wrap: nowrap;
    gap: 0.375rem;
    margin-top: 0.25rem;
    overflow-x: auto;
    min-width: 100%;
  }

  .number-option {
    width: 2rem;
    height: 2rem;
    border-radius: 0.375rem;
    border: 1px solid #d1d5db;
    background-color: white;
    color: #374151;
    font-size: 0.75rem;
    font-weight: 500;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.2s ease-in-out;
    flex-shrink: 0;

    &:hover:not(.disabled) {
      border-color: #60a5fa;
      background-color: #eff6ff;
    }

    &.selected {
      background-color: #2563eb;
      color: white;
      border-color: #2563eb;
    }

    &.disabled {
      opacity: 0.5;
      cursor: not-allowed;
      background-color: #f9fafb;
    }
  }

  /* Class selection styling - Single horizontal line */
  .class-selection-container {
    display: flex;
    gap: 0.5rem;
    flex-wrap: wrap;
  }

  .class-selection-item {
    padding: 0.375rem 0.75rem;
    font-size: 0.75rem;
    font-weight: 500;
    border-radius: 0.375rem;
    background-color: #f3f4f6;
    color: #374151;
    cursor: pointer;
    transition: all 0.2s ease-in-out;
    text-align: center;
    flex: 1;
    white-space: nowrap;

    &:hover {
      background-color: #e5e7eb;
    }

    &.active {
      background-color: #2563eb;
      color: white;
    }
  }

  /* Group booking notice */
  .group-booking-notice {
    padding: 0.5rem;
    background-color: #eff6ff;
    border-radius: 0.375rem;
    margin-bottom: 1rem;

    p {
      font-size: 0.75rem;
      color: #4b5563;
      margin: 0 0 0.125rem 0;
    }

    a {
      color: #2563eb;
      font-size: 0.75rem;
      font-weight: 500;
      text-decoration: none;

      &:hover {
        text-decoration: underline;
      }
    }
  }

  /* Section styling */
  .traveller-section {
    margin-bottom: 1rem;

    .section-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 0.25rem;

      .section-title {
        font-weight: 500;
        color: #1f2937;
        font-size: 0.875rem;

        .section-subtitle {
          font-size: 0.75rem;
          color: #6b7280;
          font-weight: 400;
          margin-left: 0.5rem;
        }
      }
    }
  }

  /* Apply button */
  .apply-button {
    width: 100%;
    background-color: #2563eb;
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 0.375rem;
    border: none;
    font-weight: 500;
    cursor: pointer;
    transition: background-color 0.2s ease-in-out;
    font-size: 0.875rem;

    &:hover {
      background-color: #1d4ed8;
    }

    &:focus {
      outline: none;
      box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
    }
  }

  /* Modal container - content styling only */
  .modal-container {
    width: 100%;
    padding: 1rem;
  }

  /* Title styling */
  .modal-title {
    font-size: 1rem;
    font-weight: 600;
    color: #1f2937;
    margin-bottom: 1rem;
  }

  .section-title {
    font-size: 0.875rem;
    font-weight: 600;
    color: #1f2937;
    margin-bottom: 0.5rem;
  }
}

/* Responsive adjustments */
@media (max-width: 640px) {
  .traveller-selection {
    .modal-container {
      max-height: calc(100vh - var(--dropdown-top, 150px) - 1rem);
    }

    .number-grid {
      gap: 0.25rem;
      flex-wrap: nowrap;
      overflow-x: auto;
      justify-content: flex-start;
    }

    .number-option {
      width: 1.5rem;
      height: 1.5rem;
      font-size: 0.625rem;
      flex-shrink: 0;
    }

    .class-selection-container {
      flex-direction: column;
      gap: 0.5rem;
    }

    .class-selection-item {
      flex: none;
    }
  }
}

/* Extra small screens */
@media (max-width: 480px) {
  .traveller-selection {
    .number-grid {
      gap: 0.125rem;
      flex-wrap: nowrap;
      overflow-x: auto;
    }

    .number-option {
      width: 1.25rem;
      height: 1.25rem;
      font-size: 0.5rem;
      flex-shrink: 0;
    }
  }
}
