/* Import SCSS variables and color utilities */
@use './styles/variables' as *;
@use './styles/color-utilities';

@tailwind base;
@tailwind components;
@tailwind utilities;

/* Custom button styles */
@layer utilities {
  .rounded-button {
    border-radius: 0.75rem !important;
  }
}

/* Custom scrollbar styles */
@layer components {
  .custom-scrollbar {
    /* Firefox */
    scrollbar-width: thin;
    scrollbar-color: $neutral_300 $neutral_50;
  }

  .custom-scrollbar::-webkit-scrollbar {
    width: 6px;
  }

  .custom-scrollbar::-webkit-scrollbar-track {
    background: $neutral_50;
    border-radius: 3px;
  }

  .custom-scrollbar::-webkit-scrollbar-thumb {
    background: $neutral_300;
    border-radius: 3px;
  }

  .custom-scrollbar::-webkit-scrollbar-thumb:hover {
    background: $neutral_400;
  }
}
