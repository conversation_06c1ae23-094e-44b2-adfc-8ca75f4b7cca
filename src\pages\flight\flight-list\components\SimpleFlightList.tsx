import React from 'react';
import type { FlightListItem } from '../../../models/flight/flight-list-models';

interface SimpleFlightListProps {
  flights: FlightListItem[];
  isLoading: boolean;
  title?: string;
}

const SimpleFlightList: React.FC<SimpleFlightListProps> = ({
  flights,
  isLoading,
  title = "Flight Results"
}) => {
  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-slate-600 font-medium">Loading flights...</p>
        </div>
      </div>
    );
  }

  if (flights.length === 0) {
    return (
      <div className="text-center py-12">
        <div className="text-gray-400 mb-4">
          <i className="fas fa-plane text-4xl"></i>
        </div>
        <h3 className="text-lg font-medium text-gray-900 mb-2">No flights found</h3>
        <p className="text-gray-500">Try adjusting your search criteria</p>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      <h2 className="text-xl font-semibold text-gray-900">{title}</h2>
      <div className="space-y-3">
        {flights.map((flight) => (
          <div
            key={flight.id}
            className="bg-white rounded-lg border border-gray-200 p-4 hover:shadow-md transition-shadow cursor-pointer"
          >
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-4">
                {/* Airline */}
                <div className="flex items-center space-x-2">
                  <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                    <span className="text-xs font-medium text-blue-600">
                      {flight.airline.code}
                    </span>
                  </div>
                  <span className="text-sm font-medium text-gray-900">
                    {flight.airline.name}
                  </span>
                </div>

                {/* Flight Route */}
                <div className="flex items-center space-x-2">
                  <div className="text-center">
                    <div className="text-lg font-semibold text-gray-900">
                      {flight.departure.time}
                    </div>
                    <div className="text-xs text-gray-500">
                      {flight.departure.airport}
                    </div>
                  </div>
                  
                  <div className="flex items-center space-x-1 px-2">
                    <div className="w-2 h-2 bg-gray-300 rounded-full"></div>
                    <div className="w-8 h-px bg-gray-300"></div>
                    <div className="text-xs text-gray-500">{flight.duration}</div>
                    <div className="w-8 h-px bg-gray-300"></div>
                    <div className="w-2 h-2 bg-gray-300 rounded-full"></div>
                  </div>
                  
                  <div className="text-center">
                    <div className="text-lg font-semibold text-gray-900">
                      {flight.arrival.time}
                    </div>
                    <div className="text-xs text-gray-500">
                      {flight.arrival.airport}
                    </div>
                  </div>
                </div>

                {/* Stops */}
                <div className="text-center">
                  <div className="text-sm font-medium text-gray-900">
                    {flight.stops === 0 ? 'Non-stop' : `${flight.stops} stop${flight.stops > 1 ? 's' : ''}`}
                  </div>
                  {flight.connections.length > 0 && (
                    <div className="text-xs text-gray-500">
                      via {flight.connections[0].airportName}
                    </div>
                  )}
                </div>
              </div>

              {/* Price and Details */}
              <div className="text-right">
                <div className="text-2xl font-bold text-blue-600">
                  {flight.price.currency} {flight.price.amount.toLocaleString()}
                </div>
                <div className="text-xs text-gray-500">
                  {flight.refundable ? 'Refundable' : 'Non-refundable'}
                </div>
                <div className="text-xs text-gray-500">
                  {flight.availability.seats} seats left
                </div>
                <div className="text-xs text-gray-500">
                  {flight.availability.cabinClass}
                </div>
              </div>
            </div>

            {/* Additional Info */}
            <div className="mt-3 pt-3 border-t border-gray-100 flex items-center justify-between text-xs text-gray-500">
              <div className="flex items-center space-x-4">
                <span>Baggage: {flight.baggage.checked}</span>
                <span>Fare: {flight.fareType}</span>
                {flight.performanceData?.cacheHit && (
                  <span className="text-green-600">Cached Result</span>
                )}
              </div>
              {flight.performanceData && (
                <span>Response: {flight.performanceData.responseTimeMs}ms</span>
              )}
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default SimpleFlightList;
