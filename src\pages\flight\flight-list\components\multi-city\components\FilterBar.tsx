import React from 'react';

interface FilterBarProps {
  isVisible: boolean;
}

const FilterBar: React.FC<FilterBarProps> = ({ isVisible }) => {
  if (!isVisible) return null;

  return (
    <div className="bg-gray-50 p-3 border-b overflow-x-auto">
      <div className="flex space-x-2">
        <div className="bg-white px-3 py-1 rounded border text-sm cursor-pointer whitespace-nowrap">
          <i className="fas fa-plane-departure mr-1 text-blue-500"></i>
          All Airlines
        </div>
        <div className="bg-white px-3 py-1 rounded border text-sm cursor-pointer whitespace-nowrap">
          <i className="fas fa-dollar-sign mr-1 text-green-500"></i>
          Price: Any
        </div>
        <div className="bg-white px-3 py-1 rounded border text-sm cursor-pointer whitespace-nowrap">
          <i className="fas fa-exchange-alt mr-1 text-orange-500"></i>
          Stops: Any
        </div>
        <div className="bg-white px-3 py-1 rounded border text-sm cursor-pointer whitespace-nowrap">
          <i className="fas fa-clock mr-1 text-purple-500"></i>
          Departure Time
        </div>
        <div className="bg-white px-3 py-1 rounded border text-sm cursor-pointer whitespace-nowrap">
          <i className="fas fa-hourglass-half mr-1 text-red-500"></i>
          Duration
        </div>
        <div className="bg-white px-3 py-1 rounded border text-sm cursor-pointer whitespace-nowrap">
          <i className="fas fa-suitcase mr-1 text-blue-500"></i>
          Baggage
        </div>
        <div className="bg-white px-3 py-1 rounded border text-sm cursor-pointer whitespace-nowrap">
          <i className="fas fa-tag mr-1 text-yellow-500"></i>
          Fare Type
        </div>
      </div>
    </div>
  );
};

export default FilterBar;
