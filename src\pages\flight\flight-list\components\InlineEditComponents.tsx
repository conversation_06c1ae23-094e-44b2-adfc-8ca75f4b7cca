import React, { useState, useRef, useEffect } from 'react';
import type { FlightSearchData } from '../../../../contexts/FlightSearchContext';
import { parseAirportString, formatAirportDisplay } from '../../../../utils/airportUtils';

// Import the individual components we'll reuse
import AirportSearch from '../../../../components/flight/flight-search-form/components/airport-search/AirportSearch';
import TravelClassPicker from '../../../../components/flight/flight-search-form/components/travel-class/TravelClassPicker';

interface ClassOption {
  id: string;
  label: string;
}

interface InlineEditComponentsProps {
  activeComponent: 'airports' | 'dates' | 'passengers' | 'class';
  currentSearchData: FlightSearchData;
  onSearchUpdate: (searchData: FlightSearchData) => void;
  onClose: () => void;
  classOptions: ClassOption[];
  formatDate: (dateString: string) => string;
}

const InlineEditComponents: React.FC<InlineEditComponentsProps> = ({
  activeComponent,
  currentSearchData,
  onSearchUpdate,
  onClose,
  classOptions,
  formatDate
}) => {
  // Local state for editing
  const [localFromAirport, setLocalFromAirport] = useState(formatAirportDisplay(currentSearchData.from));
  const [localToAirport, setLocalToAirport] = useState(formatAirportDisplay(currentSearchData.to));
  const [localPassengers, setLocalPassengers] = useState(currentSearchData.passengers);
  const [localClass, setLocalClass] = useState(currentSearchData.class);
  const [localDates, setLocalDates] = useState({
    departure: currentSearchData.depart.toISOString().split('T')[0],
    return: currentSearchData.return ? currentSearchData.return.toISOString().split('T')[0] : ''
  });

  // Airport search states
  const [fromSearchTerm, setFromSearchTerm] = useState('');
  const [toSearchTerm, setToSearchTerm] = useState('');
  const [showFromDropdown, setShowFromDropdown] = useState(false);
  const [showToDropdown, setShowToDropdown] = useState(false);

  // Date picker states - simplified for MultiDatePicker
  const [showDatePicker, setShowDatePicker] = useState(false);

  // Passenger/class picker states
  const [showTravelClassDropdown, setShowTravelClassDropdown] = useState(false);

  // Loading state for save operations
  const [isSaving, setIsSaving] = useState(false);

  // Error state for validation
  const [validationErrors, setValidationErrors] = useState<{
    airports?: string;
    dates?: string;
    passengers?: string;
  }>({});

  const componentRef = useRef<HTMLDivElement>(null);
  const fromDropdownRef = useRef<HTMLDivElement>(null);
  const toDropdownRef = useRef<HTMLDivElement>(null);
  const modalRef = useRef<HTMLDivElement>(null);

  // Close dropdowns when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (fromDropdownRef.current && !fromDropdownRef.current.contains(event.target as Node)) {
        setShowFromDropdown(false);
      }
      if (toDropdownRef.current && !toDropdownRef.current.contains(event.target as Node)) {
        setShowToDropdown(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  // Auto-scroll modal into view when opened
  useEffect(() => {
    if (activeComponent && modalRef.current) {
      const modal = modalRef.current;
      const rect = modal.getBoundingClientRect();
      const viewportHeight = window.innerHeight;
      const viewportWidth = window.innerWidth;

      // Check if modal is fully visible
      const isFullyVisible =
        rect.top >= 0 &&
        rect.left >= 0 &&
        rect.bottom <= viewportHeight &&
        rect.right <= viewportWidth;

      if (!isFullyVisible) {
        // Calculate optimal scroll position to center modal in viewport
        const modalCenterY = rect.top + rect.height / 2;
        const viewportCenterY = viewportHeight / 2;
        const scrollOffset = modalCenterY - viewportCenterY;

        // Smooth scroll to position modal optimally
        window.scrollBy({
          top: scrollOffset,
          behavior: 'smooth'
        });
      }
    }
  }, [activeComponent]);

  // Sample airports data (in real app, this would come from API)
  const airports = [
    { code: 'JFK', city: 'New York', name: 'John F. Kennedy International Airport', country: 'United States' },
    { code: 'LAX', city: 'Los Angeles', name: 'Los Angeles International Airport', country: 'United States' },
    { code: 'LHR', city: 'London', name: 'Heathrow Airport', country: 'United Kingdom' },
    { code: 'CDG', city: 'Paris', name: 'Charles de Gaulle Airport', country: 'France' },
    { code: 'DXB', city: 'Dubai', name: 'Dubai International Airport', country: 'United Arab Emirates' },
    { code: 'NRT', city: 'Tokyo', name: 'Narita International Airport', country: 'Japan' },
    { code: 'SIN', city: 'Singapore', name: 'Singapore Changi Airport', country: 'Singapore' },
    { code: 'SYD', city: 'Sydney', name: 'Sydney Kingsford Smith Airport', country: 'Australia' }
  ];

  // Validation function
  const validateChanges = () => {
    const errors: typeof validationErrors = {};

    // Validate airports
    const fromAirport = parseAirportString(localFromAirport, airports);
    const toAirport = parseAirportString(localToAirport, airports);

    if (!fromAirport?.iata || !toAirport?.iata) {
      errors.airports = 'Please select valid departure and destination airports';
    } else if (fromAirport.iata === toAirport.iata) {
      errors.airports = 'Departure and destination airports cannot be the same';
    }

    // Validate dates
    const departureDate = new Date(localDates.departure);
    const returnDate = localDates.return ? new Date(localDates.return) : null;
    const today = new Date();
    today.setHours(0, 0, 0, 0);

    if (isNaN(departureDate.getTime())) {
      errors.dates = 'Please select a valid departure date';
    } else if (departureDate < today) {
      errors.dates = 'Departure date cannot be in the past';
    } else if (currentSearchData.tripType === 'roundTrip') {
      if (!returnDate || isNaN(returnDate.getTime())) {
        errors.dates = 'Please select a valid return date for round trip';
      } else if (returnDate < departureDate) {
        errors.dates = 'Return date cannot be before departure date';
      }
    }

    // Validate passengers
    const totalPassengers = localPassengers.adults + localPassengers.children + localPassengers.infants;
    if (totalPassengers === 0) {
      errors.passengers = 'At least one passenger is required';
    } else if (localPassengers.adults === 0) {
      errors.passengers = 'At least one adult is required';
    } else if (localPassengers.infants > localPassengers.adults) {
      errors.passengers = 'Number of infants cannot exceed number of adults';
    }

    setValidationErrors(errors);
    return Object.keys(errors).length === 0;
  };

  // Handle save changes
  const handleSave = async () => {
    // Validate before saving
    if (!validateChanges()) {
      return;
    }

    setIsSaving(true);

    try {
      // Parse airports with proper fallback
      const fromAirport = parseAirportString(localFromAirport, airports) || currentSearchData.from;
      const toAirport = parseAirportString(localToAirport, airports) || currentSearchData.to;

      // Validate dates
      const departureDate = new Date(localDates.departure);
      const returnDate = localDates.return ? new Date(localDates.return) : null;

      const updatedSearchData: FlightSearchData = {
        ...currentSearchData,
        from: fromAirport,
        to: toAirport,
        passengers: localPassengers,
        class: localClass,
        depart: departureDate,
        return: returnDate
      };

      console.log('💾 Saving search changes:', updatedSearchData);

      // Close the edit interface immediately for better UX
      onClose();

      // Trigger the search update which will show loading and make API call
      await onSearchUpdate(updatedSearchData);
    } catch (error) {
      console.error('Error saving search changes:', error);
    } finally {
      setIsSaving(false);
    }
  };

  // Handle airport selection
  const handleFromAirportSelect = (airport: string) => {
    setLocalFromAirport(airport);
    setShowFromDropdown(false);
    // Clear validation errors when user makes changes
    setValidationErrors(prev => ({ ...prev, airports: undefined }));
  };

  const handleToAirportSelect = (airport: string) => {
    setLocalToAirport(airport);
    setShowToDropdown(false);
    // Clear validation errors when user makes changes
    setValidationErrors(prev => ({ ...prev, airports: undefined }));
  };

  // Handle passenger changes with proper validation
  const handlePassengerChange = (type: 'adults' | 'children' | 'infants', action: 'increase' | 'decrease') => {
    setLocalPassengers(prev => {
      const newValue = action === 'increase' ? prev[type] + 1 : prev[type] - 1;

      // Validation rules
      if (type === 'adults') {
        // Adults: minimum 1, maximum 9
        return {
          ...prev,
          [type]: Math.max(1, Math.min(9, newValue))
        };
      } else if (type === 'children') {
        // Children: minimum 0, maximum 8
        return {
          ...prev,
          [type]: Math.max(0, Math.min(8, newValue))
        };
      } else if (type === 'infants') {
        // Infants: minimum 0, maximum equal to adults (1 infant per adult)
        return {
          ...prev,
          [type]: Math.max(0, Math.min(prev.adults, newValue))
        };
      }

      return prev;
    });

    // Clear validation errors when user makes changes
    setValidationErrors(prev => ({ ...prev, passengers: undefined }));
  };

  // Handle date changes from MultiDatePicker
  const handleDatesChange = (dates: { departure: string; return: string }) => {
    setLocalDates(dates);
    // Clear validation errors when user makes changes
    setValidationErrors(prev => ({ ...prev, dates: undefined }));
  };

  // Handle swap airports
  const handleSwapAirports = () => {
    const temp = localFromAirport;
    setLocalFromAirport(localToAirport);
    setLocalToAirport(temp);
  };

  // Position the modal with professional viewport handling
  const getModalStyle = () => {
    const viewportHeight = window.innerHeight;
    const viewportWidth = window.innerWidth;

    // Calculate optimal positioning
    const isMobile = viewportWidth < 768;
    const maxHeight = isMobile ? '95vh' : '90vh';
    const padding = isMobile ? '1rem' : '2rem';

    return {
      position: 'fixed' as const,
      top: '50%',
      left: '50%',
      transform: 'translate(-50%, -50%)',
      zIndex: 10001,
      maxHeight,
      width: isMobile ? `calc(100vw - ${padding})` : 'auto',
      maxWidth: isMobile ? 'none' : '95vw',
      overflow: 'hidden', // Let individual sections handle overflow
    };
  };

  // Handle modal scroll lock and focus management
  useEffect(() => {
    if (activeComponent) {
      // Lock body scroll when modal is open
      const originalOverflow = document.body.style.overflow;
      document.body.style.overflow = 'hidden';

      // Focus management for accessibility
      if (modalRef.current) {
        modalRef.current.focus();
      }

      return () => {
        document.body.style.overflow = originalOverflow;
      };
    }
  }, [activeComponent]);

  const renderAirportsEditor = () => (
    <>
      {/* Backdrop */}
      <div
        className="fixed inset-0 bg-black bg-opacity-50 z-[10000] backdrop-blur-sm"
        onClick={onClose}
        aria-hidden="true"
      ></div>
      {/* Modal */}
      <div
        ref={modalRef}
        style={getModalStyle()}
        role="dialog"
        aria-modal="true"
        aria-labelledby="airports-modal-title"
        tabIndex={-1}
      >
        <div className="bg-white rounded-xl shadow-2xl border border-slate-200 max-w-2xl w-full overflow-hidden">
          <div className="p-6 max-h-[90vh] overflow-y-auto">
            <div className="flex items-center justify-between mb-6">
              <h3 id="airports-modal-title" className="text-lg font-semibold text-slate-800 flex items-center">
                <i className="fas fa-plane text-blue-600 mr-2"></i>
                Edit Route
              </h3>
              <button
                onClick={onClose}
                className="text-slate-400 hover:text-slate-600 transition-colors p-2 hover:bg-slate-100 rounded-lg"
                title="Close"
              >
                <i className="fas fa-times"></i>
              </button>
            </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4 relative">
          {/* From Airport */}
          <div className="relative" ref={fromDropdownRef}>
            <label className="block text-sm font-medium text-slate-700 mb-2">From</label>
            <div className="relative">
              <input
                type="text"
                value={fromSearchTerm || localFromAirport}
                onChange={(e) => {
                  setFromSearchTerm(e.target.value);
                  setShowFromDropdown(true);
                }}
                onFocus={() => setShowFromDropdown(true)}
                className="w-full pl-10 pr-10 py-3 border border-slate-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                placeholder="City or airport"
              />
              <i className="fas fa-plane-departure absolute left-3 top-1/2 transform -translate-y-1/2 text-blue-600"></i>
              {localFromAirport && (
                <button
                  onClick={() => {
                    setLocalFromAirport('');
                    setFromSearchTerm('');
                    setShowFromDropdown(false);
                  }}
                  className="absolute right-3 top-1/2 transform -translate-y-1/2 text-slate-400 hover:text-red-500 transition-colors"
                >
                  <i className="fas fa-times text-xs"></i>
                </button>
              )}
            </div>
            {showFromDropdown && (
              <div className="absolute left-0 mt-1 w-full bg-white rounded-xl shadow-xl z-50 max-h-60 overflow-y-auto border border-slate-200">
                <AirportSearch
                  searchTerm={fromSearchTerm || localFromAirport}
                  airports={airports}
                  showDropdown={showFromDropdown}
                  onAirportSelect={handleFromAirportSelect}
                  icon="fas fa-plane-departure"
                />
              </div>
            )}
          </div>

          {/* Swap Button */}
          <div className="hidden md:flex items-center justify-center absolute left-1/2 top-1/2 transform -translate-x-1/2 -translate-y-1/2 z-10">
            <button
              onClick={handleSwapAirports}
              className="w-10 h-10 bg-white border-2 border-slate-200 rounded-full flex items-center justify-center text-blue-600 hover:bg-blue-50 hover:border-blue-300 transition-all shadow-sm"
            >
              <i className="fas fa-exchange-alt"></i>
            </button>
          </div>

          {/* To Airport */}
          <div className="relative" ref={toDropdownRef}>
            <label className="block text-sm font-medium text-slate-700 mb-2">To</label>
            <div className="relative">
              <input
                type="text"
                value={toSearchTerm || localToAirport}
                onChange={(e) => {
                  setToSearchTerm(e.target.value);
                  setShowToDropdown(true);
                }}
                onFocus={() => setShowToDropdown(true)}
                className="w-full pl-10 pr-10 py-3 border border-slate-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                placeholder="City or airport"
              />
              <i className="fas fa-plane-arrival absolute left-3 top-1/2 transform -translate-y-1/2 text-green-600"></i>
              {localToAirport && (
                <button
                  onClick={() => {
                    setLocalToAirport('');
                    setToSearchTerm('');
                    setShowToDropdown(false);
                  }}
                  className="absolute right-3 top-1/2 transform -translate-y-1/2 text-slate-400 hover:text-red-500 transition-colors"
                >
                  <i className="fas fa-times text-xs"></i>
                </button>
              )}
            </div>
            {showToDropdown && (
              <div className="absolute left-0 mt-1 w-full bg-white rounded-xl shadow-xl z-50 max-h-60 overflow-y-auto border border-slate-200">
                <AirportSearch
                  searchTerm={toSearchTerm || localToAirport}
                  airports={airports}
                  showDropdown={showToDropdown}
                  onAirportSelect={handleToAirportSelect}
                  icon="fas fa-plane-arrival"
                />
              </div>
            )}
          </div>
        </div>

        {/* Error Display */}
        {validationErrors.airports && (
          <div className="mb-4 p-3 bg-red-50 border border-red-200 rounded-lg">
            <div className="flex items-center text-red-700">
              <i className="fas fa-exclamation-circle mr-2"></i>
              <span className="text-sm">{validationErrors.airports}</span>
            </div>
          </div>
        )}

            <div className="flex justify-end space-x-3 pt-4 border-t border-slate-200">
              <button
                onClick={onClose}
                className="px-6 py-2.5 text-sm font-medium text-slate-700 bg-white border border-slate-300 rounded-lg hover:bg-slate-50 transition-colors"
              >
                Cancel
              </button>
              <button
                onClick={handleSave}
                disabled={isSaving}
                className="px-6 py-2.5 text-sm font-medium bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50 flex items-center"
              >
                {isSaving ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                    Updating...
                  </>
                ) : (
                  'Update Route'
                )}
              </button>
            </div>
          </div>
        </div>
      </div>
    </>
  );

  const renderDatesEditor = () => {
    // State for tracking selection mode
    const [selectionMode, setSelectionMode] = useState<'departure' | 'return'>('departure');

    // Generate calendar days for current month
    const generateCalendarDays = (year: number, month: number) => {
      const firstDay = new Date(year, month, 1);
      const lastDay = new Date(year, month + 1, 0);
      const daysInMonth = lastDay.getDate();
      const startingDayOfWeek = (firstDay.getDay() + 6) % 7; // Monday = 0

      const days = [];

      // Add empty cells for days before the first day of the month
      for (let i = 0; i < startingDayOfWeek; i++) {
        days.push(null);
      }

      // Add days of the month
      for (let day = 1; day <= daysInMonth; day++) {
        const date = new Date(year, month, day);
        const dateString = date.toISOString().split('T')[0];
        days.push({
          day,
          date: dateString,
          price: Math.floor(Math.random() * 200) + 150 // Mock price
        });
      }

      return days;
    };

    const isDateInPast = (dateString: string) => {
      const today = new Date();
      today.setHours(0, 0, 0, 0);
      const date = new Date(dateString);
      return date < today;
    };

    const isDepartureDate = (dateString: string) => {
      return dateString === localDates.departure;
    };

    const isReturnDate = (dateString: string) => {
      return dateString === localDates.return;
    };

    const isDateInRange = (dateString: string) => {
      if (!localDates.departure || !localDates.return) return false;
      const date = new Date(dateString);
      const departure = new Date(localDates.departure);
      const returnDate = new Date(localDates.return);
      return date > departure && date < returnDate;
    };

    const handleDateClick = (dateString: string) => {
      const selectedDate = new Date(dateString);
      selectedDate.setHours(0, 0, 0, 0);
      const today = new Date();
      today.setHours(0, 0, 0, 0);

      // Don't allow past dates
      if (selectedDate < today) {
        return;
      }

      if (currentSearchData.tripType === 'oneWay') {
        // For one-way, just set departure
        setLocalDates({
          departure: dateString,
          return: ''
        });
        return;
      }

      // Round trip logic: Smart date selection
      if (!localDates.departure || (localDates.departure && localDates.return)) {
        // First click or both dates already selected - set departure and clear return
        setLocalDates({
          departure: dateString,
          return: ''
        });
        setSelectionMode('return');
      } else if (localDates.departure && !localDates.return) {
        // Second click - set return
        const departureDate = new Date(localDates.departure);
        departureDate.setHours(0, 0, 0, 0);

        if (selectedDate < departureDate) {
          // If return is before departure, set as new departure and clear return
          setLocalDates({
            departure: dateString,
            return: ''
          });
          setSelectionMode('return');
        } else {
          // Valid return date (same day or after departure)
          setLocalDates({
            departure: localDates.departure,
            return: dateString
          });
          setSelectionMode('departure'); // Reset for next selection
        }
      }
    };

    const currentMonth = new Date();
    const nextMonth = new Date();
    nextMonth.setMonth(nextMonth.getMonth() + 1);

    const getSelectionText = () => {
      if (currentSearchData.tripType === 'oneWay') {
        return 'Select departure date';
      }
      if (!localDates.departure) {
        return 'Select departure date';
      }
      if (!localDates.return) {
        return 'Now select return date';
      }
      return 'Click any date to change selection';
    };

    return (
      <>
        {/* Backdrop */}
        <div
          className="fixed inset-0 bg-black bg-opacity-50 z-[10000] backdrop-blur-sm"
          onClick={onClose}
          aria-hidden="true"
        ></div>
        {/* Modal */}
        <div
          ref={modalRef}
          style={getModalStyle()}
          role="dialog"
          aria-modal="true"
          aria-labelledby="dates-modal-title"
          tabIndex={-1}
        >
          <div className="bg-white rounded-xl shadow-2xl border border-slate-200 max-w-4xl w-full overflow-hidden">
            <div className="p-6 max-h-[90vh] overflow-y-auto">
              <div className="flex items-center justify-between mb-6">
                <h3 id="dates-modal-title" className="text-lg font-semibold text-slate-800 flex items-center">
                  <i className="fas fa-calendar text-blue-600 mr-2"></i>
                  Select Dates
                </h3>
                <button
                  onClick={onClose}
                  className="text-slate-400 hover:text-slate-600 transition-colors p-2 hover:bg-slate-100 rounded-lg"
                  title="Close"
                >
                  <i className="fas fa-times"></i>
                </button>
              </div>

              {/* Selection Instructions */}
              <div className="text-center mb-6 p-4 bg-blue-50 rounded-lg">
                <p className="text-sm font-medium text-blue-800">{getSelectionText()}</p>
            {localDates.departure && (
              <div className="flex items-center justify-center space-x-4 mt-2">
                <div className="flex items-center space-x-2">
                  <div className="w-3 h-3 bg-blue-600 rounded-full"></div>
                  <span className="text-sm text-blue-700">
                    Departure: {formatDate(localDates.departure)}
                  </span>
                </div>
                {localDates.return && (
                  <>
                    <div className="w-px h-4 bg-blue-300"></div>
                    <div className="flex items-center space-x-2">
                      <div className="w-3 h-3 bg-green-600 rounded-full"></div>
                      <span className="text-sm text-green-700">
                        Return: {formatDate(localDates.return)}
                      </span>
                    </div>
                  </>
                )}
              </div>
            )}
          </div>

          {/* Calendar Grid */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
            {/* Current Month */}
            <div>
              <div className="flex items-center justify-center mb-3">
                <h4 className="text-sm font-semibold text-slate-800">
                  {currentMonth.toLocaleDateString('en-US', { month: 'long', year: 'numeric' })}
                </h4>
              </div>

              {/* Weekday headers */}
              <div className="grid grid-cols-7 gap-1 mb-2">
                {['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'].map(day => (
                  <div key={day} className="text-center text-xs font-medium text-slate-500 py-2">
                    {day}
                  </div>
                ))}
              </div>

              {/* Calendar days */}
              <div className="grid grid-cols-7 gap-1">
                {generateCalendarDays(currentMonth.getFullYear(), currentMonth.getMonth()).map((dayData, index) => (
                  <div key={index} className="text-center">
                    {dayData ? (
                      <button
                        onClick={() => handleDateClick(dayData.date)}
                        disabled={isDateInPast(dayData.date)}
                        className={`w-full p-2 rounded-lg text-xs transition-all ${
                          isDepartureDate(dayData.date)
                            ? 'bg-blue-600 text-white shadow-sm'
                            : isReturnDate(dayData.date)
                            ? 'bg-green-600 text-white shadow-sm'
                            : isDateInRange(dayData.date)
                            ? 'bg-blue-100 text-blue-800'
                            : isDateInPast(dayData.date)
                            ? 'text-slate-300 cursor-not-allowed'
                            : 'hover:bg-slate-100 text-slate-700 cursor-pointer'
                        }`}
                      >
                        <div className="font-medium">{dayData.day}</div>
                        <div className="text-xs">${dayData.price}</div>
                      </button>
                    ) : (
                      <div className="p-2"></div>
                    )}
                  </div>
                ))}
              </div>
            </div>

            {/* Next Month */}
            <div>
              <div className="flex items-center justify-center mb-3">
                <h4 className="text-sm font-semibold text-slate-800">
                  {nextMonth.toLocaleDateString('en-US', { month: 'long', year: 'numeric' })}
                </h4>
              </div>

              {/* Weekday headers */}
              <div className="grid grid-cols-7 gap-1 mb-2">
                {['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'].map(day => (
                  <div key={day} className="text-center text-xs font-medium text-slate-500 py-2">
                    {day}
                  </div>
                ))}
              </div>

              {/* Calendar days */}
              <div className="grid grid-cols-7 gap-1">
                {generateCalendarDays(nextMonth.getFullYear(), nextMonth.getMonth()).map((dayData, index) => (
                  <div key={index} className="text-center">
                    {dayData ? (
                      <button
                        onClick={() => handleDateClick(dayData.date)}
                        disabled={isDateInPast(dayData.date)}
                        className={`w-full p-2 rounded-lg text-xs transition-all ${
                          isDepartureDate(dayData.date)
                            ? 'bg-blue-600 text-white shadow-sm'
                            : isReturnDate(dayData.date)
                            ? 'bg-green-600 text-white shadow-sm'
                            : isDateInRange(dayData.date)
                            ? 'bg-blue-100 text-blue-800'
                            : isDateInPast(dayData.date)
                            ? 'text-slate-300 cursor-not-allowed'
                            : 'hover:bg-slate-100 text-slate-700 cursor-pointer'
                        }`}
                      >
                        <div className="font-medium">{dayData.day}</div>
                        <div className="text-xs">${dayData.price}</div>
                      </button>
                    ) : (
                      <div className="p-2"></div>
                    )}
                  </div>
                ))}
              </div>
            </div>
          </div>

          {/* Error Display */}
          {validationErrors.dates && (
            <div className="mb-4 p-3 bg-red-50 border border-red-200 rounded-lg">
              <div className="flex items-center text-red-700">
                <i className="fas fa-exclamation-circle mr-2"></i>
                <span className="text-sm">{validationErrors.dates}</span>
              </div>
            </div>
          )}

              <div className="flex justify-end space-x-3 pt-4 border-t border-slate-200">
                <button
                  onClick={onClose}
                  className="px-6 py-2.5 text-sm font-medium text-slate-700 bg-white border border-slate-300 rounded-lg hover:bg-slate-50 transition-colors"
                >
                  Cancel
                </button>
                <button
                  onClick={handleSave}
                  disabled={!localDates.departure || (currentSearchData.tripType === 'roundTrip' && !localDates.return) || isSaving}
                  className="px-6 py-2.5 text-sm font-medium bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center"
                >
                  {isSaving ? (
                    <>
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                      Updating...
                    </>
                  ) : (
                    'Update Dates'
                  )}
                </button>
              </div>
            </div>
          </div>
        </div>
    </>
    );
  };

  // Continue with passengers and class editors in the next part...
  const renderPassengersEditor = () => (
    <>
      {/* Backdrop */}
      <div
        className="fixed inset-0 bg-black bg-opacity-50 z-[10000] backdrop-blur-sm"
        onClick={onClose}
        aria-hidden="true"
      ></div>
      {/* Modal */}
      <div
        ref={modalRef}
        style={getModalStyle()}
        role="dialog"
        aria-modal="true"
        aria-labelledby="passengers-modal-title"
        tabIndex={-1}
      >
        <div className="bg-white rounded-xl shadow-2xl border border-slate-200 max-w-md w-full overflow-hidden">
          <div className="p-6 max-h-[90vh] overflow-y-auto">
            <div className="flex items-center justify-between mb-6">
              <h3 id="passengers-modal-title" className="text-lg font-semibold text-slate-800 flex items-center">
                <i className="fas fa-users text-green-600 mr-2"></i>
                Edit Passengers
              </h3>
              <button
                onClick={onClose}
                className="text-slate-400 hover:text-slate-600 transition-colors p-2 hover:bg-slate-100 rounded-lg"
                title="Close"
              >
                <i className="fas fa-times"></i>
              </button>
            </div>

            <div className="space-y-6 mb-6">
              {/* Adults */}
              <div className="flex items-center justify-between">
            <div>
              <span className="font-medium text-slate-800">Adults</span>
              <span className="text-xs text-slate-500 ml-1">(12+ years)</span>
              <div className="text-xs text-slate-400 mt-1">Required: 1-9 adults</div>
            </div>
            <div className="flex items-center space-x-3">
              <button
                onClick={() => handlePassengerChange('adults', 'decrease')}
                disabled={localPassengers.adults <= 1}
                className="w-8 h-8 rounded-full border border-slate-300 flex items-center justify-center text-slate-600 hover:bg-slate-50 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                title={localPassengers.adults <= 1 ? 'Minimum 1 adult required' : 'Decrease adults'}
              >
                <i className="fas fa-minus text-xs"></i>
              </button>
              <span className="w-8 text-center font-medium text-lg">{localPassengers.adults}</span>
              <button
                onClick={() => handlePassengerChange('adults', 'increase')}
                disabled={localPassengers.adults >= 9}
                className="w-8 h-8 rounded-full border border-slate-300 flex items-center justify-center text-slate-600 hover:bg-slate-50 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                title={localPassengers.adults >= 9 ? 'Maximum 9 adults allowed' : 'Increase adults'}
              >
                <i className="fas fa-plus text-xs"></i>
              </button>
            </div>
          </div>

          {/* Children */}
          <div className="flex items-center justify-between">
            <div>
              <span className="font-medium text-slate-800">Children</span>
              <span className="text-xs text-slate-500 ml-1">(2-12 years)</span>
              <div className="text-xs text-slate-400 mt-1">Optional: 0-8 children</div>
            </div>
            <div className="flex items-center space-x-3">
              <button
                onClick={() => handlePassengerChange('children', 'decrease')}
                disabled={localPassengers.children <= 0}
                className="w-8 h-8 rounded-full border border-slate-300 flex items-center justify-center text-slate-600 hover:bg-slate-50 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                title={localPassengers.children <= 0 ? 'No children to remove' : 'Decrease children'}
              >
                <i className="fas fa-minus text-xs"></i>
              </button>
              <span className="w-8 text-center font-medium text-lg">{localPassengers.children}</span>
              <button
                onClick={() => handlePassengerChange('children', 'increase')}
                disabled={localPassengers.children >= 8}
                className="w-8 h-8 rounded-full border border-slate-300 flex items-center justify-center text-slate-600 hover:bg-slate-50 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                title={localPassengers.children >= 8 ? 'Maximum 8 children allowed' : 'Increase children'}
              >
                <i className="fas fa-plus text-xs"></i>
              </button>
            </div>
          </div>

          {/* Infants */}
          <div className="flex items-center justify-between">
            <div>
              <span className="font-medium text-slate-800">Infants</span>
              <span className="text-xs text-slate-500 ml-1">(Under 2 years)</span>
              <div className="text-xs text-slate-400 mt-1">Max: {localPassengers.adults} (1 per adult)</div>
            </div>
            <div className="flex items-center space-x-3">
              <button
                onClick={() => handlePassengerChange('infants', 'decrease')}
                disabled={localPassengers.infants <= 0}
                className="w-8 h-8 rounded-full border border-slate-300 flex items-center justify-center text-slate-600 hover:bg-slate-50 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                title={localPassengers.infants <= 0 ? 'No infants to remove' : 'Decrease infants'}
              >
                <i className="fas fa-minus text-xs"></i>
              </button>
              <span className="w-8 text-center font-medium text-lg">{localPassengers.infants}</span>
              <button
                onClick={() => handlePassengerChange('infants', 'increase')}
                disabled={localPassengers.infants >= localPassengers.adults}
                className="w-8 h-8 rounded-full border border-slate-300 flex items-center justify-center text-slate-600 hover:bg-slate-50 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                title={localPassengers.infants >= localPassengers.adults ? 'Maximum 1 infant per adult' : 'Increase infants'}
              >
                <i className="fas fa-plus text-xs"></i>
              </button>
            </div>
          </div>
            </div>

            {/* Error Display */}
            {validationErrors.passengers && (
              <div className="mb-6 p-4 bg-red-50 border border-red-200 rounded-lg">
                <div className="flex items-center text-red-700">
                  <i className="fas fa-exclamation-circle mr-2"></i>
                  <span className="text-sm">{validationErrors.passengers}</span>
                </div>
              </div>
            )}

            <div className="flex justify-end space-x-3 pt-4 border-t border-slate-200">
              <button
                onClick={onClose}
                className="px-6 py-2.5 text-sm font-medium text-slate-700 bg-white border border-slate-300 rounded-lg hover:bg-slate-50 transition-colors"
              >
                Cancel
              </button>
              <button
                onClick={handleSave}
                disabled={isSaving}
                className="px-6 py-2.5 text-sm font-medium bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors disabled:opacity-50 flex items-center"
              >
                {isSaving ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                    Updating...
                  </>
                ) : (
                  'Update Passengers'
                )}
              </button>
            </div>
          </div>
        </div>
      </div>
    </>
  );

  const renderClassEditor = () => (
    <>
      {/* Backdrop */}
      <div className="fixed inset-0 bg-black bg-opacity-50 z-[10000]" onClick={onClose}></div>
      {/* Modal */}
      <div ref={modalRef} style={getModalStyle()}>
        <div className="bg-white rounded-xl shadow-2xl border border-slate-200 max-w-sm w-full overflow-hidden">
          <div className="p-6 max-h-[90vh] overflow-y-auto">
            <div className="flex items-center justify-between mb-6">
              <h3 className="text-lg font-semibold text-slate-800 flex items-center">
                <i className="fas fa-chair text-purple-600 mr-2"></i>
                Edit Class
              </h3>
              <button
                onClick={onClose}
                className="text-slate-400 hover:text-slate-600 transition-colors p-2 hover:bg-slate-100 rounded-lg"
                title="Close"
              >
                <i className="fas fa-times"></i>
              </button>
            </div>

            <div className="space-y-3 mb-6">
              {classOptions.map((option) => (
                <button
                  key={option.id}
                  onClick={() => setLocalClass(option.id as any)}
                  className={`w-full p-4 text-left rounded-lg border transition-colors ${
                    localClass === option.id
                      ? 'bg-purple-50 border-purple-300 text-purple-800'
                      : 'bg-white border-slate-200 hover:bg-slate-50'
                  }`}
                >
                  <div className="flex items-center justify-between">
                    <span className="font-medium">{option.label}</span>
                    {localClass === option.id && (
                      <i className="fas fa-check text-purple-600"></i>
                    )}
                  </div>
                </button>
              ))}
            </div>

            <div className="flex justify-end space-x-3 pt-4 border-t border-slate-200">
              <button
                onClick={onClose}
                className="px-6 py-2.5 text-sm font-medium text-slate-700 bg-white border border-slate-300 rounded-lg hover:bg-slate-50 transition-colors"
              >
                Cancel
              </button>
              <button
                onClick={handleSave}
                disabled={isSaving}
                className="px-6 py-2.5 text-sm font-medium bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors disabled:opacity-50 flex items-center"
              >
                {isSaving ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                    Updating...
                  </>
                ) : (
                  'Update Class'
                )}
              </button>
            </div>
          </div>
        </div>
      </div>
    </>
  );

  // Render the appropriate editor based on activeComponent
  switch (activeComponent) {
    case 'airports':
      return renderAirportsEditor();
    case 'dates':
      return renderDatesEditor();
    case 'passengers':
      return renderPassengersEditor();
    case 'class':
      return renderClassEditor();
    default:
      return null;
  }
};

export default InlineEditComponents;
