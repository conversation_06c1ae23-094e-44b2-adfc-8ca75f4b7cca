  export interface FlightResponse {
    sh_price: boolean
    TUI: string
    Completed: boolean
    CeilingInfo: string
    TripType: any
    ElapsedTime: string
    Notices: any
    Msg: string[]
    Code: number
    Trips: Trip[]
    // ✅ Cache-related fields for proper cache detection (exact pattern implementation)
    cache_hit?: boolean  // API sometimes returns this field
    CacheHit?: boolean   // API sometimes returns this field
    DataSource?: string
    ResponseTimeMS?: number
  }

  export interface Trip {
    Journey: Journey[]
  }

  export interface Journey {
    Stops: number
    Seats: number
    ReturnIdentifier: any
    Index: string
    Provider: string
    FlightNo: string
    VAC: string
    MAC: string
    OAC: string
    ArrivalTime: string
    DepartureTime: string
    FareClass: string
    Duration: string
    GroupCount: number
    TotalFare: number
    GrossFare: number
    TotalCommission: number
    NetFare: number
    Hops: number
    Notice: string
    NoticeLink: string
    NoticeType: string
    Refundable: string
    Alliances: string
    Amenities: string
    Hold: boolean
    Connections: Connection[]
    From: string
    To: string
    FromName: string
    ToName: string
    AirlineName: string
    AirCraft: string
    RBD: string
    Cabin: string
    FBC: string
    FCBegin: any
    FCEnd: any
    FCType: string
    GFL: boolean
    Promo: string
    Recommended: boolean
    Premium: boolean
    JourneyKey: string
    FareKey: string
    PaxCategory: string
    PrivateFareType: string
    DealKey: any
    VACAirlineLogo: string
    MACAirlineLogo: string
    OACAirlineLogo: string
    //Rechange if needed
    isShowFareType: boolean //
    CreatedJourneyKey?: string
    isDisplay?: boolean
    SubFlights?: SubFlight[]
    ConnectionText?: string
    isSelect?: boolean
    isVisible: boolean
    ChannelCode?: string
    WpIndex?: string | null
    IsSchedule?: boolean
    Inclusions?: Inclusions
    // ✅ NEW: Terminal information fields
    DepartureTerminal?: string
    ArrivalTerminal?: string
    // ✅ NEW: Fare restriction metadata for error 1080 prevention
    fareRestrictions?: FareRestrictions
  }

  export interface Connection {
    Airport: string
    ArrAirportName: string
    Duration: string
    MAC: string
    Type: string
  }

  export interface SubFlight {
    Amount?: number
    Index?: string
    ChannelCode?: string
    Baggage?: string
    SeatSelection?: string | number
    Meal?: string
    Refund?: string
    FCType?: string
    isSelect?: boolean
  }

  export interface Inclusions {
    Baggage?: string
    Meals?: string | null
    PieceDescription?: string | number | null
  }

   export interface FareRestrictions {
    fareIdentifier: string;     // PUBLISHED, SPECIAL_RETURN, TJ_FLEX
    fareFamily: string;         // Derived from fareIdentifier
    requiresBundling: boolean;  // True for SPECIAL_RETURN fares
    compatibleFareIds: string[]; // Compatible fare IDs for round-trip
    specialFareWarning: string; // Warning message for UI
    fareType: string;           // REGULAR, SPECIAL_RETURN, PROMOTIONAL
    sri?: string;               // Special Return Identifier
    msri?: string[];            // Matched Special Return Identifiers
    isSpecialReturn: boolean;   // Quick check for special return fare
  }
