# API Services Documentation

This directory contains the restructured API services following industry best practices and standards.

## 🏗️ Architecture Overview

The API layer is built with a **modular, extensible architecture** that provides:

- **Base API Service**: Core functionality with interceptors, error handling, and token management
- **Specialized Services**: Domain-specific services (Flight, Auth) that extend the base
- **Singleton Pattern**: Ensures single instances across the application
- **Legacy Compatibility**: Maintains backward compatibility with existing code

## 📁 File Structure

```
services/api/
├── base-api.service.ts      # Core API functionality
├── flight-api.service.ts    # Flight-specific operations
├── auth-api.service.ts      # Authentication operations
├── index.ts                 # Central export point
└── README.md               # This documentation
```

## 🚀 Usage Examples

### Modern Approach (Recommended)

```typescript
import { flightApi, authApi } from 'services/api';

// Flight operations
const airports = await flightApi.searchAirport({ search_text: 'Delhi' });
const searchResults = await flightApi.callUnifiedSearch(searchBody);

// Authentication
const loginResult = await authApi.verifyLoginOtp({ email, otp });
const isAuthenticated = authApi.isAuthenticated();
```

### Service Factory Approach

```typescript
import { ApiServiceFactory } from 'services/api';

const flightService = ApiServiceFactory.getFlightService();
const authService = ApiServiceFactory.getAuthService();

// Use services
const results = await flightService.callPollingSearch(body, 10, 1000);
```

### Legacy Compatibility

```typescript
import FlightApiService from 'services/api';

// Works exactly like before
const results = await FlightApiService.callUnifiedSearch(body);
const token = FlightApiService.getToken();
```

## 🔧 Base API Service Features

### Automatic Token Management
- **JWT validation** with expiry checking
- **Automatic token injection** for protected endpoints
- **Token refresh** handling
- **Secure storage** in localStorage

### Request/Response Interceptors
- **Authentication headers** added automatically
- **Request/response logging** in development
- **Error handling** with standardized error format
- **Response metadata** (cache hits, response times)

### Retry Logic
- **Automatic retries** for network errors and 5xx responses
- **Exponential backoff** with configurable delays
- **Maximum retry attempts** configuration

### Error Handling
- **Standardized error format** across all APIs
- **401 handling** with automatic token cleanup
- **Network error** fallback strategies

## 🛡️ Security Features

### Token Security
- **JWT expiry validation** before requests
- **Automatic cleanup** on token expiration
- **Secure storage** with localStorage fallback

### Endpoint Protection
- **Whitelist approach** for public endpoints
- **Automatic authentication** for protected routes
- **Token validation** before sensitive operations

## 📊 Performance Features

### Caching
- **Response metadata** for cache hit detection
- **Cache-aware** request handling
- **Performance metrics** tracking

### Request Optimization
- **Polling mechanisms** for long-running operations
- **Seamless refresh** for cached data
- **Unified search workflow** optimization

## 🔄 Migration Guide

### From Old Structure
```typescript
// OLD
import FlightApiService from 'services/flight-api-service';

// NEW (Recommended)
import { flightApi } from 'services/api';

// NEW (Legacy Compatible)
import FlightApiService from 'services/api';
```

### Key Changes
1. **Modular structure** - Services are now separated by domain
2. **Better error handling** - Standardized error responses
3. **Enhanced logging** - Detailed request/response logging
4. **Token management** - Centralized and secure
5. **Type safety** - Better TypeScript support

## 🧪 Testing

### Health Check
```typescript
import { ApiServiceFactory } from 'services/api';

const health = await ApiServiceFactory.healthCheck();
console.log('API Health:', health);
```

### Service Initialization
```typescript
import { ApiServiceFactory } from 'services/api';

// Initialize all services at app startup
ApiServiceFactory.initializeServices();
```

## 🔍 API Methods Reference

### Flight API Service
- **Airport Operations**: `searchAirport()`, `getAllAirports()`
- **Search Operations**: `callUnifiedSearch()`, `callSeamlessRefreshSearch()`, `callPollingSearch()`
- **Flight Details**: `getFlightInfo()`, `getFlightSSR()`, `getFlightFareRule()`
- **Booking Operations**: `bookNowApi()`, `getBookingList()`, `getBookingDetails()`
- **Pricing**: `callSmartPricer()`, `callGetsPrice()`

### Auth API Service
- **Registration**: `registerSubmit()`
- **Login**: `loginSubmit()`, `verifyLoginOtp()`
- **OTP**: `verifyOtp()`
- **Token Management**: `setToken()`, `getToken()`, `clearToken()`, `isTokenValid()`
- **Session**: `isAuthenticated()`, `logout()`

## 🛠️ Configuration

### Environment Variables
```env
NEXT_PUBLIC_API_BASE_URL=https://api.seateleven.com/apis
NODE_ENV=development  # Enables detailed logging
```

### Custom Configuration
```typescript
import { BaseApiService } from 'services/api';

const customApi = new BaseApiService({
  baseURL: 'https://custom-api.com',
  timeout: 60000,
  retryAttempts: 5,
  enableLogging: true
});
```

## 📈 Monitoring & Debugging

### Request Logging
All requests are logged in development mode with:
- **Request details** (method, URL, data)
- **Response metadata** (status, response time, cache hits)
- **Error information** (status codes, error messages)

### Performance Metrics
- **Response times** tracked automatically
- **Cache hit rates** monitored
- **Retry attempts** logged

## 🔮 Future Enhancements

- **Request caching** with TTL
- **Offline support** with request queuing
- **GraphQL integration** for complex queries
- **Real-time updates** with WebSocket support
- **API versioning** support

## 🤝 Contributing

When adding new API methods:

1. **Extend appropriate service** (Flight, Auth, or create new)
2. **Add proper error handling** with try-catch blocks
3. **Include logging** for debugging
4. **Update TypeScript types** for better type safety
5. **Add to legacy compatibility** layer if needed

## 📞 Support

For questions or issues with the API services:
- Check the console logs for detailed error information
- Use the health check endpoint to verify service status
- Review the request/response logs in development mode
