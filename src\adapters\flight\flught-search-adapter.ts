import type { flight_search_form, FlightScheduleBody, Parameters } from "../../models/flight/flight-search.model";

interface BuildFlightScheduleBodyInput {
  form: flight_search_form;
  source: string;
  clientId: string;
  tui: string;
  parameters: Parameters;
  mode: string;
  isRefundable: boolean;
  isMultipleCarrier: boolean;
  preferedAirlines?: string | null;
  paymentType?: string;
  yth?: number;
}

export function buildFlightScheduleBody({
  form,
  source,
  clientId,
  tui,
  parameters,
  mode,
  isRefundable,
  isMultipleCarrier,
  preferedAirlines = null,
  paymentType,
  yth = 0,
}: BuildFlightScheduleBodyInput): FlightScheduleBody {
  const { FareType, SecType, cabin, travellers, trips } = form;

  return {
    FareType,
    SecType,
    ADT: travellers.adult,
    CHD: travellers.child,
    INF: travellers.infant,
    YTH: yth,
    Cabin: cabin,
    Source: source,
    ClientID: clientId,
    TUI: tui,
    Trips: trips,
    Parameters: parameters,
    Mode: mode,
    IsRefundable: isRefundable,
    IsMultipleCarrier: isMultipleCarrier,
    preferedAirlines,
    PaymentType: paymentType,
  };
}
