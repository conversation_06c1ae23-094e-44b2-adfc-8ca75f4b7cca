import React, { useEffect } from 'react';
import '../../flight-search-form/components/multi-date-picker/CalendarModal.scss';

interface SelectedDates {
  departure: string;
  return: string;
}

interface CalendarDay {
  day: number;
  date: string;
  price: number;
  uniqueKey?: string;
}

interface ModifyCalendarModalProps {
  showCalendar: boolean;
  selectedDates: SelectedDates;
  selectedTripType: 'roundTrip' | 'oneWay' | 'multiCity';
  dateSelectionState: 'departure' | 'return' | null;
  currentMonth: Date;
  nextMonth: Date;
  tripDays: string;
  calendarRef: React.RefObject<HTMLDivElement | null>;
  onClose: () => void;
  onDateSelect: (date: string) => void;
  onPrevMonth: () => void;
  onNextMonth: () => void;
  onDaysInput: (days: string) => void;
  generateCalendarDays: (year: number, month: number) => (CalendarDay | null)[];
  isDateInPast: (dateString: string) => boolean;
  isDateSelected: (dateString: string) => boolean;
  isDateInRange: (dateString: string) => boolean;
}

const ModifyCalendarModal: React.FC<ModifyCalendarModalProps> = ({
  showCalendar,
  selectedDates,
  selectedTripType,
  dateSelectionState,
  currentMonth,
  nextMonth,
  tripDays,
  calendarRef,
  onClose,
  onDateSelect,
  onPrevMonth,
  onNextMonth,
  onDaysInput,
  generateCalendarDays,
  isDateInPast,
  isDateSelected,
  isDateInRange
}) => {
  if (!showCalendar) return null;

  const renderCalendar = (month: Date, isNextMonth: boolean = false) => {
    const year = month.getFullYear();
    const monthIndex = month.getMonth();
    const days = generateCalendarDays(year, monthIndex);

    return (
      <div className="flex-1 min-w-[320px]">
        <div className="flex justify-between items-center mb-3">
          {!isNextMonth && (
            <button
              onClick={onPrevMonth}
              className="w-6 h-6 flex items-center justify-center text-secondary hover-primary-text hover-primary-background-lightest rounded transition-all cursor-pointer"
            >
              <i className="fas fa-chevron-left text-xs"></i>
            </button>
          )}
          <h3 className="text-sm font-medium primary-text text-center flex-1">
            {month.toLocaleDateString('en-US', { month: 'long' })}
          </h3>
          {!isNextMonth && <div className="w-6"></div>}
          {isNextMonth && (
            <button
              onClick={onNextMonth}
              className="w-6 h-6 flex items-center justify-center text-secondary hover-primary-text hover-primary-background-lightest rounded transition-all cursor-pointer"
            >
              <i className="fas fa-chevron-right text-xs"></i>
            </button>
          )}
        </div>
        <div className="calendar-header-background rounded-lg p-1.5 mb-2">
          <div className="calendar-weekdays-grid grid grid-cols-7 gap-0.5">
            {['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'].map(day => (
              <div key={day} className="text-center text-xs font-medium text-secondary py-0.5">
                {day}
              </div>
            ))}
          </div>
        </div>
        <div className="calendar-days-grid grid grid-cols-7 gap-0.5">
          {days.map((day, index) => {
            if (!day) {
              return <div key={`empty-${year}-${monthIndex}-${index}`} className="h-10"></div>;
            }

            const isPast = isDateInPast(day.date);
            const isSelected = isDateSelected(day.date);
            const isInRange = isDateInRange(day.date);
            const isDeparture = day.date === selectedDates.departure;
            const isReturn = day.date === selectedDates.return;

            return (
              <button
                key={`${year}-${monthIndex}-${day.day}`}
                onClick={() => !isPast && onDateSelect(day.date)}
                disabled={isPast}
                className={`h-10 text-xs transition-all cursor-pointer relative rounded border ${
                  isPast
                    ? 'calendar-disabled-text cursor-not-allowed neutral-background-50 border-transparent'
                    : isSelected
                    ? isDeparture
                      ? 'calendar-selected-background calendar-selected-text border-primary shadow-sm'
                      : 'calendar-return-background calendar-return-text border-secondary shadow-sm'
                    : isInRange
                    ? 'calendar-range-background calendar-range-text border-primary-light hover-primary-background-lighter'
                    : 'hover-primary-background-lightest primary-text hover-primary-border border-transparent'
                }`}
              >
                <div className="flex flex-col items-center justify-center h-full">
                  <span className={`text-xs font-medium leading-none ${isSelected ? 'font-semibold' : ''}`}>
                    {day.day}
                  </span>
                  <span className={`text-[10px] leading-none mt-0.5 ${
                    isPast
                      ? 'calendar-disabled-text'
                      : isSelected
                      ? 'opacity-90'
                      : 'text-tertiary'
                  }`}>
                    ${day.price}
                  </span>
                </div>
                {isDeparture && (
                  <div className="absolute -top-0.5 -right-0.5 w-3 h-3 primary-background rounded-full flex items-center justify-center shadow-sm">
                    <i className="fas fa-plane-departure text-white text-[8px]"></i>
                  </div>
                )}
                {isReturn && (
                  <div className="absolute -top-0.5 -right-0.5 w-3 h-3 secondary-background rounded-full flex items-center justify-center shadow-sm">
                    <i className="fas fa-plane-arrival text-white text-[8px]"></i>
                  </div>
                )}
              </button>
            );
          })}
        </div>
      </div>
    );
  };

  return (
    <div
      ref={calendarRef}
      className="bg-white rounded-xl shadow-xl border border-gray-200 w-full"
    >
      {/* Compact Header */}
      <div className="calendar-header">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <div className="w-6 h-6 primary-background rounded-md flex items-center justify-center">
              <i className="fas fa-calendar-alt text-white text-xs"></i>
            </div>
            <div>
              <h3 className="text-sm font-semibold primary-text">Select Travel Dates - {currentMonth.getFullYear()}</h3>
              <p className="text-secondary text-xs">
                {dateSelectionState === 'departure'
                  ? 'Choose your departure date'
                  : dateSelectionState === 'return'
                  ? 'Choose your return date'
                  : 'Select your travel dates'
                }
              </p>
            </div>
          </div>
          <div className="flex items-center space-x-3">
            {/* Compact Legend */}
            <div className="flex items-center space-x-2 text-xs">
              <div className="flex items-center">
                <div className="w-1.5 h-1.5 primary-background rounded-full mr-1"></div>
                <span className="text-secondary text-xs">Departure</span>
              </div>
              <div className="flex items-center">
                <div className="w-1.5 h-1.5 secondary-background rounded-full mr-1"></div>
                <span className="text-secondary text-xs">Return</span>
              </div>
            </div>
            <button
              className="w-6 h-6 flex items-center justify-center text-secondary hover-primary-text hover-primary-background-lightest rounded transition-all cursor-pointer"
              onClick={onClose}
            >
              <i className="fas fa-times text-xs"></i>
            </button>
          </div>
        </div>
      </div>

      {/* Calendar Content */}
      <div className="calendar-content">
        <div className="calendar-months-container">
          {/* Current Month Calendar */}
          {renderCalendar(currentMonth)}

          {/* Next Month Calendar */}
          {renderCalendar(nextMonth, true)}
        </div>

        {/* Days Input Section - Only for Round Trip */}
        {selectedTripType === 'roundTrip' && (
          <div className="days-input-section">
            <div className="flex items-center justify-between">
              <div>
                <h4 className="text-xs font-medium primary-text leading-tight">Quick Trip Duration</h4>
                <p className="text-xs text-secondary leading-tight">Enter days to auto-calculate return date</p>
              </div>
              <div className="flex items-center space-x-2">
                <label className="text-xs font-medium primary-text">Days:</label>
                <input
                  type="number"
                  min="1"
                  max="365"
                  value={tripDays}
                  onChange={(e) => onDaysInput(e.target.value)}
                  className="w-12 px-2 py-1 border primary-border rounded text-xs focus:ring-1 focus:ring-blue-500/20 focus-primary-border primary-text"
                  placeholder="7"
                />
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default ModifyCalendarModal;
