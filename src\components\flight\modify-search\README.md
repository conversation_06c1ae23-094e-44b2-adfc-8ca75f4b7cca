# Modify Search Components

This directory contains components for modifying flight search parameters with the same functionality as the main FlightSearchForm but with a horizontal, compact display layout as requested.

## Components

### ModifySearchForm
The core form component that provides all the search modification functionality.

**Props:**
- `onSearch: (searchData: any) => void` - Required callback when search is submitted
- `onClose?: () => void` - Optional callback for modal close
- `isModal?: boolean` - Whether to render as modal (default: true)

**Features:**
- Reuses all FlightSearchForm components (AirportSearch, TravelClassPicker, CalendarModal)
- Horizontal layout with color-coded input sections
- Responsive design with mobile-friendly stacking
- Professional styling with hover effects
- Integrates with FlightSearchContext for state management

### ModifySearchButton
A ready-to-use component that includes the trigger button and modal functionality.

**Props:**
- `onSearch?: (searchData: any) => void` - Callback when search is modified
- `className?: string` - Additional CSS classes
- `variant?: 'button' | 'inline'` - Display variant

**Variants:**
- `button` - Simple button that opens modal
- `inline` - Shows current search summary with modify button

## Usage Examples

### Basic Button Trigger
```tsx
import { ModifySearchButton } from './components/flight/modify-search';

<ModifySearchButton 
    onSearch={(searchData) => {
        // Handle search update
        console.log('Updated search:', searchData);
    }}
/>
```

### Inline Search Summary
```tsx
<ModifySearchButton 
    variant="inline"
    onSearch={(searchData) => {
        // Handle search update
    }}
/>
```

### Manual Modal Control
```tsx
import { ModifySearchForm } from './components/flight/modify-search';

const [showModal, setShowModal] = useState(false);

<button onClick={() => setShowModal(true)}>
    Modify Search
</button>

{showModal && (
    <ModifySearchForm
        onSearch={(data) => {
            handleSearch(data);
            setShowModal(false);
        }}
        onClose={() => setShowModal(false)}
        isModal={true}
    />
)}
```

### Inline Form (No Modal)
```tsx
<ModifySearchForm
    onSearch={(searchData) => {
        // Handle search update
    }}
    isModal={false}
/>
```

## Styling

The components use the `ModifySearch.scss` file for styling, which provides:
- Color-coded input sections (blue for departure, green for arrival, purple for dates, orange for travelers)
- Professional hover effects and transitions
- Responsive design with mobile-friendly layouts
- Modal overlay styling
- Dropdown positioning

## Integration

The components integrate seamlessly with:
- **FlightSearchContext** - For state management and search data
- **Airport utilities** - For airport parsing and formatting
- **Existing form components** - Reuses AirportSearch, TravelClassPicker, CalendarModal

## Design Features

✅ **Horizontal Layout** - Inputs displayed in a single row as requested
✅ **Same Functionality** - Uses exact same components as FlightSearchForm
✅ **Modal Support** - Can be used as modal or inline
✅ **Responsive Design** - Adapts to mobile screens
✅ **Professional Styling** - Industrial standard design with proper spacing
✅ **Color Coding** - Different colors for different input types
✅ **Reusable Components** - Modular design for easy integration

## File Structure

```
src/components/flight/modify-search/
├── ModifySearchForm.tsx      # Core form component
├── ModifySearchButton.tsx    # Button trigger component
├── ModifySearchExample.tsx   # Usage examples
├── ModifySearch.scss         # Styling
├── index.ts                  # Exports
└── README.md                 # This file
```

The components are ready to use and follow the same patterns as the existing FlightSearchForm while providing the horizontal layout you requested.
