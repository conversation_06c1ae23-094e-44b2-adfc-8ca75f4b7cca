import { useState, useCallback } from 'react';
import type { FlightDetailsEmitData } from '../models/flight/flight-details.model';
import type { FlightListItem } from '../models/flight/flight-list-models';

interface FlightDetailsPopupState {
  isOpen: boolean;
  flightDetailsBody: FlightDetailsEmitData | null;
  selectedFlight: FlightListItem | null;
}

export const useFlightDetailsPopup = () => {
  const [popupState, setPopupState] = useState<FlightDetailsPopupState>({
    isOpen: false,
    flightDetailsBody: null,
    selectedFlight: null
  });

  const openFlightDetails = useCallback((flight: FlightListItem, searchTUI?: string) => {
    // Transform FlightListItem to FlightDetailsEmitData format
    const flightDetailsBody: FlightDetailsEmitData = {
      TripType: 'ON', // Default to one-way, can be updated based on context
      Trips: [[{
        TUI: searchTUI || flight.tui || '',
        Amount: flight.price.amount,
        Index: flight.index || '',
        OrderID: flight.orderId || 0,
        ChannelCode: null
      }]]
    };

    setPopupState({
      isOpen: true,
      flightDetailsBody,
      selectedFlight: flight
    });
  }, []);

  const openRoundTripFlightDetails = useCallback((
    outboundFlight: FlightListItem, 
    returnFlight: FlightListItem, 
    searchTUI?: string
  ) => {
    const flightDetailsBody: FlightDetailsEmitData = {
      TripType: 'RT',
      Trips: [
        [{
          TUI: searchTUI || outboundFlight.tui || '',
          Amount: outboundFlight.price.amount,
          Index: outboundFlight.index || '',
          OrderID: outboundFlight.orderId || 0,
          ChannelCode: null
        }],
        [{
          TUI: searchTUI || returnFlight.tui || '',
          Amount: returnFlight.price.amount,
          Index: returnFlight.index || '',
          OrderID: returnFlight.orderId || 0,
          ChannelCode: null
        }]
      ]
    };

    setPopupState({
      isOpen: true,
      flightDetailsBody,
      selectedFlight: outboundFlight // Use outbound as primary
    });
  }, []);

  const closeFlightDetails = useCallback(() => {
    setPopupState({
      isOpen: false,
      flightDetailsBody: null,
      selectedFlight: null
    });
  }, []);

  const handleFlightSelection = useCallback((flightData: any) => {
    // Handle flight selection logic here
    console.log('Flight selected from details popup:', flightData);
    // You can add additional logic here like navigation or state updates
  }, []);

  return {
    isOpen: popupState.isOpen,
    flightDetailsBody: popupState.flightDetailsBody,
    selectedFlight: popupState.selectedFlight,
    openFlightDetails,
    openRoundTripFlightDetails,
    closeFlightDetails,
    handleFlightSelection
  };
};
