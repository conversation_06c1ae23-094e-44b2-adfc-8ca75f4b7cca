/**
 * Enhanced Index Helper for TripJack API Integration
 * Handles TUI generation, fare ID validation, and Index format compliance
 */

export class EnhancedIndexHelper {
  /**
   * ✅ RANDOM TUI GENERATION: Generate random TUI to avoid booking conflicts
   * Format: flight_search_{from}_{to}_{date}_{adults}_{children}_{infants}_{cabin}_{randomSuffix}
   */
  static generateRandomTUI(
    from: string,
    to: string,
    date: string,
    adults: number = 1,
    children: number = 0,
    infants: number = 0,
    cabin: string = 'E'
  ): string {
    // Generate random 8-character suffix to prevent conflicts
    const randomSuffix = Math.random().toString(36).substring(2, 10).toUpperCase();
    const timestamp = Date.now().toString().slice(-6); // Last 6 digits of timestamp
    
    // Format date as YYYY-MM-DD
    const formattedDate = date.replace(/\//g, '-');
    
    return `flight_search_${from}_${to}_${formattedDate}_${adults}_${children}_${infants}_${cabin}_${randomSuffix}_${timestamp}`;
  }

  /**
   * ✅ FARE VALIDATION: Implement 10% tolerance threshold for fare changes
   */
  static validateFareChange(originalFare: number, newFare: number): {
    isValid: boolean;
    changePercentage: number;
    message: string;
  } {
    const changePercentage = Math.abs((newFare - originalFare) / originalFare) * 100;
    const tolerance = 10; // 10% tolerance threshold
    
    const isValid = changePercentage <= tolerance;
    
    return {
      isValid,
      changePercentage: Math.round(changePercentage * 100) / 100,
      message: isValid 
        ? `Fare change within acceptable range (${changePercentage.toFixed(2)}%)` 
        : `Fare change exceeds 10% tolerance (${changePercentage.toFixed(2)}%). Please refresh and select again.`
    };
  }

  /**
   * ✅ TRIPJACK FARE ID VALIDATION: Validate fare ID format against TripJack Postman collection
   */
  static validateTripJackFareId(fareId: string): {
    isValid: boolean;
    isTripJackFormat: boolean;
    errors: string[];
    warnings: string[];
  } {
    const errors: string[] = [];
    const warnings: string[] = [];
    
    if (!fareId || fareId.trim() === '') {
      errors.push('Fare ID is empty');
      return { isValid: false, isTripJackFormat: false, errors, warnings };
    }

    // TripJack fare ID pattern from Postman collection: "10-15-2-9349944529_0DELBOMSG252~1112424609503896"
    const tripjackPattern = /^\d+-\d+-\d+-\d+_\d+[A-Z]{6}[A-Z0-9]+~\d+$/;
    const isTripJackFormat = tripjackPattern.test(fareId);
    
    if (!isTripJackFormat) {
      warnings.push('Fare ID does not match TripJack Postman collection format');
      warnings.push('Expected format: {num}-{num}-{num}-{num}_{num}{6_letters}{alphanumeric}~{num}');
    }

    // Basic validation
    if (fareId.length < 10) {
      errors.push('Fare ID too short');
    }

    if (fareId.length > 100) {
      errors.push('Fare ID too long');
    }

    return {
      isValid: errors.length === 0,
      isTripJackFormat,
      errors,
      warnings
    };
  }

  /**
   * ✅ EXTRACT FARE ID: Extract fare ID from enhanced Index format
   */
  static extractFareId(enhancedIndex: string): string {
    if (!enhancedIndex) return '';
    
    // Enhanced Index format: {fare_id}|{idx}|{timestamp}|{random_hex}|{tui}|TJ
    const parts = enhancedIndex.split('|');
    return parts[0] || '';
  }

  /**
   * ✅ CLEAN FLIGHT INDEX: Remove TripJack suffix for API calls
   */
  static cleanFlightIndex(enhancedIndex: string): string {
    if (!enhancedIndex) return '';
    
    // Remove |0|TJ suffix that's added for internal reference
    return enhancedIndex.replace(/\|\d+\|TJ$/, '');
  }

  /**
   * ✅ VALIDATE DATE SEQUENCE: Ensure travel dates are in ascending order (prevent error 1003)
   */
  static validateDateSequence(routes: Array<{ travelDate: string }>): {
    isValid: boolean;
    error?: string;
  } {
    if (routes.length <= 1) {
      return { isValid: true };
    }

    for (let i = 1; i < routes.length; i++) {
      const prevDate = new Date(routes[i - 1].travelDate);
      const currentDate = new Date(routes[i].travelDate);
      
      if (currentDate < prevDate) {
        return {
          isValid: false,
          error: `Error 1003 Prevention: Travel dates must be in ascending order. Route ${i + 1} date (${routes[i].travelDate}) is before route ${i} date (${routes[i - 1].travelDate})`
        };
      }
    }

    return { isValid: true };
  }

  /**
   * ✅ GENERATE BOOKING REFERENCE: Generate unique booking reference
   */
  static generateBookingReference(): string {
    const timestamp = Date.now();
    const random = Math.random().toString(36).substring(2, 8).toUpperCase();
    return `FT${timestamp}${random}`;
  }

  /**
   * ✅ VALIDATE ROUND-TRIP ROUTES: Ensure proper round-trip route construction
   */
  static validateRoundTripRoutes(onwardRoute: string, returnRoute: string): {
    isValid: boolean;
    error?: string;
  } {
    const [onwardFrom, onwardTo] = onwardRoute.split('-');
    const [returnFrom, returnTo] = returnRoute.split('-');
    
    if (onwardTo !== returnFrom || onwardFrom !== returnTo) {
      return {
        isValid: false,
        error: `Invalid round-trip route: ${onwardRoute} and ${returnRoute}. Return route should be ${onwardTo}-${onwardFrom}`
      };
    }

    return { isValid: true };
  }

  /**
   * ✅ FORMAT TRIPJACK REQUEST: Format request according to TripJack API specifications
   */
  static formatTripJackSearchRequest(searchParams: {
    routes: Array<{ from: string; to: string; date: string }>;
    passengers: { adults: number; children: number; infants: number };
    cabinClass: string;
    isDirectFlight?: boolean;
    isConnectingFlight?: boolean;
  }) {
    // Validate date sequence
    const dateValidation = this.validateDateSequence(searchParams.routes.map(r => ({ travelDate: r.date })));
    if (!dateValidation.isValid) {
      throw new Error(dateValidation.error);
    }

    return {
      searchQuery: {
        cabinClass: searchParams.cabinClass,
        paxInfo: {
          ADULT: searchParams.passengers.adults.toString(),
          CHILD: searchParams.passengers.children.toString(),
          INFANT: searchParams.passengers.infants.toString()
        },
        routeInfos: searchParams.routes.map(route => ({
          fromCityOrAirport: { code: route.from },
          toCityOrAirport: { code: route.to },
          travelDate: route.date
        })),
        searchModifiers: {
          isDirectFlight: searchParams.isDirectFlight || false,
          isConnectingFlight: searchParams.isConnectingFlight || false
        }
      }
    };
  }

  /**
   * ✅ VALIDATE SPECIAL RETURN FARE: Check if fare requires special handling
   */
  static isSpecialReturnFare(fareIdentifier: string, sri?: string, msri?: string[]): boolean {
    return fareIdentifier === 'SPECIAL_RETURN' || (sri && sri.length > 0) || (msri && msri.length > 0);
  }
}

export default EnhancedIndexHelper;
