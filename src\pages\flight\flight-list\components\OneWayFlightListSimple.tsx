import React, { useEffect } from 'react';

interface OneWayFlightListProps {
  apiData: any;
  apiMetadata: any;
  isLoading: boolean;
}

const OneWayFlightList: React.FC<OneWayFlightListProps> = ({
  apiData,
  apiMetadata
}) => {
  // Process API data when it changes
  useEffect(() => {
    if (apiData) {
      console.log('🔄 OneWayFlightList: Processing API data:', apiData);
      // For now, just log the data - transformation will be added later
    }
  }, [apiData, apiMetadata]);

  // Loading is now handled by parent component with shimmer

  return (
    <div className="space-y-8">
      <div className="bg-white rounded-lg border border-gray-200 p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">One-Way Flights</h3>
        <p className="text-gray-600">One-way flight processing will be implemented here.</p>
        
        {/* Show raw API data for debugging */}
        {apiData && (
          <div className="mt-4 p-3 bg-gray-50 rounded text-xs">
            <p><strong>API Data Available:</strong> {apiData.Trips?.length || 0} trips</p>
            <p><strong>Completed:</strong> {apiData.Completed ? 'Yes' : 'No'}</p>
            {apiData.Trips && apiData.Trips[0] && (
              <div className="mt-2">
                <p><strong>First Trip Flights:</strong> {apiData.Trips[0].Journey?.length || 0}</p>
                {apiData.Trips[0].Journey && apiData.Trips[0].Journey[0] && (
                  <div className="mt-1 text-xs text-gray-600">
                    <p>Sample Flight: {apiData.Trips[0].Journey[0].AirlineName} - {apiData.Trips[0].Journey[0].From} to {apiData.Trips[0].Journey[0].To}</p>
                    <p>Price: {apiData.Trips[0].Journey[0].TotalFare}</p>
                  </div>
                )}
              </div>
            )}
          </div>
        )}
      </div>

      {/* Performance Metrics */}
      {apiMetadata && (
        <div className="mt-4 p-3 bg-gray-100 rounded-lg text-xs text-gray-600">
          <div>Response Time: {apiMetadata.responseTimeMS || 0}ms</div>
          <div>Cache Hit: {apiMetadata.cacheHit ? 'Yes' : 'No'}</div>
          <div>Data Source: {apiMetadata.dataSource || 'Unknown'}</div>
          <div>Used Cache: {apiMetadata.usedCachedResults ? 'Yes' : 'No'}</div>
          <div>Required Polling: {apiMetadata.requiresPolling ? 'Yes' : 'No'}</div>
        </div>
      )}
    </div>
  );
};

export default OneWayFlightList;
