import React from 'react';
import type { AirlineIconProps } from './types';

const AirlineIcon: React.FC<AirlineIconProps> = ({ code, size = 'md' }) => {
  const sizeClasses = {
    sm: 'w-6 h-6 text-xs',
    md: 'w-8 h-8 text-sm',
    lg: 'w-10 h-10 text-base'
  };

  return (
    <div className={`${sizeClasses[size]} rounded-full bg-blue-100 flex items-center justify-center text-blue-800 font-bold`}>
      {code}
    </div>
  );
};

export default AirlineIcon;
