import React, { useState, useEffect, useRef } from 'react';
import type { Airport } from '../../../../../models/flight/common-flight.model';

interface AirportSearchProps {
  searchTerm: string;
  airports: Airport[];
  showDropdown: boolean;
  onAirportSelect: (airport: Airport) => void;
  icon: string;
  isLoading?: boolean;
  disabledAirportCode?: string; // Airport code to disable (opposite selection)
}


const AirportSearch: React.FC<AirportSearchProps> = ({
  searchTerm,
  airports,
  showDropdown,
  onAirportSelect,
  icon,
  isLoading = false,
  disabledAirportCode,
}) => {
  const [highlightedIndex, setHighlightedIndex] = useState(-1);
  const listContainerRef = useRef<HTMLDivElement>(null);

  // Helper functions for keyboard navigation with disabled airports
  const isAirportDisabled = (airport: Airport) => {
    return disabledAirportCode && airport.code === disabledAirportCode;
  };

  const findNextEnabledIndex = (currentIndex: number, direction: 'down' | 'up') => {
    if (!airports || airports.length === 0) return -1;

    const enabledAirports = airports.filter(airport => !isAirportDisabled(airport));
    if (enabledAirports.length === 0) return -1;

    // If starting from -1 (no selection), find first/last enabled airport
    if (currentIndex === -1) {
      if (direction === 'down') {
        // Find first enabled airport
        for (let i = 0; i < airports.length; i++) {
          if (!isAirportDisabled(airports[i])) {
            return i;
          }
        }
      } else {
        // Find last enabled airport
        for (let i = airports.length - 1; i >= 0; i--) {
          if (!isAirportDisabled(airports[i])) {
            return i;
          }
        }
      }
      return -1;
    }

    let nextIndex = currentIndex;
    let attempts = 0;
    const maxAttempts = airports.length;

    do {
      if (direction === 'down') {
        nextIndex = (nextIndex + 1) % airports.length;
      } else {
        nextIndex = (nextIndex - 1 + airports.length) % airports.length;
      }
      attempts++;
    } while (isAirportDisabled(airports[nextIndex]) && attempts < maxAttempts);

    return attempts < maxAttempts ? nextIndex : -1;
  };

  // Reset highlighted index when search term changes or dropdown is shown/hidden
  useEffect(() => {
    setHighlightedIndex(-1);
  }, [searchTerm, showDropdown]);

  // Keyboard navigation
  useEffect(() => {
    if (!showDropdown) return;

    const handleKeyDown = (e: KeyboardEvent) => {
      if (isLoading || !airports || airports.length === 0) return;

      switch (e.key) {
        case 'ArrowDown':
          e.preventDefault();
          const nextDownIndex = findNextEnabledIndex(highlightedIndex, 'down');
          if (nextDownIndex !== -1) {
            setHighlightedIndex(nextDownIndex);
          }
          break;
        case 'ArrowUp':
          e.preventDefault();
          const nextUpIndex = findNextEnabledIndex(highlightedIndex, 'up');
          if (nextUpIndex !== -1) {
            setHighlightedIndex(nextUpIndex);
          }
          break;
        case 'Enter':
          e.preventDefault();
          if (highlightedIndex >= 0 && !isAirportDisabled(airports[highlightedIndex])) {
            onAirportSelect(airports[highlightedIndex]);
          }
          break;
        default:
          break;
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => {
      document.removeEventListener('keydown', handleKeyDown);
    };
  }, [showDropdown, isLoading, airports, highlightedIndex, onAirportSelect, disabledAirportCode]);

  // Scroll to highlighted item
  useEffect(() => {
    if (highlightedIndex >= 0 && listContainerRef.current) {
      const item = listContainerRef.current.querySelector(`[data-index="${highlightedIndex}"]`);
      if (item) {
        item.scrollIntoView({ behavior: 'smooth', block: 'nearest' });
      }
    }
  }, [highlightedIndex]);


  if (!showDropdown) return null;

  const ShimmerItem = () => (
    <div className="flex items-start px-3 py-2.5 animate-pulse">
      <div className="mr-3 mt-0.5">
        <div className="w-4 h-4 bg-gray-200 rounded-full"></div>
      </div>
      <div className="flex-1 min-w-0">
        <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
        <div className="h-3 bg-gray-200 rounded w-full"></div>
      </div>
    </div>
  );

  return (
      <div className="p-2 w-100" ref={listContainerRef}>
        <div className="text-xs font-medium text-gray-500 px-3 py-2">
          {isLoading ? 'Searching...' : (searchTerm ? 'Search Results' : 'Popular Airports')}
        </div>
        {isLoading ? (
          <div>
            {[...Array(4)].map((_, i) => <ShimmerItem key={i} />)}
          </div>
        ) : airports && airports.length > 0 ? (
          airports.map((airport, index) => {
            const isDisabled = disabledAirportCode && airport.code === disabledAirportCode;
            const isHighlighted = index === highlightedIndex && !isDisabled;
            return (
              <button
                key={index}
                data-index={index}
                className={`!rounded-button w-full flex items-start px-3 py-2.5 text-left transition-colors ${
                  isDisabled
                    ? 'cursor-not-allowed bg-gray-100 opacity-50'
                    : isHighlighted
                      ? 'bg-gray-100 cursor-pointer'
                      : 'hover:bg-gray-50 cursor-pointer'
                }`}
                onClick={() => !isDisabled && onAirportSelect(airport)}
                disabled={isDisabled}
                title={isDisabled ? 'This airport is already selected' : ''}
              >
                <div className="mr-3 mt-0.5">
                  <i className={`${icon === 'fas fa-map-marker-alt' ? 'fas fa-plane-departure' : 'fas fa-plane-arrival'} ${
                    isDisabled ? 'text-gray-300' : 'text-gray-400'
                  }`}></i>
                </div>
                <div className="flex-1 min-w-0">
                  <div className={`font-medium truncate ${
                    isDisabled ? 'text-gray-400' : 'text-gray-800'
                  }`}>
                    {airport.city_name} ({airport.code})
                    {isDisabled && <span className="ml-2 text-xs">(Already selected)</span>}
                  </div>
                  <div className={`text-xs truncate ${
                    isDisabled ? 'text-gray-300' : 'text-gray-500'
                  }`}>
                    {airport.name}, {airport.country}
                  </div>
                </div>
              </button>
            );
          })
        ) : (
          <div className="px-3 py-4 text-center text-gray-500">No airports found. Try a different search.</div>
        )}
      </div>
  );
};

export default AirportSearch;
