/**
 * Flight Search Payload Tests
 * 
 * Tests to verify the exact implementation of flight search payload structure
 */

import { scheduleBodySet } from '../utils/flightPayloadUtils';
import type { FormSearch, ScheduleBody } from '../models/flight/flight-search.model';

describe('Flight Search Payload Implementation', () => {
  
  test('scheduleBodySet creates correct one-way payload structure', () => {
    const formValue: FormSearch = {
      FareType: 'ON',
      SecType: 'Flight',
      travellers: {
        adult: 2,
        child: 1,
        infant: 0
      },
      cabin: 'E',
      trips: [
        {
          from: {
            city: 'New York',
            airport: 'John F. Kennedy International Airport',
            iata: 'JFK',
            country: 'United States',
            airportOpen: false
          },
          to: {
            city: 'Los Angeles',
            airport: 'Los Angeles International Airport',
            iata: 'LAX',
            country: 'United States',
            airportOpen: false
          },
          depart: new Date('2024-12-25'),
          return: undefined
        }
      ]
    };

    const result = scheduleBodySet(formValue, 'ON');

    // Verify exact structure
    expect(result.SecType).toBe('Flight');
    expect(result.FareType).toBe('ON');
    expect(result.ADT).toBe(2);
    expect(result.CHD).toBe(1);
    expect(result.INF).toBe(0);
    expect(result.Cabin).toBe('E');
    expect(result.Source).toBe('CF');
    expect(result.Mode).toBe('AS');
    expect(result.ClientID).toBe('');
    expect(result.IsMultipleCarrier).toBe(false);
    expect(result.IsRefundable).toBe(false);
    expect(result.preferedAirlines).toBe(null);
    expect(result.TUI).toBe('');
    expect(result.YTH).toBe(0);

    // Verify trips structure
    expect(result.Trips).toHaveLength(1);
    expect(result.Trips[0].From).toBe('JFK');
    expect(result.Trips[0].FromArptName).toBe('John F. Kennedy International Airport');
    expect(result.Trips[0].FromCity).toBe('New York');
    expect(result.Trips[0].OnwardDate).toBe('2024-12-25');
    expect(result.Trips[0].OrderId).toBe(1);
    expect(result.Trips[0].ReturnDate).toBe(null);
    expect(result.Trips[0].To).toBe('LAX');
    expect(result.Trips[0].ToArptName).toBe('Los Angeles International Airport');
    expect(result.Trips[0].ToCity).toBe('Los Angeles');
    expect(result.Trips[0].TUI).toBe('');

    // Verify parameters structure
    expect(result.Parameters.Airlines).toBe('');
    expect(result.Parameters.GroupType).toBe('');
    expect(result.Parameters.IsDirect).toBe(false);
    expect(result.Parameters.IsNearbyAirport).toBe(true);
    expect(result.Parameters.Refundable).toBe('');
  });

  test('scheduleBodySet creates correct round-trip payload structure', () => {
    const formValue: FormSearch = {
      FareType: 'RT',
      SecType: 'Flight',
      travellers: {
        adult: 1,
        child: 0,
        infant: 0
      },
      cabin: 'B',
      trips: [
        {
          from: {
            city: 'London',
            airport: 'Heathrow Airport',
            iata: 'LHR',
            country: 'United Kingdom',
            airportOpen: false
          },
          to: {
            city: 'Dubai',
            airport: 'Dubai International Airport',
            iata: 'DXB',
            country: 'United Arab Emirates',
            airportOpen: false
          },
          depart: new Date('2024-12-20'),
          return: new Date('2024-12-30')
        }
      ]
    };

    const result = scheduleBodySet(formValue, 'RT');

    expect(result.FareType).toBe('RT');
    expect(result.ADT).toBe(1);
    expect(result.CHD).toBe(0);
    expect(result.INF).toBe(0);
    expect(result.Cabin).toBe('B');

    // Verify round-trip specific fields
    expect(result.Trips[0].OnwardDate).toBe('2024-12-20');
    expect(result.Trips[0].ReturnDate).toBe('2024-12-30');
  });

  test('scheduleBodySet creates correct multi-city payload structure', () => {
    const formValue: FormSearch = {
      FareType: 'MC',
      SecType: 'Flight',
      travellers: {
        adult: 2,
        child: 0,
        infant: 1
      },
      cabin: 'PE',
      trips: [
        {
          from: {
            city: 'New York',
            airport: 'John F. Kennedy International Airport',
            iata: 'JFK',
            country: 'United States',
            airportOpen: false
          },
          to: {
            city: 'London',
            airport: 'Heathrow Airport',
            iata: 'LHR',
            country: 'United Kingdom',
            airportOpen: false
          },
          depart: new Date('2024-12-15'),
          return: undefined
        },
        {
          from: {
            city: 'London',
            airport: 'Heathrow Airport',
            iata: 'LHR',
            country: 'United Kingdom',
            airportOpen: false
          },
          to: {
            city: 'Paris',
            airport: 'Charles de Gaulle Airport',
            iata: 'CDG',
            country: 'France',
            airportOpen: false
          },
          depart: new Date('2024-12-20'),
          return: undefined
        }
      ]
    };

    const result = scheduleBodySet(formValue, 'MC');

    expect(result.FareType).toBe('MC');
    expect(result.ADT).toBe(2);
    expect(result.CHD).toBe(0);
    expect(result.INF).toBe(1);
    expect(result.Cabin).toBe('PE');

    // Verify multi-city trips
    expect(result.Trips).toHaveLength(2);
    
    // First trip
    expect(result.Trips[0].From).toBe('JFK');
    expect(result.Trips[0].To).toBe('LHR');
    expect(result.Trips[0].OnwardDate).toBe('2024-12-15');
    expect(result.Trips[0].OrderId).toBe(1);
    expect(result.Trips[0].ReturnDate).toBe(null);

    // Second trip
    expect(result.Trips[1].From).toBe('LHR');
    expect(result.Trips[1].To).toBe('CDG');
    expect(result.Trips[1].OnwardDate).toBe('2024-12-20');
    expect(result.Trips[1].OrderId).toBe(2);
    expect(result.Trips[1].ReturnDate).toBe(null);
  });

  test('scheduleBodySet handles date formatting correctly', () => {
    const formValue: FormSearch = {
      FareType: 'ON',
      SecType: 'Flight',
      travellers: { adult: 1, child: 0, infant: 0 },
      cabin: 'E',
      trips: [
        {
          from: { city: 'NYC', airport: 'JFK', iata: 'JFK', country: 'US', airportOpen: false },
          to: { city: 'LA', airport: 'LAX', iata: 'LAX', country: 'US', airportOpen: false },
          depart: new Date('2024-12-25T10:30:00.000Z'),
          return: undefined
        }
      ]
    };

    const result = scheduleBodySet(formValue, 'ON');

    // Verify date is formatted as yyyy-MM-dd
    expect(result.Trips[0].OnwardDate).toBe('2024-12-25');
  });

});
