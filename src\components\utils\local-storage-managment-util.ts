export function pushToLocalStorageList<T>(key: string,newItem: T,maxItems?: number): void {
    
  const raw = localStorage.getItem(key);
  let items: T[] = raw ? JSON.parse(raw) : [];

  // Remove duplicates (exact match)
  items = items.filter(item => JSON.stringify(item) !== JSON.stringify(newItem));

  // Add new item to the front
  items.unshift(newItem);

  // Trim list if maxItems is provided
  if (typeof maxItems === 'number') {
    items = items.slice(0, maxItems);
  }

  localStorage.setItem(key, JSON.stringify(items));
}
