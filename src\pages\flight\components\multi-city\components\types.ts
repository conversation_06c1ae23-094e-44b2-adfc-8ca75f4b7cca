// Multi-City Flight Components Types and Interfaces

export interface MultiCityFlight {
  id: string;
  airline: {
    name: string;
    code: string;
  };
  flightNumber: string;
  departure: {
    city: string;
    code: string;
    time: string;
    date: string;
    terminal: string;
  };
  arrival: {
    city: string;
    code: string;
    time: string;
    date: string;
    terminal: string;
  };
  duration: string;
  stops: number;
  stopDetails: Array<{
    airport: string;
    duration: string;
  }>;
  aircraft: string;
  fare: {
    type: string;
    price: number;
    currency: string;
    refundable: boolean;
    baggage: string;
  };
}

export interface MultiCitySegment {
  id: string;
  origin: {
    city: string;
    code: string;
  };
  destination: {
    city: string;
    code: string;
  };
  date: string;
  flights: MultiCityFlight[];
}

export interface SegmentFilters {
  airlines: string[];
  priceRange: {
    min: number;
    max: number;
  };
  stops: number[];
  departureTime: {
    from: string;
    to: string;
  };
  duration: {
    max: number;
  };
  fareTypes: string[];
}

// Component Props Interfaces
export interface SegmentHeaderProps {
  segment: MultiCitySegment;
  selectedFlight: MultiCityFlight | null;
  isCollapsed: boolean;
  onToggleCollapse: () => void;
}

export interface MultiCityFlightCardProps {
  flight: MultiCityFlight;
  isSelected: boolean;
  onSelect: (flight: MultiCityFlight) => void;
  onToggleDetails: (flightId: string) => void;
  showDetails: boolean;
}

export interface SegmentFiltersProps {
  segment: MultiCitySegment;
  filters: SegmentFilters;
  onFiltersChange: (filters: SegmentFilters) => void;
  onClose: () => void;
  onApply: () => void;
  onReset: () => void;
}

export interface TripSummaryProps {
  segments: MultiCitySegment[];
  selectedFlights: Record<string, MultiCityFlight>;
  totalPrice: number;
  onBookItinerary: () => void;
  onEmailItinerary: () => void;
}

export interface AirlineIconProps {
  code: string;
  size?: 'sm' | 'md' | 'lg';
}

export interface FlightDurationChartProps {
  duration: string;
  maxDuration?: number;
}
