import React from 'react';

interface FlightDetailsShimmerProps {
  tripType: 'ON' | 'RT';
}

const FlightDetailsShimmer: React.FC<FlightDetailsShimmerProps> = ({ tripType }) => {
  return (
    <div className="space-y-6">
      {/* Header Shimmer */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <div className="w-12 h-12 bg-gray-200 rounded-full animate-pulse"></div>
          <div className="space-y-2">
            <div className="w-32 h-4 bg-gray-200 rounded animate-pulse"></div>
            <div className="w-24 h-3 bg-gray-200 rounded animate-pulse"></div>
          </div>
        </div>
        <div className="w-8 h-8 bg-gray-200 rounded animate-pulse"></div>
      </div>

      {/* Flight Segments Shimmer */}
      <div className="space-y-4">
        <div className="w-32 h-5 bg-gray-200 rounded animate-pulse"></div>
        
        {/* Outbound Flight Shimmer */}
        <FlightSegmentShimmer />
        
        {/* Return Flight Shimmer (only for round trip) */}
        {tripType === 'RT' && (
          <>
            <div className="w-28 h-4 bg-gray-200 rounded animate-pulse mt-6"></div>
            <FlightSegmentShimmer />
          </>
        )}
      </div>

      {/* Fare Summary Shimmer */}
      <div className="space-y-4">
        <div className="w-28 h-5 bg-gray-200 rounded animate-pulse"></div>
        <FareSummaryShimmer />
      </div>

      {/* Baggage Information Shimmer */}
      <div className="space-y-4">
        <div className="w-40 h-5 bg-gray-200 rounded animate-pulse"></div>
        <BaggageInfoShimmer />
      </div>

      {/* Fare Rules Shimmer */}
      <div className="space-y-4">
        <div className="w-24 h-5 bg-gray-200 rounded animate-pulse"></div>
        <FareRulesShimmer />
      </div>

      {/* Action Buttons Shimmer */}
      <div className="flex justify-end space-x-4 pt-4">
        <div className="w-20 h-10 bg-gray-200 rounded animate-pulse"></div>
        <div className="w-32 h-10 bg-gray-200 rounded animate-pulse"></div>
      </div>
    </div>
  );
};

const FlightSegmentShimmer: React.FC = () => {
  return (
    <div className="border border-gray-200 rounded-lg p-4 space-y-4">
      {/* Airline and Flight Number */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-3">
          <div className="w-8 h-8 bg-gray-200 rounded animate-pulse"></div>
          <div className="space-y-1">
            <div className="w-24 h-4 bg-gray-200 rounded animate-pulse"></div>
            <div className="w-16 h-3 bg-gray-200 rounded animate-pulse"></div>
          </div>
        </div>
        <div className="w-20 h-4 bg-gray-200 rounded animate-pulse"></div>
      </div>

      {/* Flight Route */}
      <div className="flex items-center justify-between">
        <div className="space-y-2">
          <div className="w-16 h-6 bg-gray-200 rounded animate-pulse"></div>
          <div className="w-24 h-3 bg-gray-200 rounded animate-pulse"></div>
          <div className="w-20 h-3 bg-gray-200 rounded animate-pulse"></div>
        </div>
        
        <div className="flex-1 flex items-center justify-center space-x-2">
          <div className="w-16 h-3 bg-gray-200 rounded animate-pulse"></div>
          <div className="w-8 h-0.5 bg-gray-200 animate-pulse"></div>
          <div className="w-6 h-6 bg-gray-200 rounded-full animate-pulse"></div>
          <div className="w-8 h-0.5 bg-gray-200 animate-pulse"></div>
          <div className="w-16 h-3 bg-gray-200 rounded animate-pulse"></div>
        </div>

        <div className="space-y-2">
          <div className="w-16 h-6 bg-gray-200 rounded animate-pulse"></div>
          <div className="w-24 h-3 bg-gray-200 rounded animate-pulse"></div>
          <div className="w-20 h-3 bg-gray-200 rounded animate-pulse"></div>
        </div>
      </div>

      {/* Additional Details */}
      <div className="grid grid-cols-2 md:grid-cols-4 gap-4 pt-2 border-t border-gray-100">
        <div className="space-y-1">
          <div className="w-12 h-3 bg-gray-200 rounded animate-pulse"></div>
          <div className="w-16 h-4 bg-gray-200 rounded animate-pulse"></div>
        </div>
        <div className="space-y-1">
          <div className="w-10 h-3 bg-gray-200 rounded animate-pulse"></div>
          <div className="w-20 h-4 bg-gray-200 rounded animate-pulse"></div>
        </div>
        <div className="space-y-1">
          <div className="w-14 h-3 bg-gray-200 rounded animate-pulse"></div>
          <div className="w-18 h-4 bg-gray-200 rounded animate-pulse"></div>
        </div>
        <div className="space-y-1">
          <div className="w-16 h-3 bg-gray-200 rounded animate-pulse"></div>
          <div className="w-12 h-4 bg-gray-200 rounded animate-pulse"></div>
        </div>
      </div>
    </div>
  );
};

const FareSummaryShimmer: React.FC = () => {
  return (
    <div className="border border-gray-200 rounded-lg p-4 space-y-3">
      {/* Fare Breakdown */}
      <div className="space-y-2">
        {[1, 2, 3, 4].map((item) => (
          <div key={item} className="flex justify-between items-center">
            <div className="w-24 h-4 bg-gray-200 rounded animate-pulse"></div>
            <div className="w-16 h-4 bg-gray-200 rounded animate-pulse"></div>
          </div>
        ))}
      </div>
      
      {/* Total */}
      <div className="border-t border-gray-200 pt-2">
        <div className="flex justify-between items-center">
          <div className="w-20 h-5 bg-gray-200 rounded animate-pulse"></div>
          <div className="w-24 h-5 bg-gray-200 rounded animate-pulse"></div>
        </div>
      </div>
    </div>
  );
};

const BaggageInfoShimmer: React.FC = () => {
  return (
    <div className="border border-gray-200 rounded-lg p-4 space-y-4">
      {/* Included Baggage */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div className="space-y-2">
          <div className="w-20 h-4 bg-gray-200 rounded animate-pulse"></div>
          <div className="w-16 h-5 bg-gray-200 rounded animate-pulse"></div>
        </div>
        <div className="space-y-2">
          <div className="w-24 h-4 bg-gray-200 rounded animate-pulse"></div>
          <div className="w-16 h-5 bg-gray-200 rounded animate-pulse"></div>
        </div>
      </div>

      {/* Additional Services */}
      <div className="space-y-2">
        <div className="w-32 h-4 bg-gray-200 rounded animate-pulse"></div>
        <div className="space-y-2">
          {[1, 2].map((item) => (
            <div key={item} className="flex justify-between items-center">
              <div className="w-40 h-4 bg-gray-200 rounded animate-pulse"></div>
              <div className="w-16 h-4 bg-gray-200 rounded animate-pulse"></div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

const FareRulesShimmer: React.FC = () => {
  return (
    <div className="border border-gray-200 rounded-lg p-4 space-y-4">
      {[1, 2, 3].map((item) => (
        <div key={item} className="space-y-2">
          <div className="w-32 h-4 bg-gray-200 rounded animate-pulse"></div>
          <div className="space-y-1">
            <div className="w-full h-3 bg-gray-200 rounded animate-pulse"></div>
            <div className="w-3/4 h-3 bg-gray-200 rounded animate-pulse"></div>
          </div>
          {item < 3 && <div className="border-b border-gray-100"></div>}
        </div>
      ))}
    </div>
  );
};

export default FlightDetailsShimmer;
