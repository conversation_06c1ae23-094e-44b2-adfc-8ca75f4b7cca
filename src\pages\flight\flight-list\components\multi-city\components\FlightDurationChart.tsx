import React from 'react';
import type { FlightDurationChartProps } from './types';

const FlightDurationChart: React.FC<FlightDurationChartProps> = ({ 
  duration, 
  maxDuration = 600 // 10 hours in minutes as default max
}) => {
  const parseDuration = (duration: string): number => {
    const durationHours = parseFloat(duration.split("h")[0]);
    const durationMinutes = duration.includes("m")
      ? parseFloat(duration.split("h")[1].split("m")[0].trim())
      : 0;
    return durationHours * 60 + durationMinutes;
  };

  const totalMinutes = parseDuration(duration);
  const widthPercentage = Math.min(100, (totalMinutes / maxDuration) * 100);

  return (
    <div className="w-full h-1 bg-gray-200 rounded-full">
      <div
        className="h-1 bg-teal-500 rounded-full transition-all duration-300"
        style={{ width: `${widthPercentage}%` }}
      />
    </div>
  );
};

export default FlightDurationChart;
