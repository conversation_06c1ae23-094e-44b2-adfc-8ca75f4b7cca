import React, { useState, useRef, useEffect } from 'react';

// Component imports
import AirportSearch from './components/airport-search/AirportSearch';
import TravelClassPicker from './components/travel-class/TravelClassPicker';
import CalendarModal from './components/multi-date-picker/CalendarModal';
import AdvancedSearchContent from './components/advanced-search/AdvancedSearchContent';
import TripTypeSelector from './components/trip-type-selecter/TripTypeSelector';
import RecentSearches from './components/RecentSearches';
import SearchButton from './components/SearchButton';

// Context and utilities
import { useFlightSearch } from '../../../contexts/FlightSearchContext';
import { parseAirportString, formatAirportDisplay } from '../../../utils/airportUtils';

// Legacy interface for backward compatibility
export interface FlightSearchData {
    fromAirport: string;
    toAirport: string;
    selectedDates: {
        departure: string;
        return: string;
    };
    selectedTripType: 'roundTrip' | 'oneWay' | 'multiCity';
    passengers: {
        adults: number;
        children: number;
        infants: number;
    };
    selectedClass: 'economy' | 'premiumEconomy' | 'business' | 'first';
}

export interface FlightSearchFormProps {
    onSearch: (searchData: FlightSearchData) => void; // Required - parent handles search logic
    compact?: boolean; // Optional - for compact modal display
}

const FlightSearchForm: React.FC<FlightSearchFormProps> = ({ onSearch, compact = false }) => {
    // Use flight search context
    const {
        currentSearch,
        updateSearch,
        isLoading
    } = useFlightSearch();

    const tripTypes: Array<{ id: 'roundTrip' | 'oneWay' | 'multiCity'; label: string }> = [
        { id: 'roundTrip', label: 'Round Trip' },
        { id: 'oneWay', label: 'One Way' },
        { id: 'multiCity', label: 'Multi-City' }
    ];

    const classOptions = [
        { id: 'economy', label: 'Economy' },
        { id: 'premiumEconomy', label: 'Premium Economy' },
        { id: 'business', label: 'Business' },
        { id: 'first', label: 'First Class' }
    ];

    const popularAirports = [
        { code: 'JFK', city: 'New York', name: 'John F. Kennedy International Airport', country: 'United States' },
        { code: 'LHR', city: 'London', name: 'Heathrow Airport', country: 'United Kingdom' },
        { code: 'LAX', city: 'Los Angeles', name: 'Los Angeles International Airport', country: 'United States' },
        { code: 'CDG', city: 'Paris', name: 'Charles de Gaulle Airport', country: 'France' },
        { code: 'DXB', city: 'Dubai', name: 'Dubai International Airport', country: 'United Arab Emirates' },
        { code: 'SIN', city: 'Singapore', name: 'Changi Airport', country: 'Singapore' },
        { code: 'HKG', city: 'Hong Kong', name: 'Hong Kong International Airport', country: 'Hong Kong' },
        { code: 'FRA', city: 'Frankfurt', name: 'Frankfurt Airport', country: 'Germany' }
    ];

    const allAirlines = [
        { name: 'British Airways', rating: 4.5 },
        { name: 'Emirates', rating: 4.7 },
        { name: 'Lufthansa', rating: 4.5 },
        { name: 'Qatar Airways', rating: 4.8 },
        { name: 'Singapore Airlines', rating: 4.9 },
        { name: 'Air France', rating: 4.4 },
        { name: 'KLM', rating: 4.3 },
        { name: 'United Airlines', rating: 4.2 }
    ];

    // Local state for UI interactions (derived from context)
    const selectedTripType = currentSearch?.tripType || 'roundTrip';
    const [fromAirport, setFromAirport] = useState('');
    const [toAirport, setToAirport] = useState('');
    const [selectedDates, setSelectedDates] = useState({
        departure: currentSearch?.depart ? (() => {
            const date = currentSearch.depart;
            const year = date.getFullYear();
            const month = String(date.getMonth() + 1).padStart(2, '0');
            const day = String(date.getDate()).padStart(2, '0');
            return `${year}-${month}-${day}`;
        })() : (() => {
            // Default to today's date if no date is set
            const today = new Date();
            const year = today.getFullYear();
            const month = String(today.getMonth() + 1).padStart(2, '0');
            const day = String(today.getDate()).padStart(2, '0');
            return `${year}-${month}-${day}`;
        })(),
        return: currentSearch?.return ? (() => {
            const date = currentSearch.return;
            const year = date.getFullYear();
            const month = String(date.getMonth() + 1).padStart(2, '0');
            const day = String(date.getDate()).padStart(2, '0');
            return `${year}-${month}-${day}`;
        })() : (() => {
            // Default to tomorrow's date if no return date is set and it's a round trip
            if (selectedTripType === 'roundTrip') {
                const tomorrow = new Date();
                tomorrow.setDate(tomorrow.getDate() + 1);
                const year = tomorrow.getFullYear();
                const month = String(tomorrow.getMonth() + 1).padStart(2, '0');
                const day = String(tomorrow.getDate()).padStart(2, '0');
                return `${year}-${month}-${day}`;
            }
            return '';
        })()
    });
    const passengers = currentSearch?.passengers || { adults: 1, children: 0, infants: 0 };
    const selectedClass = currentSearch?.class || 'economy';
    const [showFromDropdown, setShowFromDropdown] = useState(false);
    const [showToDropdown, setShowToDropdown] = useState(false);
    const [showCalendar, setShowCalendar] = useState(false);
    const [dateSelectionState, setDateSelectionState] = useState<'departure' | 'return' | null>(null);
    const [showTravelersClassDropdown, setShowTravelersClassDropdown] = useState(false);
    const [currentMonth, setCurrentMonth] = useState(new Date());
    const [nextMonth, setNextMonth] = useState(new Date(new Date().setMonth(new Date().getMonth() + 1)));
    const [showToast, setShowToast] = useState(false);
    const [showAllAirlines, setShowAllAirlines] = useState(false);
    const [showAdvancedSearch, setShowAdvancedSearch] = useState(false);
    const [tripDays, setTripDays] = useState<string>('');

    // Validation state
    const [validationErrors, setValidationErrors] = useState<{
        from?: string;
        to?: string;
        depart?: string;
        return?: string;
    }>({});

    // Advanced search selections state (derived from context)
    const selectedAirlines = currentSearch?.advanced_search.selectedAirlines || [];
    const selectedFlightOptions = currentSearch?.advanced_search.flightOptions || {
        directFlights: false,
        refundableFares: false,
        corporateRates: false
    };
    const selectedServices = currentSearch?.advanced_search.services || {
        airportLounge: false,
        extraBaggage: false,
        travelInsurance: false
    };

    // Refs for dropdown management
    const fromDropdownRef = useRef<HTMLDivElement>(null);
    const toDropdownRef = useRef<HTMLDivElement>(null);
    const calendarRef = useRef<HTMLDivElement>(null);
    const travelersClassDropdownRef = useRef<HTMLDivElement>(null);
    const formContainerRef = useRef<HTMLDivElement>(null);

    // Update next month when current month changes
    useEffect(() => {
        const nextMonthDate = new Date(currentMonth);
        nextMonthDate.setMonth(nextMonthDate.getMonth() + 1);
        setNextMonth(nextMonthDate);
    }, [currentMonth]);

    // Initialize airport values when context loads
    useEffect(() => {
        if (!isLoading && currentSearch) {
            console.log('🏢 Initializing airports from context:', {
                from: currentSearch.from,
                to: currentSearch.to
            });

            // Set airport values from context if they have valid IATA codes
            if (currentSearch.from?.iata) {
                const fromDisplay = formatAirportDisplay(currentSearch.from);
                setFromAirport(fromDisplay);
                console.log('✅ Set FROM airport from localStorage:', fromDisplay);
            } else {
                // Parse and set default FROM airport in context
                const defaultFromAirport = parseAirportString('New York (JFK)', popularAirports);
                if (defaultFromAirport) {
                    updateSearch({ from: defaultFromAirport });
                    console.log('🔧 Set default FROM airport in context:', defaultFromAirport);
                    // Clear validation error for FROM airport
                    setValidationErrors(prev => ({ ...prev, from: undefined }));
                }
                setFromAirport('New York (JFK)');
                console.log('📝 Using default FROM airport');
            }

            if (currentSearch.to?.iata) {
                const toDisplay = formatAirportDisplay(currentSearch.to);
                setToAirport(toDisplay);
                console.log('✅ Set TO airport from localStorage:', toDisplay);
            } else {
                // Parse and set default TO airport in context
                const defaultToAirport = parseAirportString('London (LHR)', popularAirports);
                if (defaultToAirport) {
                    updateSearch({ to: defaultToAirport });
                    console.log('🔧 Set default TO airport in context:', defaultToAirport);
                    // Clear validation error for TO airport
                    setValidationErrors(prev => ({ ...prev, to: undefined }));
                }
                setToAirport('London (LHR)');
                console.log('📝 Using default TO airport');
            }
        } else if (!isLoading && !currentSearch) {
            // Set defaults if no context data
            setFromAirport('New York (JFK)');
            setToAirport('London (LHR)');
            console.log('📝 Using default airports (no context data)');
        }
    }, [isLoading, currentSearch]);

    // Synchronize selectedDates state with context when context changes
    useEffect(() => {
        if (!isLoading && currentSearch) {
            const formatDateToString = (date: Date | null) => {
                if (!date) return '';
                const year = date.getFullYear();
                const month = String(date.getMonth() + 1).padStart(2, '0');
                const day = String(date.getDate()).padStart(2, '0');
                return `${year}-${month}-${day}`;
            };

            const newDepartureDate = currentSearch.depart ? formatDateToString(currentSearch.depart) : (() => {
                // Default to today's date if no date is set
                const today = new Date();
                return formatDateToString(today);
            })();

            const newReturnDate = currentSearch.return ? formatDateToString(currentSearch.return) : (() => {
                // Default to tomorrow's date if no return date is set and it's a round trip
                if (selectedTripType === 'roundTrip') {
                    const tomorrow = new Date();
                    tomorrow.setDate(tomorrow.getDate() + 1);
                    return formatDateToString(tomorrow);
                }
                return '';
            })();

            console.log('📅 Synchronizing dates from context:', {
                departure: newDepartureDate,
                return: newReturnDate,
                contextDepart: currentSearch.depart,
                contextReturn: currentSearch.return
            });

            setSelectedDates({
                departure: newDepartureDate,
                return: newReturnDate
            });
        }
    }, [isLoading, currentSearch?.depart, currentSearch?.return, selectedTripType]);

    // Clear validation errors when airports are properly set in context
    useEffect(() => {
        if (currentSearch?.from?.iata && currentSearch?.to?.iata) {
            setValidationErrors(prev => {
                const newErrors = { ...prev };

                // Clear airport validation errors if airports are now valid
                if (currentSearch.from.iata && newErrors.from?.includes('select a departure airport')) {
                    delete newErrors.from;
                }
                if (currentSearch.to.iata && newErrors.to?.includes('select a destination airport')) {
                    delete newErrors.to;
                }

                console.log('🧹 Cleared validation errors for valid airports');
                return newErrors;
            });
        }
    }, [currentSearch?.from?.iata, currentSearch?.to?.iata]);

    // Handle modal behavior for travelers/class dropdown only
    useEffect(() => {
        if (showTravelersClassDropdown) {
            // Auto-scroll for travelers/class dropdown if needed
            setTimeout(() => {
                if (formContainerRef.current) {
                    const formRect = formContainerRef.current.getBoundingClientRect();
                    const formTop = formRect.top + window.scrollY;
                    const viewportHeight = window.innerHeight;
                    const modalHeight = 400; // Approximate height of travelers dropdown
                    const optimalScrollY = Math.max(0, formTop - (viewportHeight - modalHeight) / 2);

                    window.scrollTo({
                        top: optimalScrollY,
                        behavior: 'smooth'
                    });
                }
            }, 100);
        }
    }, [showTravelersClassDropdown]);

    // Close dropdowns when clicking outside
    useEffect(() => {
        const handleClickOutside = (event: MouseEvent) => {
            if (fromDropdownRef.current && !fromDropdownRef.current.contains(event.target as Node)) {
                setShowFromDropdown(false);
            }
            if (toDropdownRef.current && !toDropdownRef.current.contains(event.target as Node)) {
                setShowToDropdown(false);
            }
            if (calendarRef.current && !calendarRef.current.contains(event.target as Node)) {
                setShowCalendar(false);
            }
            if (travelersClassDropdownRef.current && !travelersClassDropdownRef.current.contains(event.target as Node)) {
                setShowTravelersClassDropdown(false);
            }
        };

        document.addEventListener('mousedown', handleClickOutside);
        return () => {
            document.removeEventListener('mousedown', handleClickOutside);
        };
    }, []);

    // Validation function
    const validateForm = () => {
        const errors: typeof validationErrors = {};

        // Check if airports are selected (not just default values)
        if (!currentSearch?.from?.iata || currentSearch.from.iata === '') {
            errors.from = 'Please select a departure airport';
        }

        if (!currentSearch?.to?.iata || currentSearch.to.iata === '') {
            errors.to = 'Please select a destination airport';
        }

        // Check if same airport
        if (currentSearch?.from?.iata && currentSearch?.to?.iata &&
            currentSearch.from.iata === currentSearch.to.iata) {
            errors.from = 'Departure and destination airports cannot be the same';
            errors.to = 'Departure and destination airports cannot be the same';
        }

        // Check departure date
        if (!currentSearch?.depart || isNaN(currentSearch.depart.getTime())) {
            errors.depart = 'Please select a departure date';
        }

        // Check return date for round trip
        if (selectedTripType === 'roundTrip') {
            if (!currentSearch?.return || isNaN(currentSearch.return.getTime())) {
                errors.return = 'Please select a return date';
            } else if (currentSearch?.depart && currentSearch.return < currentSearch.depart) {
                errors.return = 'Return date cannot be before departure date';
            }
            // Note: Same day return is allowed (return >= departure)
        }

        setValidationErrors(errors);
        return Object.keys(errors).length === 0;
    };

    // Airport selection handlers that update context
    const handleFromAirportSelect = (airportString: string) => {
        setFromAirport(airportString);
        setShowFromDropdown(false);

        // Parse airport string and update context
        const airport = parseAirportString(airportString, popularAirports);
        if (airport) {
            // Check if same as destination airport
            if (currentSearch?.to?.iata && airport.iata === currentSearch.to.iata) {
                setValidationErrors(prev => ({
                    ...prev,
                    from: 'Departure airport cannot be the same as destination airport',
                    to: 'Destination airport cannot be the same as departure airport'
                }));
                console.warn('⚠️ Same airport selected for departure and destination');
            } else {
                updateSearch({
                    from: airport
                });
                console.log('✅ Updated FROM airport in context:', airport);

                // Clear validation errors for both airports if they were the same
                setValidationErrors(prev => ({
                    ...prev,
                    from: undefined,
                    to: prev.to?.includes('same') ? undefined : prev.to
                }));
            }
        }
    };

    const handleToAirportSelect = (airportString: string) => {
        setToAirport(airportString);
        setShowToDropdown(false);

        // Parse airport string and update context
        const airport = parseAirportString(airportString, popularAirports);
        if (airport) {
            // Check if same as departure airport
            if (currentSearch?.from?.iata && airport.iata === currentSearch.from.iata) {
                setValidationErrors(prev => ({
                    ...prev,
                    to: 'Destination airport cannot be the same as departure airport',
                    from: 'Departure airport cannot be the same as destination airport'
                }));
                console.warn('⚠️ Same airport selected for departure and destination');
            } else {
                updateSearch({
                    to: airport
                });
                console.log('✅ Updated TO airport in context:', airport);

                // Clear validation errors for both airports if they were the same
                setValidationErrors(prev => ({
                    ...prev,
                    to: undefined,
                    from: prev.from?.includes('same') ? undefined : prev.from
                }));
            }
        }
    };



    const handleSearch = () => {
        console.log('Search button clicked');
        console.log('Current search from context:', currentSearch);

        // Validate form first
        if (!validateForm()) {
            console.log('Form validation failed:', validationErrors);
            return;
        }

        // Create legacy format for parent component
        const legacySearchData: FlightSearchData = {
            fromAirport,
            toAirport,
            selectedDates,
            selectedTripType,
            passengers,
            selectedClass
        };

        console.log('Calling parent onSearch with:', legacySearchData);

        // Call the parent's onSearch callback
        onSearch(legacySearchData);
    };

    const handlePassengerChange = (type: 'adults' | 'children' | 'infants', action: 'increase' | 'decrease') => {
        const currentPassengers = currentSearch?.passengers || { adults: 1, children: 0, infants: 0 };
        const newValue = action === 'increase' ? currentPassengers[type] + 1 : Math.max(type === 'adults' ? 1 : 0, currentPassengers[type] - 1);

        updateSearch({
            passengers: {
                ...currentPassengers,
                [type]: newValue
            }
        });
    };

    const getTotalPassengers = () => {
        return passengers.adults + passengers.children + passengers.infants;
    };

    const getTravelersClassText = () => {
        return `${getTotalPassengers()} Traveler${getTotalPassengers() !== 1 ? 's' : ''}, ${classOptions.find(option => option.id === selectedClass)?.label}`;
    };

    const getAdvancedSearchCount = () => {
        const airlineCount = selectedAirlines.length;
        const flightOptionsCount = Object.values(selectedFlightOptions).filter(Boolean).length;
        const servicesCount = Object.values(selectedServices).filter(Boolean).length;

        return airlineCount + flightOptionsCount + servicesCount;
    };

    const handleAirlineToggle = (airlineName: string) => {
        const currentAdvanced = currentSearch?.advanced_search || {
            selectedAirlines: [],
            flightOptions: { directFlights: false, refundableFares: false, corporateRates: false },
            services: { airportLounge: false, extraBaggage: false, travelInsurance: false }
        };

        const newAirlines = currentAdvanced.selectedAirlines.includes(airlineName)
            ? currentAdvanced.selectedAirlines.filter(name => name !== airlineName)
            : [...currentAdvanced.selectedAirlines, airlineName];

        updateSearch({
            advanced_search: {
                ...currentAdvanced,
                selectedAirlines: newAirlines
            }
        });
    };

    const handleFlightOptionToggle = (option: keyof typeof selectedFlightOptions) => {
        const currentAdvanced = currentSearch?.advanced_search || {
            selectedAirlines: [],
            flightOptions: { directFlights: false, refundableFares: false, corporateRates: false },
            services: { airportLounge: false, extraBaggage: false, travelInsurance: false }
        };

        updateSearch({
            advanced_search: {
                ...currentAdvanced,
                flightOptions: {
                    ...currentAdvanced.flightOptions,
                    [option]: !currentAdvanced.flightOptions[option]
                }
            }
        });
    };

    const handleServiceToggle = (service: keyof typeof selectedServices) => {
        const currentAdvanced = currentSearch?.advanced_search || {
            selectedAirlines: [],
            flightOptions: { directFlights: false, refundableFares: false, corporateRates: false },
            services: { airportLounge: false, extraBaggage: false, travelInsurance: false }
        };

        updateSearch({
            advanced_search: {
                ...currentAdvanced,
                services: {
                    ...currentAdvanced.services,
                    [service]: !currentAdvanced.services[service]
                }
            }
        });
    };

    // Handle days input to automatically calculate return date
    const handleDaysInput = (days: string) => {
        // Update trip days in local state
        setTripDays(days);

        if (selectedDates.departure && days && !isNaN(Number(days)) && Number(days) > 0) {
            // Parse departure date
            const [year, month, day] = selectedDates.departure.split('-').map(Number);
            const departureDate = new Date(year, month - 1, day);

            // Calculate return date
            const returnDate = new Date(departureDate);
            returnDate.setDate(departureDate.getDate() + Number(days));

            // Format return date as YYYY-MM-DD
            const returnDateString = `${returnDate.getFullYear()}-${String(returnDate.getMonth() + 1).padStart(2, '0')}-${String(returnDate.getDate()).padStart(2, '0')}`;

            // Update the selected dates
            setSelectedDates(prev => ({
                ...prev,
                return: returnDateString
            }));

            // Also update the search context
            updateSearch({
                return: returnDate
            });

            console.log('🗓️ Auto-calculated return date from days input:', {
                days: Number(days),
                departure: selectedDates.departure,
                calculatedReturn: returnDateString,
                departureDate: departureDate.toLocaleDateString(),
                returnDate: returnDate.toLocaleDateString()
            });
        } else if (!days || days === '0') {
            // Clear return date if days is empty or 0
            setSelectedDates(prev => ({
                ...prev,
                return: ''
            }));
            updateSearch({
                return: null
            });
        }
    };



    // Scroll positioning function for modals
    const scrollToOptimalPosition = (modalHeight: number = 700) => {
        // Close advanced search when any modal opens
        if (showAdvancedSearch) {
            setShowAdvancedSearch(false);
        }

        if (!formContainerRef.current) return;

        const formRect = formContainerRef.current.getBoundingClientRect();
        const viewportHeight = window.innerHeight;
        const formTop = formRect.top + window.scrollY;

        // Calculate optimal scroll position for larger calendar modal
        // We want the modal to be centered in the viewport
        const headerOffset = 40; // Account for any fixed headers
        const bottomPadding = 40; // Padding from bottom of viewport
        const availableSpace = viewportHeight - headerOffset - bottomPadding;

        let targetScrollY;

        // For larger calendar modal, we want to center it in the viewport
        if (modalHeight > availableSpace * 0.85) {
            // If modal is very large, position it near the top with some padding
            targetScrollY = Math.max(0, formTop - headerOffset);
        } else {
            // Center the modal in the viewport by positioning the form appropriately
            const modalCenterOffset = (viewportHeight - modalHeight) / 2;
            targetScrollY = Math.max(0, formTop - modalCenterOffset + 80);
        }

        // Smooth scroll to position with a slight delay to ensure modal is rendered
        setTimeout(() => {
            window.scrollTo({
                top: targetScrollY,
                behavior: 'smooth'
            });
        }, 50);
    };

    const getDaysInMonth = (year: number, month: number) => {
        return new Date(year, month + 1, 0).getDate();
    };

    const getFirstDayOfMonth = (year: number, month: number) => {
        return new Date(year, month, 1).getDay();
    };

    const generateCalendarDays = (year: number, month: number) => {
        const daysInMonth = getDaysInMonth(year, month);
        const firstDay = getFirstDayOfMonth(year, month);
        const days = [];

        // Add empty cells for days before the first day of the month
        for (let i = 0; i < firstDay; i++) {
            days.push(null);
        }

        // Add days of the month
        for (let i = 1; i <= daysInMonth; i++) {
            // Create date string without timezone conversion
            const dateString = `${year}-${String(month + 1).padStart(2, '0')}-${String(i).padStart(2, '0')}`;
            // Generate a random price between $100 and $300
            const price = Math.floor(Math.random() * 200) + 100;
            days.push({
                day: i,
                date: dateString,
                price: price
            });
        }

        return days;
    };

    const handlePrevMonth = () => {
        setCurrentMonth(prev => {
            const newMonth = new Date(prev);
            newMonth.setMonth(newMonth.getMonth() - 1);
            return newMonth;
        });
    };

    const handleNextMonth = () => {
        setCurrentMonth(prev => {
            const newMonth = new Date(prev);
            newMonth.setMonth(newMonth.getMonth() + 1);
            return newMonth;
        });
    };

    // Smart month positioning based on selected dates and which field was clicked
    const setCalendarToOptimalMonth = (clickedField: 'departure' | 'return') => {
        const today = new Date();
        let targetDate = today;

        // If both dates exist, show them optimally
        if (selectedDates.departure && selectedDates.return) {
            const [depYear, depMonth, depDay] = selectedDates.departure.split('-').map(Number);
            const [retYear, retMonth, retDay] = selectedDates.return.split('-').map(Number);
            const departureDate = new Date(depYear, depMonth - 1, depDay);
            const returnDate = new Date(retYear, retMonth - 1, retDay);

            if (clickedField === 'departure') {
                // Show departure month, with return month as next if they're adjacent
                targetDate = departureDate;
            } else {
                // Show return month, but if it's adjacent to departure, show departure month first
                const monthDiff = (retYear - depYear) * 12 + (retMonth - depMonth);
                if (monthDiff === 1) {
                    // Adjacent months - show departure month so both are visible
                    targetDate = departureDate;
                } else {
                    // Not adjacent - show return month
                    targetDate = returnDate;
                }
            }
        } else if (clickedField === 'departure') {
            if (selectedDates.departure) {
                // Show departure month
                const [year, month, day] = selectedDates.departure.split('-').map(Number);
                targetDate = new Date(year, month - 1, day);
            } else {
                // No departure date, show current month
                targetDate = today;
            }
        } else if (clickedField === 'return') {
            if (selectedDates.return) {
                // Show return month
                const [year, month, day] = selectedDates.return.split('-').map(Number);
                targetDate = new Date(year, month - 1, day);
            } else if (selectedDates.departure) {
                // No return but departure exists
                const [year, month, day] = selectedDates.departure.split('-').map(Number);
                const departureDate = new Date(year, month - 1, day);

                // Show departure month so user can see it and select return in next month
                targetDate = departureDate;
            } else {
                // No dates, show current month
                targetDate = today;
            }
        }

        // Ensure we don't go to past months
        const currentMonthStart = new Date(today.getFullYear(), today.getMonth(), 1);
        const targetMonthStart = new Date(targetDate.getFullYear(), targetDate.getMonth(), 1);

        if (targetMonthStart < currentMonthStart) {
            targetDate = today;
        }

        // Set the calendar to show the target month
        setCurrentMonth(new Date(targetDate.getFullYear(), targetDate.getMonth(), 1));

        console.log('🗓️ Setting calendar to optimal month:', {
            clickedField,
            targetDate: targetDate.toLocaleDateString(),
            selectedDeparture: selectedDates.departure,
            selectedReturn: selectedDates.return,
            showingMonth: targetDate.toLocaleDateString('en-US', { month: 'long', year: 'numeric' })
        });
    };

    // Swap airports function
    const handleSwapAirports = () => {
        // Get current airport values
        const currentFromAirport = fromAirport;
        const currentToAirport = toAirport;
        const currentFromData = currentSearch?.from;
        const currentToData = currentSearch?.to;

        // Swap the display values
        setFromAirport(currentToAirport);
        setToAirport(currentFromAirport);

        // Swap the context data
        updateSearch({
            from: currentToData || { city: '', airport: '', iata: '', country: '', airportOpen: false },
            to: currentFromData || { city: '', airport: '', iata: '', country: '', airportOpen: false }
        });

        // Clear any validation errors since we're swapping valid selections
        setValidationErrors(prev => ({
            ...prev,
            from: undefined,
            to: undefined
        }));

        console.log('🔄 Swapped airports:', {
            from: currentToAirport,
            to: currentFromAirport
        });
    };

    // Smart departure date selection with auto-correction
    const handleDepartureDateSelection = (date: string, dateObj: Date) => {
        setSelectedDates(prev => ({ ...prev, departure: date }));
        updateSearch({ depart: dateObj });
        console.log('✅ Updated departure date in context:', dateObj);
        setValidationErrors(prev => ({ ...prev, depart: undefined }));

        // Smart auto-correction: If return date exists and is STRICTLY before the new departure date
        if (selectedDates.return && selectedTripType === 'roundTrip') {
            const [retYear, retMonth, retDay] = selectedDates.return.split('-').map(Number);
            const returnDateObj = new Date(retYear, retMonth - 1, retDay);

            // Only auto-correct if return date is BEFORE departure date (not same day)
            if (returnDateObj < dateObj) {
                // Auto-correct: Set return date to same day as departure (allow same-day returns)
                const sameDayString = `${dateObj.getFullYear()}-${String(dateObj.getMonth() + 1).padStart(2, '0')}-${String(dateObj.getDate()).padStart(2, '0')}`;

                setSelectedDates(prev => ({ ...prev, return: sameDayString }));
                updateSearch({ return: dateObj });

                console.log('🔄 Auto-corrected return date to same day as departure:', sameDayString);
                setValidationErrors(prev => ({ ...prev, return: undefined }));

                // Show a brief notification (optional)
                // You could add a toast notification here
            } else if (returnDateObj.getTime() === dateObj.getTime()) {
                // Same day return - this is valid, just log it
                console.log('ℹ️ Same-day return trip maintained');
                setValidationErrors(prev => ({ ...prev, return: undefined }));
            }
        }
    };

    // Smart return date selection with validation
    const handleReturnDateSelection = (date: string, dateObj: Date) => {
        if (!selectedDates.departure) {
            // No departure date selected yet
            setValidationErrors(prev => ({
                ...prev,
                return: 'Please select a departure date first'
            }));
            console.warn('⚠️ Return date selected before departure date');
            return;
        }

        const [depYear, depMonth, depDay] = selectedDates.departure.split('-').map(Number);
        const departureObj = new Date(depYear, depMonth - 1, depDay);

        if (dateObj < departureObj) {
            // Return date is before departure date - not allowed
            setValidationErrors(prev => ({
                ...prev,
                return: 'Return date cannot be before departure date'
            }));
            console.warn('⚠️ Return date is before departure date');
        } else {
            // Valid return date (same day or after departure)
            setSelectedDates(prev => ({ ...prev, return: date }));
            updateSearch({ return: dateObj });
            console.log('✅ Updated return date in context:', dateObj);
            setValidationErrors(prev => ({ ...prev, return: undefined }));

            if (dateObj.getTime() === departureObj.getTime()) {
                console.log('ℹ️ Same-day return trip selected');
            }
        }
    };

    // Updated handleDateSelect with smart validation and auto-correction
    const handleDateSelect = (date: string) => {
        // Create date object without timezone issues
        // Parse the date string (YYYY-MM-DD) and create a local date
        const [year, month, day] = date.split('-').map(Number);
        const dateObj = new Date(year, month - 1, day); // month is 0-indexed in Date constructor

        console.log('📅 Date selected:', date, '→ Date object:', dateObj);

        if (!dateSelectionState) {
            // If no selection state is set, default to departure
            setDateSelectionState('departure');
            handleDepartureDateSelection(date, dateObj);

            if (selectedTripType === 'roundTrip') {
                // Move to selecting return date
                setDateSelectionState('return');
            } else {
                setShowCalendar(false);
                setDateSelectionState(null);
            }
        } else if (dateSelectionState === 'departure') {
            handleDepartureDateSelection(date, dateObj);

            if (selectedTripType === 'roundTrip') {
                // Move to selecting return date
                setDateSelectionState('return');
            } else {
                setShowCalendar(false);
                setDateSelectionState(null);
            }
        } else if (dateSelectionState === 'return') {
            handleReturnDateSelection(date, dateObj);
            setShowCalendar(false);
            setDateSelectionState(null);
        }
    };

    const isDateInPast = (dateString: string) => {
        const today = new Date();
        today.setHours(0, 0, 0, 0);

        // Create date object without timezone issues
        const [year, month, day] = dateString.split('-').map(Number);
        const date = new Date(year, month - 1, day);
        date.setHours(0, 0, 0, 0);

        // Only disable dates that are strictly before today (not today itself)
        return date < today;
    };

    const isDateSelected = (dateString: string) => {
        return dateString === selectedDates.departure || dateString === selectedDates.return;
    };

    const isDateInRange = (dateString: string) => {
        if (!selectedDates.departure || !selectedDates.return) return false;

        // Create date objects without timezone issues
        const [year, month, day] = dateString.split('-').map(Number);
        const date = new Date(year, month - 1, day);

        const [depYear, depMonth, depDay] = selectedDates.departure.split('-').map(Number);
        const departure = new Date(depYear, depMonth - 1, depDay);

        const [retYear, retMonth, retDay] = selectedDates.return.split('-').map(Number);
        const returnDate = new Date(retYear, retMonth - 1, retDay);

        return date > departure && date < returnDate;
    };

    // Show loading indicator while localStorage is being loaded
    if (isLoading) {
        return (
            <div className="max-w-7xl mx-auto px-4 py-8">
                <div className="bg-white rounded-2xl shadow-lg p-4 md:p-6">
                    <div className="flex items-center justify-center py-12">
                        <div className="flex items-center space-x-3">
                            <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"></div>
                            <span className="text-gray-700 font-medium">Loading search data...</span>
                        </div>
                    </div>
                </div>
            </div>
        );
    }

    return (
        <div className={`${compact ? '' : 'max-w-7xl mx-auto px-4 py-8'}`}>
            <div ref={formContainerRef} className={`${compact ? 'compact-form' : 'bg-white rounded-2xl shadow-lg p-4 md:p-6'}`}>
                {/* Trip Type Selection */}
                <TripTypeSelector
                    tripTypes={tripTypes}
                    selectedTripType={selectedTripType}
                    onTripTypeChange={(tripType: 'roundTrip' | 'oneWay' | 'multiCity') => {
                        updateSearch({ tripType });
                    }}
                    compact={compact}
                />
                {/* Search Fields */}
                <div className={`flex flex-col sm:flex-row sm:items-end gap-4 sm:gap-3 ${compact ? 'mb-3' : 'mb-4'}`}>
                    {/* From */}
                    <div className="relative group flex-1" ref={fromDropdownRef}>
                        <label className="block text-sm font-medium text-gray-700 mb-2 flex items-center">
                            <i className="fas fa-map-marker-alt text-blue-600 mr-2 text-xs"></i>
                            From
                        </label>
                        <div className="relative">
                            <div className="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none">
                                <i className="fas fa-plane-departure text-gray-400"></i>
                            </div>
                            <input
                                type="text"
                                className={`w-full pl-12 pr-16 py-3 border-2 rounded-xl text-sm focus:ring-2 focus:ring-blue-500/20 focus:border-blue-500 transition-all ${
                                    validationErrors.from ? 'border-red-500 focus:border-red-500' : 'border-gray-200'
                                }`}
                                placeholder="City or airport"
                                value={fromAirport}
                                onChange={(e) => {
                                    // Update the input value immediately for typing
                                    setFromAirport(e.target.value);

                                    // Show dropdown when typing
                                    if (!showFromDropdown) {
                                        setShowFromDropdown(true);
                                    }

                                    // Clear validation error when user starts typing
                                    if (validationErrors.from) {
                                        setValidationErrors(prev => ({ ...prev, from: undefined }));
                                    }

                                    // Try to parse if it looks like a complete airport string
                                    if (e.target.value.includes('(') && e.target.value.includes(')')) {
                                        try {
                                            const airportData = parseAirportString(e.target.value, popularAirports);
                                            if (airportData && airportData.iata) {
                                                // Check if same as destination
                                                if (currentSearch?.to?.iata && airportData.iata === currentSearch.to.iata) {
                                                    setValidationErrors(prev => ({
                                                        ...prev,
                                                        from: 'Departure airport cannot be the same as destination airport'
                                                    }));
                                                } else {
                                                    updateSearch({ from: airportData });
                                                    console.log('Auto-parsed FROM airport:', airportData);
                                                    // Clear same airport error if it was set
                                                    setValidationErrors(prev => ({
                                                        ...prev,
                                                        from: prev.from?.includes('same') ? undefined : prev.from,
                                                        to: prev.to?.includes('same') ? undefined : prev.to
                                                    }));
                                                }
                                            }
                                        } catch (error) {
                                            // Ignore parsing errors while typing
                                        }
                                    }
                                }}
                                onClick={() => {
                                    if (!showFromDropdown) {
                                        setShowFromDropdown(true);
                                        setTimeout(() => scrollToOptimalPosition(300), 100);
                                    }
                                }}
                            />
                            <div className="absolute inset-y-0 right-0 pr-3 flex items-center space-x-1">
                                {fromAirport && (
                                    <button
                                        className="!rounded-button text-gray-400 hover:text-red-500 cursor-pointer whitespace-nowrap p-1"
                                        onClick={(e) => {
                                            e.stopPropagation();
                                            setFromAirport('');
                                            updateSearch({
                                                from: { city: '', airport: '', iata: '', country: '', airportOpen: false }
                                            });
                                            setValidationErrors(prev => ({ ...prev, from: undefined }));
                                            setShowFromDropdown(false);
                                        }}
                                        title="Clear selection"
                                    >
                                        <i className="fas fa-times text-xs"></i>
                                    </button>
                                )}
                                <button
                                    className="!rounded-button text-gray-400 hover:text-gray-600 cursor-pointer whitespace-nowrap p-1"
                                    onClick={() => {
                                        setShowFromDropdown(!showFromDropdown);
                                        if (!showFromDropdown) {
                                            setTimeout(() => scrollToOptimalPosition(300), 100);
                                        }
                                    }}
                                >
                                    <i className={`fas fa-chevron-down transition-transform ${showFromDropdown ? 'rotate-180' : ''}`}></i>
                                </button>
                            </div>
                        </div>
                        {showFromDropdown && (
                            <div className="absolute left-0 mt-1 w-full bg-white rounded-xl shadow-xl z-50 max-h-80 overflow-y-auto">
                                <AirportSearch
                                    searchTerm={fromAirport}
                                    airports={popularAirports}
                                    showDropdown={showFromDropdown}
                                    onAirportSelect={handleFromAirportSelect}
                                    icon="fas fa-map-marker-alt"
                                />
                            </div>
                        )}
                        {/* Error message for From airport */}
                        {validationErrors.from && (
                            <div className="mt-1 text-sm text-red-600 flex items-center">
                                <i className="fas fa-exclamation-circle mr-1"></i>
                                {validationErrors.from}
                            </div>
                        )}
                    </div>

                    {/* Swap Button - Inline with fields */}
                    <div className="hidden sm:flex items-end pb-3">
                        <button
                            type="button"
                            className="!rounded-button w-8 h-8 flex items-center justify-center bg-white border-2 border-gray-200 rounded-full text-gray-400 hover:text-blue-600 hover:border-blue-300 hover:bg-blue-50 transition-all cursor-pointer shadow-sm hover:shadow-md"
                            onClick={handleSwapAirports}
                            title="Swap departure and destination airports"
                        >
                            <i className="fas fa-exchange-alt text-xs"></i>
                        </button>
                    </div>

                    {/* To */}
                    <div className="relative group flex-1" ref={toDropdownRef}>
                        <label className="block text-sm font-medium text-gray-700 mb-2 flex items-center">
                            <i className="fas fa-map-marker text-blue-600 mr-2 text-xs"></i>
                            To
                        </label>
                        <div className="relative">
                            <div className="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none">
                                <i className="fas fa-plane-arrival text-gray-400"></i>
                            </div>
                            <input
                                type="text"
                                className={`w-full pl-12 pr-16 py-3 border-2 rounded-xl text-sm focus:ring-2 focus:ring-blue-500/20 focus:border-blue-500 transition-all ${
                                    validationErrors.to ? 'border-red-500 focus:border-red-500' : 'border-gray-200'
                                }`}
                                placeholder="City or airport"
                                value={toAirport}
                                onChange={(e) => {
                                    // Update the input value immediately for typing
                                    setToAirport(e.target.value);

                                    // Show dropdown when typing
                                    if (!showToDropdown) {
                                        setShowToDropdown(true);
                                    }

                                    // Clear validation error when user starts typing
                                    if (validationErrors.to) {
                                        setValidationErrors(prev => ({ ...prev, to: undefined }));
                                    }

                                    // Try to parse if it looks like a complete airport string
                                    if (e.target.value.includes('(') && e.target.value.includes(')')) {
                                        try {
                                            const airportData = parseAirportString(e.target.value, popularAirports);
                                            if (airportData && airportData.iata) {
                                                // Check if same as departure
                                                if (currentSearch?.from?.iata && airportData.iata === currentSearch.from.iata) {
                                                    setValidationErrors(prev => ({
                                                        ...prev,
                                                        to: 'Destination airport cannot be the same as departure airport'
                                                    }));
                                                } else {
                                                    updateSearch({ to: airportData });
                                                    console.log('Auto-parsed TO airport:', airportData);
                                                    // Clear same airport error if it was set
                                                    setValidationErrors(prev => ({
                                                        ...prev,
                                                        to: prev.to?.includes('same') ? undefined : prev.to,
                                                        from: prev.from?.includes('same') ? undefined : prev.from
                                                    }));
                                                }
                                            }
                                        } catch (error) {
                                            // Ignore parsing errors while typing
                                        }
                                    }
                                }}
                                onClick={() => {
                                    if (!showToDropdown) {
                                        setShowToDropdown(true);
                                        setTimeout(() => scrollToOptimalPosition(300), 100);
                                    }
                                }}
                            />
                            <div className="absolute inset-y-0 right-0 pr-3 flex items-center space-x-1">
                                {toAirport && (
                                    <button
                                        className="!rounded-button text-gray-400 hover:text-red-500 cursor-pointer whitespace-nowrap p-1"
                                        onClick={(e) => {
                                            e.stopPropagation();
                                            setToAirport('');
                                            updateSearch({
                                                to: { city: '', airport: '', iata: '', country: '', airportOpen: false }
                                            });
                                            setValidationErrors(prev => ({ ...prev, to: undefined }));
                                            setShowToDropdown(false);
                                        }}
                                        title="Clear selection"
                                    >
                                        <i className="fas fa-times text-xs"></i>
                                    </button>
                                )}
                                <button
                                    className="!rounded-button text-gray-400 hover:text-gray-600 cursor-pointer whitespace-nowrap p-1"
                                    onClick={() => {
                                        setShowToDropdown(!showToDropdown);
                                        if (!showToDropdown) {
                                            setTimeout(() => scrollToOptimalPosition(300), 100);
                                        }
                                    }}
                                >
                                    <i className={`fas fa-chevron-down transition-transform ${showToDropdown ? 'rotate-180' : ''}`}></i>
                                </button>
                            </div>
                        </div>
                        {showToDropdown && (
                            <div className="absolute left-0 mt-1 w-full bg-white rounded-xl shadow-xl z-50 max-h-80 overflow-y-auto">
                                <AirportSearch
                                    searchTerm={toAirport}
                                    airports={popularAirports.filter(airport => airport.code !== toAirpor)}
                                    showDropdown={showToDropdown}
                                    onAirportSelect={handleToAirportSelect}
                                    icon="fas fa-map-marker"
                                />
                            </div>
                        )}
                        {/* Error message for To airport */}
                        {validationErrors.to && (
                            <div className="mt-1 text-sm text-red-600 flex items-center">
                                <i className="fas fa-exclamation-circle mr-1"></i>
                                {validationErrors.to}
                            </div>
                        )}
                    </div>

                    {/* Mobile Swap Button - Appears below airports on small screens */}
                    <div className="sm:hidden flex justify-center pt-2">
                        <button
                            type="button"
                            className="!rounded-button flex items-center justify-center px-4 py-2 bg-white border-2 border-gray-200 rounded-lg text-gray-600 hover:text-blue-600 hover:border-blue-300 hover:bg-blue-50 transition-all cursor-pointer shadow-sm hover:shadow-md"
                            onClick={handleSwapAirports}
                            title="Swap departure and destination airports"
                        >
                            <i className="fas fa-exchange-alt mr-2 text-sm"></i>
                            <span className="text-sm font-medium">Swap Airports</span>
                        </button>
                    </div>

                    {/* Advanced Search Toggle - Compact Button Style */}
                    <div className="flex items-end">
                        <button
                            type="button"
                            className={`!rounded-button flex items-center px-4 py-3 border-2 rounded-xl text-sm transition-all cursor-pointer whitespace-nowrap h-[52px] ${
                                showAdvancedSearch
                                    ? 'border-blue-500 bg-blue-50 text-blue-700'
                                    : 'border-gray-200 hover:border-gray-300 text-gray-700 hover:bg-gray-50'
                            }`}
                            onClick={() => setShowAdvancedSearch(!showAdvancedSearch)}
                            title="Advanced Search Options"
                        >
                            <i className={`fas fa-sliders-h mr-2 text-xs ${showAdvancedSearch ? 'text-blue-600' : 'text-gray-400'}`}></i>
                            <span className="font-medium">Options</span>
                            {getAdvancedSearchCount() > 0 && (
                                <span className="ml-2 bg-blue-600 text-white text-xs font-semibold px-1.5 py-0.5 rounded-full min-w-[18px] text-center">
                                    {getAdvancedSearchCount()}
                                </span>
                            )}
                            <i className={`fas fa-chevron-down ml-2 text-xs transition-transform ${showAdvancedSearch ? 'rotate-180 text-blue-600' : 'text-gray-400'}`}></i>
                        </button>
                    </div>
                </div>

                {/* Advanced Search Options - Appears right below airport selection */}
                {showAdvancedSearch && (
                    <AdvancedSearchContent
                        allAirlines={allAirlines}
                        selectedAirlines={selectedAirlines}
                        selectedFlightOptions={selectedFlightOptions}
                        selectedServices={selectedServices}
                        showAllAirlines={showAllAirlines}
                        showToast={showToast}
                        onAirlineToggle={handleAirlineToggle}
                        onFlightOptionToggle={handleFlightOptionToggle}
                        onServiceToggle={handleServiceToggle}
                        onToggleAllAirlines={() => setShowAllAirlines(!showAllAirlines)}
                        onResetFilters={() => {
                            updateSearch({
                                advanced_search: {
                                    selectedAirlines: [],
                                    flightOptions: {
                                        directFlights: false,
                                        refundableFares: false,
                                        corporateRates: false
                                    },
                                    services: {
                                        airportLounge: false,
                                        extraBaggage: false,
                                        travelInsurance: false
                                    },
                                    stops: 'any',
                                    baggage: {
                                        carryOn: false,
                                        checked: false
                                    }
                                }
                            });
                            setShowToast(true);
                            setTimeout(() => setShowToast(false), 3000);
                        }}
                    />
                )}

                {/* Date Selection and Travelers/Class - Redesigned Layout */}
                <div className={`relative grid grid-cols-1 md:grid-cols-2 gap-4 md:gap-6 ${compact ? 'mb-4' : 'mb-6'}`}>
                    {/* Dates Container */}
                    <div className="w-full">
                        {/* Date Container - Improved Alignment */}
                        <div className="flex items-start space-x-4">
                            {/* Departure Date */}
                            <div className="flex-1 relative" data-calendar-dropdown>
                                <label className="block text-sm font-medium text-gray-700 mb-2 flex items-center">
                                    <i className="fas fa-calendar-alt text-blue-600 mr-2 text-xs"></i>
                                    Departure
                                </label>
                                <div className="relative">
                                    <div className="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none z-10">
                                        <i className="fas fa-plane-departure text-gray-400"></i>
                                    </div>
                                    <input
                                        type="text"
                                        className={`w-full pl-12 pr-12 py-3 border-2 rounded-xl text-sm focus:ring-2 focus:ring-blue-500/20 focus:border-blue-500 transition-all cursor-pointer flex items-center ${
                                            validationErrors.depart ? 'border-red-500 focus:border-red-500' : 'border-gray-200'
                                        }`}
                                        value={selectedDates.departure ? (() => {
                                            // Create date object without timezone issues
                                            const [year, month, day] = selectedDates.departure.split('-').map(Number);
                                            const date = new Date(year, month - 1, day);
                                            const formatted = date.toLocaleDateString('en-US', { month: 'short', day: 'numeric' });
                                            console.log('📅 Displaying departure date:', {
                                                raw: selectedDates.departure,
                                                formatted: formatted,
                                                dateObj: date
                                            });
                                            return formatted;
                                        })() : 'Jun 24'}
                                        onClick={() => {
                                            if (!showCalendar) {
                                                setCalendarToOptimalMonth('departure');
                                                setShowCalendar(true);
                                                setDateSelectionState('departure');
                                            } else {
                                                setShowCalendar(false);
                                                setDateSelectionState(null);
                                            }
                                        }}
                                        readOnly
                                    />
                                    <div className="absolute inset-y-0 right-0 pr-4 flex items-center pointer-events-none z-10">
                                        <i className="fas fa-chevron-down text-gray-400"></i>
                                    </div>
                                </div>
                                {/* Error message for departure date */}
                                {validationErrors.depart && (
                                    <div className="mt-1 text-sm text-red-600 flex items-center">
                                        <i className="fas fa-exclamation-circle mr-1"></i>
                                        {validationErrors.depart}
                                    </div>
                                )}

                                {/* Calendar Modal positioned under departure date */}
                                <CalendarModal
                                    showCalendar={showCalendar}
                                    selectedDates={selectedDates}
                                    selectedTripType={selectedTripType}
                                    dateSelectionState={dateSelectionState}
                                    currentMonth={currentMonth}
                                    nextMonth={nextMonth}
                                    tripDays={tripDays}
                                    calendarRef={calendarRef}
                                    onClose={() => setShowCalendar(false)}
                                    onDateSelect={handleDateSelect}
                                    onPrevMonth={handlePrevMonth}
                                    onNextMonth={handleNextMonth}
                                    onDaysInput={handleDaysInput}
                                    generateCalendarDays={generateCalendarDays}
                                    isDateInPast={isDateInPast}
                                    isDateSelected={isDateSelected}
                                    isDateInRange={isDateInRange}
                                />
                            </div>

                            {/* Return Date */}
                            {selectedTripType === 'roundTrip' ? (
                                <div className="flex-1 relative">
                                    <label className="block text-sm font-medium text-gray-700 mb-2 flex items-center">
                                        <i className="fas fa-calendar-alt text-blue-600 mr-2 text-xs"></i>
                                        Return
                                    </label>
                                    <div className="relative">
                                        <div className="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none z-10">
                                            <i className="fas fa-plane-arrival text-gray-400"></i>
                                        </div>
                                        <input
                                            type="text"
                                            className={`w-full pl-12 pr-12 py-3 border-2 rounded-xl text-sm focus:ring-2 focus:ring-blue-500/20 focus:border-blue-500 transition-all cursor-pointer flex items-center ${
                                                validationErrors.return ? 'border-red-500 focus:border-red-500' : 'border-gray-200'
                                            }`}
                                            value={selectedDates.return ? (() => {
                                                // Create date object without timezone issues
                                                const [year, month, day] = selectedDates.return.split('-').map(Number);
                                                const date = new Date(year, month - 1, day);
                                                const formatted = date.toLocaleDateString('en-US', { month: 'short', day: 'numeric' });
                                                console.log('📅 Displaying return date:', {
                                                    raw: selectedDates.return,
                                                    formatted: formatted,
                                                    dateObj: date
                                                });
                                                return formatted;
                                            })() : 'Jun 26'}
                                            onClick={() => {
                                                if (!showCalendar) {
                                                    setCalendarToOptimalMonth('return');
                                                    setShowCalendar(true);
                                                    setDateSelectionState('return');
                                                } else {
                                                    setShowCalendar(false);
                                                    setDateSelectionState(null);
                                                }
                                            }}
                                            readOnly
                                        />
                                        <div className="absolute inset-y-0 right-0 pr-4 flex items-center pointer-events-none z-10">
                                            <i className="fas fa-chevron-down text-gray-400"></i>
                                        </div>
                                    </div>
                                    {/* Error message for return date */}
                                    {validationErrors.return && (
                                        <div className="mt-1 text-sm text-red-600 flex items-center">
                                            <i className="fas fa-exclamation-circle mr-1"></i>
                                            {validationErrors.return}
                                        </div>
                                    )}

                                    {/* Trip Duration below return date - Enhanced styling */}
                                    {selectedDates.departure && selectedDates.return && (
                                        <div className="mt-3 px-1">
                                            <div className="inline-flex items-center bg-blue-50 px-3 py-1.5 rounded-lg border border-blue-200">
                                                <i className="fas fa-calendar-check text-blue-600 mr-2 text-xs"></i>
                                                <span className="text-xs font-semibold text-blue-700">
                                                    {(() => {
                                                        // Create date objects without timezone issues
                                                        const [depYear, depMonth, depDay] = selectedDates.departure.split('-').map(Number);
                                                        const departureDate = new Date(depYear, depMonth - 1, depDay);

                                                        const [retYear, retMonth, retDay] = selectedDates.return.split('-').map(Number);
                                                        const returnDate = new Date(retYear, retMonth - 1, retDay);

                                                        const timeDiff = returnDate.getTime() - departureDate.getTime();
                                                        const days = Math.ceil(timeDiff / (1000 * 60 * 60 * 24));

                                                        if (days === 0) {
                                                            return 'Same-day return trip';
                                                        } else {
                                                            return `${days} day${days !== 1 ? 's' : ''} trip`;
                                                        }
                                                    })()}
                                                </span>
                                            </div>
                                        </div>
                                    )}
                                </div>
                            ) : (
                                <div className="flex-1 relative">
                                    <label className="block text-sm font-medium text-gray-700 mb-2 flex items-center">
                                        <i className="fas fa-calendar-alt text-blue-600 mr-2 text-xs opacity-50"></i>
                                        Return
                                    </label>
                                    <div className="relative">
                                        <div className="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none z-10">
                                            <i className="fas fa-plane-arrival text-gray-400 opacity-50"></i>
                                        </div>
                                        <input
                                            type="text"
                                            className="w-full pl-12 pr-12 py-3 border-2 border-gray-200 rounded-xl text-sm bg-gray-50 opacity-60 text-gray-400 flex items-center"
                                            value="N/A"
                                            readOnly
                                        />
                                        <div className="absolute inset-y-0 right-0 pr-4 flex items-center pointer-events-none z-10">
                                            <i className="fas fa-ban text-gray-400 opacity-50"></i>
                                        </div>
                                    </div>
                                </div>
                            )}
                        </div>
                    </div>

                    {/* Travelers and Class Combined */}
                    <div className="relative w-full" ref={travelersClassDropdownRef} data-travelers-dropdown>
                        <label className="block text-sm font-medium text-gray-700 mb-2 flex items-center">
                            <i className="fas fa-users text-blue-600 mr-2 text-xs"></i>
                            Travelers & Class
                        </label>
                        <button
                            type="button"
                            className="!rounded-button w-full flex items-center justify-between pl-4 pr-4 py-3 border-2 border-gray-200 rounded-xl text-sm focus:ring-2 focus:ring-blue-500/20 focus:border-blue-500 transition-all cursor-pointer"
                            onClick={() => {
                                if (!showTravelersClassDropdown) {
                                    setShowTravelersClassDropdown(true);
                                } else {
                                    setShowTravelersClassDropdown(false);
                                }
                            }}
                        >
                            <div className="flex items-center">
                                <i className="fas fa-users text-gray-400 mr-3"></i>
                                <span>{getTravelersClassText()}</span>
                            </div>
                            <i className={`fas fa-chevron-down text-gray-400 transition-transform ${showTravelersClassDropdown ? 'rotate-180' : ''}`}></i>
                        </button>
                        <TravelClassPicker
                            passengers={passengers}
                            selectedClass={selectedClass}
                            classOptions={classOptions}
                            showDropdown={showTravelersClassDropdown}
                            onPassengerChange={handlePassengerChange}
                            onClassChange={(classId: 'economy' | 'premiumEconomy' | 'business' | 'first') => {
                                updateSearch({ class: classId });
                            }}
                            onApply={() => setShowTravelersClassDropdown(false)}
                        />
                    </div>
                </div>

                {/* Search Button */}
                <SearchButton onSearch={handleSearch} />

                {/* Recent Searches */}
                <RecentSearches
                    onSearchSelect={(search: any) => {
                        const fromAirportData = parseAirportString(search.fromAirport, popularAirports);
                        const toAirportData = parseAirportString(search.toAirport, popularAirports);

                        updateSearch({
                            from: fromAirportData,
                            to: toAirportData,
                            depart: new Date(search.departureDate),
                            return: search.returnDate ? new Date(search.returnDate) : null,
                            passengers: {
                                adults: search.passengers || 1,
                                children: 0,
                                infants: 0
                            },
                            class: search.class?.toLowerCase() as 'economy' | 'premiumEconomy' | 'business' | 'first' || 'economy',
                            tripType: search.returnDate ? 'roundTrip' : 'oneWay'
                        });
                    }}
                />
            </div>
        </div>
    );
};

export default FlightSearchForm;
