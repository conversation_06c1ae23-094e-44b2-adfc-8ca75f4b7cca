import React from 'react';
import type { FlightFilterState, FlightListItem } from '../../../../../models/flight/flight-list-models';

interface EnhancedSegmentFiltersProps {
  filters: FlightFilterState;
  flights: FlightListItem[];
  onFiltersChange: (filters: FlightFilterState) => void;
  totalFiltered: number;
  segmentIndex: number;
}

const EnhancedSegmentFilters: React.FC<EnhancedSegmentFiltersProps> = ({
  filters,
  flights,
  onFiltersChange,
  totalFiltered,
  segmentIndex
}) => {
  const handleFilterChange = (newFilters: FlightFilterState) => {
    onFiltersChange(newFilters);
  };

  const resetAllFilters = () => {
    const resetFilters = {
      ...filters,
      stops: filters.stops.map(s => ({ ...s, isSelected: false })),
      refundable: { ...filters.refundable, isSelected: false },
      departureTime: filters.departureTime.map(t => ({ ...t, isSelected: false })),
      arrivalTime: filters.arrivalTime.map(t => ({ ...t, isSelected: false })),
      airlines: filters.airlines.map(a => ({ ...a, isSelected: false })),
      connectionAirports: filters.connectionAirports.map(c => ({ ...c, isSelected: false })),
      priceRange: { ...filters.priceRange, currentValue: filters.priceRange.max }
    };
    handleFilterChange(resetFilters);
  };

  // Check if stops have available flights
  const hasStopsWithFlights = (stopValue: number) => {
    if (stopValue === 0) return flights.some(f => f.stops === 0);
    if (stopValue === 1) return flights.some(f => f.stops === 1);
    return flights.some(f => f.stops >= 2);
  };

  const availableStops = filters.stops.filter(stop => hasStopsWithFlights(stop.value));

  return (
    <div className="bg-white rounded-lg border border-gray-200 p-4 mb-4">
      <div className="flex items-center justify-between mb-3">
        <h4 className="text-sm font-semibold text-gray-900">Segment {segmentIndex + 1} Filters</h4>
        <button
          onClick={resetAllFilters}
          className="text-xs text-blue-600 hover:text-blue-800 font-medium"
        >
          Reset
        </button>
      </div>

      {/* Horizontal Filter Layout */}
      <div className="flex flex-wrap gap-4 items-start">
        {/* Stops Filter - Horizontal Pills */}
        {availableStops.length > 0 && (
          <div className="flex items-center space-x-2">
            <span className="text-xs font-medium text-gray-700 whitespace-nowrap">Stops:</span>
            <div className="flex gap-1">
              {availableStops.map((stop) => (
                <button
                  key={stop.value}
                  onClick={() => {
                    const newFilters = {
                      ...filters,
                      stops: filters.stops.map(s =>
                        s.value === stop.value
                          ? { ...s, isSelected: !s.isSelected }
                          : s
                      )
                    };
                    handleFilterChange(newFilters);
                  }}
                  className={`px-2 py-1 text-xs font-medium rounded-full border transition-colors whitespace-nowrap ${
                    stop.isSelected
                      ? 'bg-blue-600 text-white border-blue-600'
                      : 'bg-white text-gray-700 border-gray-300 hover:border-blue-300 hover:bg-blue-50'
                  }`}
                >
                  {stop.text}
                </button>
              ))}
            </div>
          </div>
        )}

        {/* Fare Type Filter */}
        <div className="flex items-center space-x-2">
          <span className="text-xs font-medium text-gray-700 whitespace-nowrap">Fare:</span>
          <label className="flex items-center">
            <input
              type="checkbox"
              checked={filters.refundable.isSelected}
              onChange={(e) => {
                const newFilters = {
                  ...filters,
                  refundable: { ...filters.refundable, isSelected: e.target.checked }
                };
                handleFilterChange(newFilters);
              }}
              className="rounded border-gray-300 text-blue-600 focus:ring-blue-500 mr-1"
            />
            <span className="text-xs text-gray-700">Refundable</span>
          </label>
        </div>

        {/* Departure Timing - Compact */}
        {filters.departureTime.length > 0 && (
          <div className="flex items-center space-x-2">
            <span className="text-xs font-medium text-gray-700 whitespace-nowrap">Departure:</span>
            <div className="flex gap-1">
              {filters.departureTime.map((timeSlot, index) => (
                <button
                  key={index}
                  onClick={() => {
                    const newFilters = {
                      ...filters,
                      departureTime: filters.departureTime.map((t, i) =>
                        i === index ? { ...t, isSelected: !t.isSelected } : t
                      )
                    };
                    handleFilterChange(newFilters);
                  }}
                  className={`px-2 py-1 text-xs rounded border transition-colors whitespace-nowrap ${
                    timeSlot.isSelected
                      ? 'bg-blue-600 text-white border-blue-600'
                      : 'bg-white text-gray-700 border-gray-300 hover:border-blue-300 hover:bg-blue-50'
                  }`}
                  title={timeSlot.text}
                >
                  <i className={`fas fa-${timeSlot.icon} mr-1`}></i>
                  {timeSlot.text.split(' (')[0]}
                </button>
              ))}
            </div>
          </div>
        )}

        {/* Airlines Filter - Compact */}
        {filters.airlines.length > 0 && (
          <div className="flex items-center space-x-2">
            <span className="text-xs font-medium text-gray-700 whitespace-nowrap">Airlines:</span>
            <div className="flex gap-1 max-w-xs overflow-x-auto">
              {filters.airlines.slice(0, 3).map((airline) => (
                <label key={airline.code} className="flex items-center whitespace-nowrap">
                  <input
                    type="checkbox"
                    checked={airline.isSelected}
                    onChange={(e) => {
                      const newFilters = {
                        ...filters,
                        airlines: filters.airlines.map(a =>
                          a.code === airline.code
                            ? { ...a, isSelected: e.target.checked }
                            : a
                        )
                      };
                      handleFilterChange(newFilters);
                    }}
                    className="rounded border-gray-300 text-blue-600 focus:ring-blue-500 mr-1"
                  />
                  <span className="text-xs text-gray-700">{airline.name}</span>
                </label>
              ))}
              {filters.airlines.length > 3 && (
                <span className="text-xs text-gray-500">+{filters.airlines.length - 3} more</span>
              )}
            </div>
          </div>
        )}

        {/* Price Range - Compact */}
        <div className="flex items-center space-x-2 min-w-0">
          <span className="text-xs font-medium text-gray-700 whitespace-nowrap">Max Price:</span>
          <div className="flex items-center space-x-2 min-w-0">
            <input
              type="range"
              min={filters.priceRange.min}
              max={filters.priceRange.max}
              value={filters.priceRange.currentValue}
              onChange={(e) => {
                const newFilters = {
                  ...filters,
                  priceRange: { ...filters.priceRange, currentValue: parseInt(e.target.value) }
                };
                handleFilterChange(newFilters);
              }}
              className="w-20 h-1 bg-gray-200 rounded-lg appearance-none cursor-pointer"
            />
            <span className="text-xs text-gray-600 whitespace-nowrap">
              ₹{filters.priceRange.currentValue.toLocaleString()}
            </span>
          </div>
        </div>

        {/* Via Cities - Compact */}
        {filters.connectionAirports.length > 0 && (
          <div className="flex items-center space-x-2">
            <span className="text-xs font-medium text-gray-700 whitespace-nowrap">Via:</span>
            <div className="flex gap-1 max-w-xs overflow-x-auto">
              {filters.connectionAirports.slice(0, 2).map((airport) => (
                <label key={airport.code} className="flex items-center whitespace-nowrap">
                  <input
                    type="checkbox"
                    checked={airport.isSelected}
                    onChange={(e) => {
                      const newFilters = {
                        ...filters,
                        connectionAirports: filters.connectionAirports.map(c =>
                          c.code === airport.code
                            ? { ...c, isSelected: e.target.checked }
                            : c
                        )
                      };
                      handleFilterChange(newFilters);
                    }}
                    className="rounded border-gray-300 text-blue-600 focus:ring-blue-500 mr-1"
                  />
                  <span className="text-xs text-gray-700">{airport.code}</span>
                </label>
              ))}
              {filters.connectionAirports.length > 2 && (
                <span className="text-xs text-gray-500">+{filters.connectionAirports.length - 2}</span>
              )}
            </div>
          </div>
        )}

        {/* Results Count */}
        <div className="flex items-center ml-auto">
          <span className="text-xs text-gray-500 whitespace-nowrap">
            {totalFiltered} of {flights.length} flights
          </span>
        </div>
      </div>
    </div>
  );
};

export default EnhancedSegmentFilters;
