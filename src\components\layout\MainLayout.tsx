import React from 'react';
import Header from '../Header';
import Footer, { type TrustedCompany } from '../Footer';

interface MainLayoutProps {
  children: React.ReactNode;
  showHeader?: boolean;
  showFooter?: boolean;
  showTrustedCompanies?: boolean;
  className?: string;
}

const MainLayout: React.FC<MainLayoutProps> = ({
  children,
  showHeader = true,
  showFooter = true,
  showTrustedCompanies = true,
  className = ''
}) => {
  // Trusted companies data
  const trustedCompanies: TrustedCompany[] = [
    {
      id: 'microsoft',
      name: 'Microsoft',
      icon: 'fab fa-microsoft'
    },
    {
      id: 'amazon',
      name: 'Amazon',
      icon: 'fab fa-amazon'
    },
    {
      id: 'google',
      name: 'Google',
      icon: 'fab fa-google'
    },
    {
      id: 'apple',
      name: 'Apple',
      icon: 'fab fa-apple'
    },
    {
      id: 'salesforce',
      name: 'Salesforce',
      icon: 'fab fa-salesforce'
    }
  ];

  const handleNewsletterSubmit = (email: string) => {
    console.log('Newsletter subscription:', email);
    // Add your newsletter subscription logic here
  };

  return (
    <div className={`min-h-screen flex flex-col ${className}`}>
      {showHeader && <Header />}
      
      <main className="flex-1">
        {children}
      </main>
      
      {showFooter && (
        <Footer
          trustedCompanies={showTrustedCompanies ? trustedCompanies : []}
          onNewsletterSubmit={handleNewsletterSubmit}
          showNewsletter={false}
        />
      )}
    </div>
  );
};

export default MainLayout;
