export interface Airport {
    city_code: string
    city_name: string
    code: string
    country: string
    id: string
    name: string
}

export interface AirportApiResponse {
  airports: Airport[];
  has_more: boolean;
  limit: number;
  page: number;
  total_shown: number;
}

export interface AirportSearchApiResponse{
  results: Airport[]
}

export interface Airline{
    name: string;
    iata: string;
    country: string;
    rating: number; 
}

export type TripTypeValue = 'RT' | 'ON' | 'MC';