import type { Airport } from "../../models/flight/common-flight.model";

export const getPopularAirportFromLocal = (): Airport[] => {
  try {
    const data = localStorage.getItem("popularAirport");
    if (!data) return [];

    const parsed = JSON.parse(data);
    
    // Optional: validate it's an array of airports
    if (Array.isArray(parsed)) {
      return parsed as Airport[];
    }

    return [];
  } catch (error) {
    console.error("Error reading popular airports from localStorage:", error);
    return [];
  }
};
