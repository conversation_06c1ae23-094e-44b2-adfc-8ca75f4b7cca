import React from 'react';

interface SelectedDates {
  departure: string;
  return: string;
}

interface MultiDatePickerProps {
  selectedTripType: 'roundTrip' | 'oneWay' | 'multiCity';
  selectedDates: SelectedDates;
  onDateClick: (dateType: 'departure' | 'return') => void;
}

const MultiDatePicker: React.FC<MultiDatePickerProps> = ({
  selectedTripType,
  selectedDates,
  onDateClick
}) => {
  // Format date to be more compact (e.g., "Dec 25" instead of "Mon, Dec 25")
  const formatCompactDate = (dateString: string) => {
    if (!dateString) return '';
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', { month: 'short', day: 'numeric' });
  };

  return (
    <div className="w-full">
      {/* Date Container - Improved Alignment */}
      <div className="flex items-start space-x-4">
        {/* Departure Date */}
        <div className="flex-1 relative">
          <label className="block text-sm font-medium text-gray-700 mb-2 flex items-center">
            <i className="fas fa-calendar-alt text-blue-600 mr-2 text-xs"></i>
            Departure
          </label>
          <div className="relative">
            <div className="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none z-10">
              <i className="fas fa-plane-departure text-gray-400"></i>
            </div>
            <input
              type="text"
              className="w-full pl-12 pr-12 py-3 border-2 border-gray-200 rounded-xl text-sm focus:ring-2 focus:ring-blue-500/20 focus:border-blue-500 transition-all cursor-pointer flex items-center"
              value={selectedDates.departure ? formatCompactDate(selectedDates.departure) : 'Jun 24'}
              onClick={() => onDateClick('departure')}
              readOnly
            />
            <div className="absolute inset-y-0 right-0 pr-4 flex items-center pointer-events-none z-10">
              <i className="fas fa-chevron-down text-gray-400"></i>
            </div>
          </div>
          <div className="absolute inset-y-0 right-0 pr-3 flex items-center z-10">
            <i className="fas fa-chevron-down text-gray-400"></i>
          </div>
        </div>

        {/* Return Date */}
        {selectedTripType === 'roundTrip' ? (
          <div className="flex-1 relative">
            <label className="block text-sm font-medium text-gray-700 mb-2 flex items-center">
              <i className="fas fa-calendar-alt text-blue-600 mr-2 text-xs"></i>
              Return
            </label>
            <div className="relative">
              <div className="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none z-10">
                <i className="fas fa-plane-arrival text-gray-400"></i>
              </div>
              <input
                type="text"
                className="w-full pl-12 pr-12 py-3 border-2 border-gray-200 rounded-xl text-sm focus:ring-2 focus:ring-blue-500/20 focus:border-blue-500 transition-all cursor-pointer flex items-center"
                value={selectedDates.return ? formatCompactDate(selectedDates.return) : 'Jun 26'}
                onClick={() => onDateClick('return')}
                readOnly
              />
              <div className="absolute inset-y-0 right-0 pr-4 flex items-center pointer-events-none z-10">
                <i className="fas fa-chevron-down text-gray-400"></i>
              </div>
            </div>
            <div className="absolute inset-y-0 right-0 pr-3 flex items-center z-10">
              <i className="fas fa-chevron-down text-gray-400"></i>
            </div>

            {/* Trip Duration below return date - Enhanced styling */}
            {selectedDates.departure && selectedDates.return && (
              <div className="mt-3 px-1">
                <div className="inline-flex items-center bg-blue-50 px-3 py-1.5 rounded-lg border border-blue-200">
                  <i className="fas fa-calendar-check text-blue-600 mr-2 text-xs"></i>
                  <span className="text-xs font-semibold text-blue-700">
                    {(() => {
                      const departureDate = new Date(selectedDates.departure);
                      const returnDate = new Date(selectedDates.return);
                      const timeDiff = returnDate.getTime() - departureDate.getTime();
                      const days = Math.ceil(timeDiff / (1000 * 60 * 60 * 24));
                      return `${days} day${days !== 1 ? 's' : ''} trip`;
                    })()}
                  </span>
                </div>
              </div>
            )}
          </div>
        ) : (
          <div className="flex-1 relative">
            <label className="block text-sm font-medium text-gray-700 mb-2 flex items-center">
              <i className="fas fa-calendar-alt text-blue-600 mr-2 text-xs opacity-50"></i>
              Return
            </label>
            <div className="relative">
              <div className="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none z-10">
                <i className="fas fa-plane-arrival text-gray-400 opacity-50"></i>
              </div>
              <input
                type="text"
                className="w-full pl-12 pr-12 py-3 border-2 border-gray-200 rounded-xl text-sm bg-gray-50 opacity-60 text-gray-400 flex items-center"
                value="N/A"
                readOnly
              />
              <div className="absolute inset-y-0 right-0 pr-4 flex items-center pointer-events-none z-10">
                <i className="fas fa-ban text-gray-400 opacity-50"></i>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default MultiDatePicker;
