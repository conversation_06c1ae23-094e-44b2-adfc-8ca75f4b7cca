import React from 'react';
import type { FlightFilterState, FlightListItem } from '../../../../models/flight/flight-list-models';
import './EnhancedFlightFilters.scss';

interface EnhancedFlightFiltersProps {
  filters: FlightFilterState;
  flights: FlightListItem[];
  onFiltersChange: (filters: FlightFilterState) => void;
  totalFiltered: number;
  showReturnTimeFilters?: boolean;
  isScrolled?: boolean; // Add prop to track if page is scrolled (sticky search active)
}

const EnhancedFlightFilters: React.FC<EnhancedFlightFiltersProps> = ({
  filters,
  flights,
  onFiltersChange,
  totalFiltered,
  showReturnTimeFilters = false,
  isScrolled = false
}) => {
  const handleFilterChange = (newFilters: FlightFilterState) => {
    onFiltersChange(newFilters);
  };

  const resetAllFilters = () => {
    const resetFilters = {
      ...filters,
      stops: filters.stops.map(s => ({ ...s, isSelected: false })),
      refundable: { ...filters.refundable, isSelected: false },
      departureTime: filters.departureTime.map(t => ({ ...t, isSelected: false })),
      arrivalTime: filters.arrivalTime.map(t => ({ ...t, isSelected: false })),
      airlines: filters.airlines.map(a => ({ ...a, isSelected: false })),
      connectionAirports: filters.connectionAirports.map(c => ({ ...c, isSelected: false })),
      priceRange: { ...filters.priceRange, currentValue: filters.priceRange.max }
    };
    handleFilterChange(resetFilters);
  };

  // Check if stops have available flights
  const hasStopsWithFlights = (stopValue: number) => {
    if (stopValue === 0) return flights.some(f => f.stops === 0);
    if (stopValue === 1) return flights.some(f => f.stops === 1);
    return flights.some(f => f.stops >= 2);
  };

  const availableStops = filters.stops.filter(stop => hasStopsWithFlights(stop.value));

  // Simple sticky top calculation - when sticky search is active, position below it
  const stickyTopValue = isScrolled ? '70px' : '1rem';

  return (
    <div className="enhanced-flight-filters w-full max-w-xs flex-shrink-0 overflow-hidden">
      <div
        className="bg-white rounded-lg border border-gray-200 p-4 sticky overflow-hidden"
        style={{ top: stickyTopValue }}
      >
        <div className="filter-header">
          <h3>Filters</h3>
          <button onClick={resetAllFilters}>
            Reset All
          </button>
        </div>

        <div className="space-y-4 max-h-[calc(100vh-200px)] overflow-y-auto custom-scrollbar">
          {/* Stops Filter - Horizontal Layout */}
          {availableStops.length > 0 && (
            <div className="filter-section">
              <h4 className="filter-section-title">Stops</h4>
              <div className="filter-buttons-horizontal">
                {availableStops.map((stop) => (
                  <button
                    key={stop.value}
                    onClick={() => {
                      const newFilters = {
                        ...filters,
                        stops: filters.stops.map(s =>
                          s.value === stop.value
                            ? { ...s, isSelected: !s.isSelected }
                            : s
                        )
                      };
                      handleFilterChange(newFilters);
                    }}
                    className={`px-3 py-1.5 text-xs font-medium rounded-full border transition-colors ${
                      stop.isSelected
                        ? 'bg-blue-600 text-white border-blue-600'
                        : 'bg-white text-gray-700 border-gray-300 hover:border-blue-300 hover:bg-blue-50'
                    }`}
                  >
                    {stop.text}
                  </button>
                ))}
              </div>
            </div>
          )}

          {/* Fare Type Filter */}
          <div className="filter-section">
            <h4 className="filter-section-title">Fare Type</h4>
            <div className="checkbox-list">
              <label>
                <input
                  type="checkbox"
                  checked={filters.refundable.isSelected}
                  onChange={(e) => {
                    const newFilters = {
                      ...filters,
                      refundable: { ...filters.refundable, isSelected: e.target.checked }
                    };
                    handleFilterChange(newFilters);
                  }}
                  className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                />
                <span>{filters.refundable.text}</span>
              </label>
            </div>
          </div>

          {/* Departure Timing Slabs */}
          {filters.departureTime.length > 0 && (
            <div className="filter-section">
              <h4 className="filter-section-title">Departure Time</h4>
              <div className="filter-buttons-grid">
                {filters.departureTime.map((timeSlot, index) => (
                  <button
                    key={index}
                    onClick={() => {
                      const newFilters = {
                        ...filters,
                        departureTime: filters.departureTime.map((t, i) =>
                          i === index ? { ...t, isSelected: !t.isSelected } : t
                        )
                      };
                      handleFilterChange(newFilters);
                    }}
                    className={`p-2 text-xs rounded-lg border transition-colors text-center ${
                      timeSlot.isSelected
                        ? 'bg-blue-600 text-white border-blue-600'
                        : 'bg-white text-gray-700 border-gray-300 hover:border-blue-300 hover:bg-blue-50'
                    }`}
                  >
                    <div className="flex flex-col items-center">
                      <i className={`fas fa-${timeSlot.icon} mb-1`}></i>
                      <span className="font-medium">{timeSlot.text.split(' (')[0]}</span>
                      <span className="text-xs opacity-75">{timeSlot.text.match(/\(([^)]+)\)/)?.[1]}</span>
                    </div>
                  </button>
                ))}
              </div>
            </div>
          )}

          {/* Arrival Timing Slabs */}
          {filters.arrivalTime.length > 0 && (
            <div className="filter-section">
              <h4 className="filter-section-title">Arrival Time</h4>
              <div className="filter-buttons-grid">
                {filters.arrivalTime.map((timeSlot, index) => (
                  <button
                    key={index}
                    onClick={() => {
                      const newFilters = {
                        ...filters,
                        arrivalTime: filters.arrivalTime.map((t, i) =>
                          i === index ? { ...t, isSelected: !t.isSelected } : t
                        )
                      };
                      handleFilterChange(newFilters);
                    }}
                    className={`p-2 text-xs rounded-lg border transition-colors text-center ${
                      timeSlot.isSelected
                        ? 'bg-blue-600 text-white border-blue-600'
                        : 'bg-white text-gray-700 border-gray-300 hover:border-blue-300 hover:bg-blue-50'
                    }`}
                  >
                    <div className="flex flex-col items-center">
                      <i className={`fas fa-${timeSlot.icon} mb-1`}></i>
                      <span className="font-medium">{timeSlot.text.split(' (')[0]}</span>
                      <span className="text-xs opacity-75">{timeSlot.text.match(/\(([^)]+)\)/)?.[1]}</span>
                    </div>
                  </button>
                ))}
              </div>
            </div>
          )}

          {/* Return Departure Time (for round trip international) */}
          {showReturnTimeFilters && filters.departureTime.length > 0 && (
            <div className="filter-section">
              <h4 className="filter-section-title">Return Departure Time</h4>
              <div className="filter-buttons-grid">
                {filters.departureTime.map((timeSlot, index) => (
                  <button
                    key={`return-dep-${index}`}
                    onClick={() => {
                      const newFilters = {
                        ...filters,
                        departureTime: filters.departureTime.map((t, i) =>
                          i === index ? { ...t, isSelected: !t.isSelected } : t
                        )
                      };
                      handleFilterChange(newFilters);
                    }}
                    className={`p-2 text-xs rounded-lg border transition-colors text-center ${
                      timeSlot.isSelected
                        ? 'bg-blue-600 text-white border-blue-600'
                        : 'bg-white text-gray-700 border-gray-300 hover:border-blue-300 hover:bg-blue-50'
                    }`}
                  >
                    <div className="flex flex-col items-center">
                      <i className={`fas fa-${timeSlot.icon} mb-1`}></i>
                      <span className="font-medium">{timeSlot.text.split(' (')[0]}</span>
                      <span className="text-xs opacity-75">{timeSlot.text.match(/\(([^)]+)\)/)?.[1]}</span>
                    </div>
                  </button>
                ))}
              </div>
            </div>
          )}

          {/* Return Arrival Time (for round trip international) */}
          {showReturnTimeFilters && filters.arrivalTime.length > 0 && (
            <div className="filter-section">
              <h4 className="filter-section-title">Return Arrival Time</h4>
              <div className="filter-buttons-grid">
                {filters.arrivalTime.map((timeSlot, index) => (
                  <button
                    key={`return-arr-${index}`}
                    onClick={() => {
                      const newFilters = {
                        ...filters,
                        arrivalTime: filters.arrivalTime.map((t, i) =>
                          i === index ? { ...t, isSelected: !t.isSelected } : t
                        )
                      };
                      handleFilterChange(newFilters);
                    }}
                    className={`p-2 text-xs rounded-lg border transition-colors text-center ${
                      timeSlot.isSelected
                        ? 'bg-blue-600 text-white border-blue-600'
                        : 'bg-white text-gray-700 border-gray-300 hover:border-blue-300 hover:bg-blue-50'
                    }`}
                  >
                    <div className="flex flex-col items-center">
                      <i className={`fas fa-${timeSlot.icon} mb-1`}></i>
                      <span className="font-medium">{timeSlot.text.split(' (')[0]}</span>
                      <span className="text-xs opacity-75">{timeSlot.text.match(/\(([^)]+)\)/)?.[1]}</span>
                    </div>
                  </button>
                ))}
              </div>
            </div>
          )}

          {/* Airlines Filter - Only show if airlines are available */}
          {filters.airlines.length > 0 && (
            <div className="filter-section">
              <h4 className="filter-section-title">
                Airlines ({filters.airlines.length} available)
              </h4>
              <div className="checkbox-list custom-scrollbar">
                {filters.airlines.map((airline) => (
                  <label key={airline.code}>
                    <input
                      type="checkbox"
                      checked={airline.isSelected}
                      onChange={(e) => {
                        const newFilters = {
                          ...filters,
                          airlines: filters.airlines.map(a =>
                            a.code === airline.code
                              ? { ...a, isSelected: e.target.checked }
                              : a
                          )
                        };
                        handleFilterChange(newFilters);
                      }}
                      className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                    />
                    <span>{airline.name}</span>
                  </label>
                ))}
              </div>
            </div>
          )}

          {/* Price Range Filter */}
          <div className="filter-section">
            <h4 className="filter-section-title">
              Price Range: ₹{filters.priceRange.currentValue.toLocaleString()}
            </h4>
            <div className="price-range-slider">
              <input
                type="range"
                min={filters.priceRange.min}
                max={filters.priceRange.max}
                value={filters.priceRange.currentValue}
                onChange={(e) => {
                  const newFilters = {
                    ...filters,
                    priceRange: { ...filters.priceRange, currentValue: parseInt(e.target.value) }
                  };
                  handleFilterChange(newFilters);
                }}
              />
              <div className="flex justify-between text-xs text-gray-500 mt-1">
                <span>₹{filters.priceRange.min.toLocaleString()}</span>
                <span>₹{filters.priceRange.max.toLocaleString()}</span>
              </div>
            </div>
          </div>

          {/* Via Cities Filter - Only show if connection airports are available */}
          {filters.connectionAirports.length > 0 && (
            <div className="filter-section">
              <h4 className="filter-section-title">
                Via Cities ({filters.connectionAirports.length} available)
              </h4>
              <div className="checkbox-list custom-scrollbar">
                {filters.connectionAirports.map((airport) => (
                  <label key={airport.code}>
                    <input
                      type="checkbox"
                      checked={airport.isSelected}
                      onChange={(e) => {
                        const newFilters = {
                          ...filters,
                          connectionAirports: filters.connectionAirports.map(c =>
                            c.code === airport.code
                              ? { ...c, isSelected: e.target.checked }
                              : c
                          )
                        };
                        handleFilterChange(newFilters);
                      }}
                      className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                    />
                    <span>{airport.name} ({airport.code})</span>
                  </label>
                ))}
              </div>
            </div>
          )}

          {/* Active Filters Count */}
          <div className="active-filters-count">
            <div className="count-text">
              {totalFiltered} of {flights.length} flights shown
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default EnhancedFlightFilters;
