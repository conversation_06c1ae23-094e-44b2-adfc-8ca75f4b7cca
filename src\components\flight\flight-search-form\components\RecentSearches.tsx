import React, { useEffect, useState } from 'react';
import type { flight_search_form } from '../../../../models/flight/flight-search.model';

interface StoredRecentSearch {
  id: string;
  label: string; // Can be constructed from trip details for display
  form: flight_search_form;
  price: string;
}

interface RecentSearchesProps {
  onSearchSelect: (form: flight_search_form) => void;
}

const STORAGE_KEY = 'recent_flight_searches';

const RecentSearches: React.FC<RecentSearchesProps> = ({ onSearchSelect }) => {
  const [recentSearches, setRecentSearches] = useState<StoredRecentSearch[]>([]);
  const [visibleSearches, setVisibleSearches] = useState<string[]>([]);
  const [showClearDialog, setShowClearDialog] = useState(false);
  const [showToast, setShowToast] = useState(false);

  useEffect(() => {
    const saved = localStorage.getItem(STORAGE_KEY);
    if (saved) {
      const parsed: StoredRecentSearch[] = JSON.parse(saved);
      setRecentSearches(parsed);
      setVisibleSearches(parsed.map(s => s.id));
    }
  }, []);

  const handleClearAll = () => {
    localStorage.removeItem(STORAGE_KEY);
    setRecentSearches([]);
    setVisibleSearches([]);
    setShowClearDialog(false);
    setShowToast(true);
    setTimeout(() => setShowToast(false), 3000);
  };

  const formatDate = (dateStr: string | Date) => {
    const date = new Date(dateStr);
    return date.toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric',
    });
  };

  if (visibleSearches.length === 0 && !showToast) {
    return null;
  }

  return (
    <div className="mt-6">
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-base font-semibold text-gray-800">Recent Searches</h3>
        {visibleSearches.length > 0 && (
          <button
            className="!rounded-button text-sm text-blue-600 hover:text-blue-700"
            onClick={() => setShowClearDialog(true)}
          >
            Clear All
          </button>
        )}
      </div>

      {/* Clear Confirmation Dialog */}
      {showClearDialog && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-xl p-6 max-w-md mx-4">
            <div className="flex items-center mb-4">
              <div className="w-10 h-10 bg-red-100 rounded-full flex items-center justify-center mr-3">
                <i className="fas fa-exclamation-triangle text-red-600"></i>
              </div>
              <div>
                <h3 className="text-lg font-semibold text-gray-800">Clear Recent Searches</h3>
                <p className="text-sm text-gray-600">This action cannot be undone.</p>
              </div>
            </div>
            <div className="flex justify-end space-x-3">
              <button
                className="!rounded-button px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 hover:bg-gray-200"
                onClick={() => setShowClearDialog(false)}
              >
                Cancel
              </button>
              <button
                className="!rounded-button px-4 py-2 text-sm font-medium text-white bg-red-600 hover:bg-red-700"
                onClick={handleClearAll}
              >
                Clear All
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Toast Notification */}
      {showToast && (
        <div className="fixed bottom-4 right-4 bg-gray-800 text-white px-6 py-3 rounded-lg shadow-lg z-50 animate-fade-in-up">
          <div className="flex items-center">
            <i className="fas fa-check-circle mr-2"></i>
            Recent searches cleared successfully
          </div>
        </div>
      )}

      {/* Grid Display */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        {recentSearches
          .filter(s => visibleSearches.includes(s.id))
          .map(search => {
            const trip = search.form.trips[0];
            const from = trip.from.airport;
            const to = trip.to.airport;
            return (
              <div
                key={search.id}
                className="bg-white rounded-xl p-4 border border-gray-100 hover:shadow-md cursor-pointer transition-all"
                onClick={() => onSearchSelect(search.form)}
              >
                <div className="flex items-center justify-between mb-3">
                  <div className="flex items-center space-x-3">
                    <i className="fas fa-plane-departure text-blue-600"></i>
                    <div>
                      <div className="font-medium text-gray-800">
                        {from} → {to}
                      </div>
                      <div className="text-xs text-gray-500">
                        {formatDate(trip.depart)}
                        {trip.return && ` - ${formatDate(trip.return)}`}
                      </div>
                    </div>
                  </div>
                  <div className="text-right">
                    <div className="font-semibold text-blue-600">{search.price}</div>
                    <div className="text-xs text-gray-500">
                      {(Object.values(search.form.travellers) as number[]).reduce((a, b) => a + b, 0)} Pax, {search.form.cabin}
                    </div>
                  </div>
                </div>
                <div className="flex items-center justify-between text-xs text-gray-500">
                  <span>Click to search again</span>
                  <i className="fas fa-arrow-right"></i>
                </div>
              </div>
            );
          })}
      </div>

      <style>{`
        @keyframes fadeInUp {
          from {
            opacity: 0;
            transform: translateY(1rem);
          }
          to {
            opacity: 1;
            transform: translateY(0);
          }
        }
        .animate-fade-in-up {
          animation: fadeInUp 0.3s ease-out;
        }
      `}</style>
    </div>
  );
};

export default RecentSearches;
