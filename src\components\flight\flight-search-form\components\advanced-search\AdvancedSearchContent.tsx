import React, { useState } from "react";
import type { Airline } from "../../../../../models/flight/common-flight.model";

export interface FlightOptions {
  directFlights: boolean;
  refundableFares: boolean;
  corporateRates: boolean;
}

export interface Services {
  airportLounge: boolean;
  extraBaggage: {
    enabled: boolean;
    weight: number; // in kg
    isCustom: boolean;
  };
  travelInsurance: boolean;
}

export interface advancedSearchData{
  airlines: Airline[]
  services: Services
  flight_option: FlightOptions
}



// Standard baggage weight options
const STANDARD_BAGGAGE_OPTIONS = [
  { weight: 10, price: 25 },
  { weight: 15, price: 35 },
  { weight: 20, price: 50 },
  { weight: 23, price: 60 },
  { weight: 30, price: 85 },
];

interface AdvancedSearchContentProps {
  data : advancedSearchData
  showToast: boolean;
  airlines: Airline[]; // Add airlines prop
  onAirlineToggle: (airlineName: string) => void;
  onFlightOptionToggle: (option: keyof FlightOptions) => void;
  onServiceToggle: (service: keyof Services) => void;
  onExtraBaggageChange: (enabled: boolean, weight?: number, isCustom?: boolean) => void;
  onResetFilters: () => void;
}

const AdvancedSearchContent: React.FC<AdvancedSearchContentProps> = ({
  data,
  showToast,
  airlines,
  onAirlineToggle,
  onFlightOptionToggle,
  onServiceToggle,
  onExtraBaggageChange,
  onResetFilters,
}) => {
  const [showDropdown, setShowDropdown] = useState<boolean>(false);
  const [showBaggageOptions, setShowBaggageOptions] = useState<boolean>(false);
  const [customWeight, setCustomWeight] = useState<string>('');
  const [airlineList,setAirlineList] = useState<Airline[]>(airlines)

  // Get airlines that are not currently selected
  const availableAirlines = airlineList.filter(
    (airline) => !data.airlines.some((a) => a.name === airline.name)
  );

  // Get selected airline objects for display
  const selectedAirlineObjects = airlineList.filter((airline) =>
    data.airlines.some((a) => a.name === airline.name)
  );

  const handleAddAirline = (airlineName: string) => {
    onAirlineToggle(airlineName);
    setShowDropdown(false);
  };

  const handleRemoveAirline = (airlineName: string) => {
    onAirlineToggle(airlineName);
  };

  const handleExtraBaggageToggle = () => {
    if (data.services.extraBaggage.enabled) {
      onExtraBaggageChange(false);
      setShowBaggageOptions(false);
    } else {
      onExtraBaggageChange(true, 23, false); // Default to 23kg
      setShowBaggageOptions(true);
    }
  };

  const handleStandardWeightSelect = (weight: number) => {
    onExtraBaggageChange(true, weight, false);
    setCustomWeight('');
  };

  const handleCustomWeightToggle = () => {
    if (data.services.extraBaggage.isCustom) {
      // Switch back to standard option
      onExtraBaggageChange(true, 23, false);
      setCustomWeight('');
    } else {
      // Switch to custom
      onExtraBaggageChange(true, parseInt(customWeight) || 0, true);
    }
  };

  const handleCustomWeightChange = (value: string) => {
    setCustomWeight(value);
    const weight = parseInt(value);
    if (weight && weight > 0 && weight <= 50) {
      onExtraBaggageChange(true, weight, true);
    }
  };

  const calculateBaggagePrice = (weight: number) => {
    if (weight <= 10) return 25;
    if (weight <= 15) return 35;
    if (weight <= 20) return 50;
    if (weight <= 23) return 60;
    if (weight <= 30) return 85;
    return Math.max(85, Math.ceil(weight / 5) * 15); // Custom pricing for higher weights
  };

  return (
    <div className="mb-6 animate-fade-in">
      <div className="bg-white rounded-xl p-6 shadow-lg border border-gray-100">
        <div className="flex items-center justify-between mb-6">
          <h3 className="text-lg font-semibold text-gray-800 flex items-center">
            <i className="fas fa-sliders-h text-blue-600 mr-3"></i>
            Advanced Search
          </h3>
          <div className="relative">
            <button
              id="reset-filters-btn"
              onClick={onResetFilters}
              className="text-sm text-blue-600 hover:text-blue-700 flex items-center"
            >
              <i className="fas fa-redo-alt mr-2"></i>
              Reset All
            </button>
            {showToast && (
              <div className="fixed bottom-4 left-1/2 transform -translate-x-1/2 bg-gray-800 text-white px-6 py-3 rounded-lg shadow-lg z-50 animate-fade-in-up">
                <div className="flex items-center">
                  <i className="fas fa-check-circle mr-2"></i>
                  All filters have been reset
                </div>
              </div>
            )}
          </div>
          <style>
            {`
@keyframes fadeInUp {
from {
opacity: 0;
transform: translate(-50%, 1rem);
}
to {
opacity: 1;
transform: translate(-50%, 0);
}
}
.animate-fade-in-up {
animation: fadeInUp 0.3s ease-out;
}
@keyframes fadeIn {
from {
opacity: 0;
transform: translateY(-10px);
}
to {
opacity: 1;
transform: translateY(0);
}
}
.animate-fade-in {
animation: fadeIn 0.3s ease-out;
}
`}
          </style>
        </div>

        {/* Search Options Grid */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          {/* Airline Selection */}
          <div className="space-y-4">
            <h4 className="text-sm font-medium text-gray-700 mb-3 flex items-center">
              <i className="fas fa-plane-departure text-blue-600 mr-2"></i>
              Airlines
            </h4>

            {/* Selected Airlines List */}
            <div className="space-y-2">
              {selectedAirlineObjects.map((airline, index) => (
                <div
                  key={index}
                  className="flex items-center justify-between p-3 bg-blue-50 rounded-lg border border-blue-200"
                >
                  <div className="flex items-center">
                    <div className="w-5 h-5 bg-blue-600 border-2 border-blue-600 rounded-md flex items-center justify-center">
                      <i className="fas fa-check text-white text-xs"></i>
                    </div>
                    <span className="ml-3 text-sm font-medium text-gray-700">
                      {airline.name}
                    </span>
                  </div>
                  <div className="flex items-center">
                    <div className="flex items-center text-sm text-gray-500 mr-3">
                      <i className="fas fa-star text-amber-400 mr-1"></i>
                      <span>{airline.rating}</span>
                    </div>
                    <button
                      onClick={() => handleRemoveAirline(airline.name)}
                      className="text-red-500 hover:text-red-700 transition-colors"
                    >
                      <i className="fas fa-times"></i>
                    </button>
                  </div>
                </div>
              ))}

              {/* Add Airlines Dropdown */}
              {availableAirlines.length > 0 && (
                <div className="relative">
                  <button
                    onClick={() => setShowDropdown(!showDropdown)}
                    className="w-full flex items-center justify-between p-3 bg-gray-50 rounded-lg hover:bg-gray-100 transition-all border-2 border-dashed border-gray-300 hover:border-blue-300"
                  >
                    <div className="flex items-center">
                      <i className="fas fa-plus text-blue-600 mr-3"></i>
                      <span className="text-sm text-gray-600">
                        Add Airlines
                      </span>
                    </div>
                    <i
                      className={`fas fa-chevron-${
                        showDropdown ? "up" : "down"
                      } text-gray-400`}
                    ></i>
                  </button>

                  {showDropdown && (
                    <div className="absolute top-full left-0 right-0 mt-1 bg-white border border-gray-200 rounded-lg shadow-lg z-10 max-h-48 overflow-y-auto">
                      {availableAirlines.map((airline, index) => (
                        <button
                          key={index}
                          onClick={() => handleAddAirline(airline.name)}
                          className="w-full flex items-center justify-between p-3 hover:bg-blue-50 transition-all text-left border-b border-gray-100 last:border-b-0"
                        >
                          <div className="flex items-center">
                            <i className="fas fa-plane text-gray-400 mr-3"></i>
                            <span className="text-sm font-medium text-gray-700">
                              {airline.name}
                            </span>
                          </div>
                          <div className="flex items-center text-sm text-gray-500">
                            <i className="fas fa-star text-amber-400 mr-1"></i>
                            <span>{airline.rating}</span>
                          </div>
                        </button>
                      ))}
                    </div>
                  )}
                </div>
              )}

              {selectedAirlineObjects.length === 0 && (
                <div className="text-center py-4 text-gray-500">
                  <i className="fas fa-plane text-gray-300 text-2xl mb-2"></i>
                  <p className="text-sm">No airlines selected</p>
                </div>
              )}
            </div>
          </div>

          {/* Flight Options */}
          <div className="space-y-4">
            <h4 className="text-sm font-medium text-gray-700 mb-3 flex items-center">
              <i className="fas fa-filter text-blue-600 mr-2"></i>
              Flight Options
            </h4>
            <div className="space-y-2">
              <label className="flex items-center justify-between p-3 bg-gray-50 rounded-lg hover:bg-blue-50 transition-all cursor-pointer group">
                <div className="flex items-center flex-1">
                  <div className="relative flex items-center">
                    <input
                      type="checkbox"
                      className="peer sr-only"
                      checked={ data.flight_option.directFlights}
                      onChange={() => onFlightOptionToggle("directFlights")}
                    />
                    <div
                      className={`w-5 h-5 bg-white border-2 rounded-md transition-all ${
                        data.flight_option.directFlights
                          ? "bg-blue-600 border-blue-600"
                          : "border-gray-300"
                      }`}
                    ></div>
                    <i
                      className={`fas fa-check text-white text-xs absolute left-1.5 transition-opacity ${
                        data.flight_option.directFlights
                          ? "opacity-100"
                          : "opacity-0"
                      }`}
                    ></i>
                  </div>
                  <div className="ml-3">
                    <span className="text-sm font-medium text-gray-700 group-hover:text-blue-700">
                      Direct Flights Only
                    </span>
                    <p className="text-xs text-gray-500">No layovers</p>
                  </div>
                </div>
                <div className="flex items-center text-sm font-medium text-blue-600">
                  +$50
                </div>
              </label>
              <label className="flex items-center justify-between p-3 bg-gray-50 rounded-lg hover:bg-blue-50 transition-all cursor-pointer group">
                <div className="flex items-center flex-1">
                  <div className="relative flex items-center">
                    <input
                      type="checkbox"
                      className="peer sr-only"
                      checked={data.flight_option.refundableFares}
                      onChange={() => onFlightOptionToggle("refundableFares")}
                    />
                    <div
                      className={`w-5 h-5 bg-white border-2 rounded-md transition-all ${
                        data.flight_option.refundableFares
                          ? "bg-blue-600 border-blue-600"
                          : "border-gray-300"
                      }`}
                    ></div>
                    <i
                      className={`fas fa-check text-white text-xs absolute left-1.5 transition-opacity ${
                        data.flight_option.refundableFares
                          ? "opacity-100"
                          : "opacity-0"
                      }`}
                    ></i>
                  </div>
                  <div className="ml-3">
                    <span className="text-sm font-medium text-gray-700 group-hover:text-blue-700">
                      Refundable Fares
                    </span>
                    <p className="text-xs text-gray-500">Free cancellation</p>
                  </div>
                </div>
                <div className="flex items-center text-sm font-medium text-blue-600">
                  +$30
                </div>
              </label>
              <label className="flex items-center justify-between p-3 bg-gray-50 rounded-lg hover:bg-blue-50 transition-all cursor-pointer group">
                <div className="flex items-center flex-1">
                  <div className="relative flex items-center">
                    <input
                      type="checkbox"
                      className="peer sr-only"
                      checked={data.flight_option.corporateRates}
                      onChange={() => onFlightOptionToggle("corporateRates")}
                    />
                    <div
                      className={`w-5 h-5 bg-white border-2 rounded-md transition-all ${
                        data.flight_option.corporateRates
                          ? "bg-blue-600 border-blue-600"
                          : "border-gray-300"
                      }`}
                    ></div>
                    <i
                      className={`fas fa-check text-white text-xs absolute left-1.5 transition-opacity ${
                        data.flight_option.corporateRates
                          ? "opacity-100"
                          : "opacity-0"
                      }`}
                    ></i>
                  </div>
                  <div className="ml-3">
                    <span className="text-sm font-medium text-gray-700 group-hover:text-blue-700">
                      Corporate Rates
                    </span>
                    <p className="text-xs text-gray-500">
                      Special business fares
                    </p>
                  </div>
                </div>
                <div className="flex items-center text-sm font-medium text-green-600">
                  -15%
                </div>
              </label>
            </div>
          </div>

          {/* Additional Services */}
          <div className="space-y-4">
            <h4 className="text-sm font-medium text-gray-700 mb-3 flex items-center">
              <i className="fas fa-concierge-bell text-blue-600 mr-2"></i>
              Additional Services
            </h4>
            <div className="space-y-2">
              <label className="flex items-center justify-between p-3 bg-gray-50 rounded-lg hover:bg-blue-50 transition-all cursor-pointer group">
                <div className="flex items-center flex-1">
                  <div className="relative flex items-center">
                    <input
                      type="checkbox"
                      className="peer sr-only"
                      checked={data.services.airportLounge}
                      onChange={() => onServiceToggle("airportLounge")}
                    />
                    <div
                      className={`w-5 h-5 bg-white border-2 rounded-md transition-all ${
                        data.services.airportLounge
                          ? "bg-blue-600 border-blue-600"
                          : "border-gray-300"
                      }`}
                    ></div>
                    <i
                      className={`fas fa-check text-white text-xs absolute left-1.5 transition-opacity ${
                        data.services.airportLounge
                          ? "opacity-100"
                          : "opacity-0"
                      }`}
                    ></i>
                  </div>
                  <div className="ml-3">
                    <span className="text-sm font-medium text-gray-700 group-hover:text-blue-700">
                      Airport Lounge
                    </span>
                    <p className="text-xs text-gray-500">Priority access</p>
                  </div>
                </div>
                <div className="flex items-center text-sm font-medium text-blue-600">
                  +$45
                </div>
              </label>

              {/* Enhanced Extra Baggage Section */}
              <div className="bg-gray-50 rounded-lg overflow-hidden">
                <label className="flex items-center justify-between p-3 hover:bg-blue-50 transition-all cursor-pointer group">
                  <div className="flex items-center flex-1">
                    <div className="relative flex items-center">
                      <input
                        type="checkbox"
                        className="peer sr-only"
                        checked={data.services.extraBaggage.enabled}
                        onChange={handleExtraBaggageToggle}
                      />
                      <div
                        className={`w-5 h-5 bg-white border-2 rounded-md transition-all ${
                          data.services.extraBaggage.enabled
                            ? "bg-blue-600 border-blue-600"
                            : "border-gray-300"
                        }`}
                      ></div>
                      <i
                        className={`fas fa-check text-white text-xs absolute left-1.5 transition-opacity ${
                          data.services.extraBaggage.enabled
                            ? "opacity-100"
                            : "opacity-0"
                        }`}
                      ></i>
                    </div>
                    <div className="ml-3">
                      <span className="text-sm font-medium text-gray-700 group-hover:text-blue-700">
                        Extra Baggage
                      </span>
                      <p className="text-xs text-gray-500">
                        {data.services.extraBaggage.enabled
                          ? `${data.services.extraBaggage.weight}kg additional`
                          : "Additional weight allowance"
                        }
                      </p>
                    </div>
                  </div>
                  <div className="flex items-center text-sm font-medium text-blue-600">
                    +${data.services.extraBaggage.enabled 
                      ? calculateBaggagePrice(data.services.extraBaggage.weight)
                      : '60'
                    }
                  </div>
                </label>

                {/* Baggage Weight Options */}
                {data.services.extraBaggage.enabled && (
                  <div className="px-3 pb-3 space-y-2 border-t border-gray-200 pt-3 bg-white">
                    <div className="text-xs font-medium text-gray-600 mb-2">Select weight:</div>
                    
                    {/* Standard Options */}
                    <div className="grid grid-cols-2 gap-2">
                      {STANDARD_BAGGAGE_OPTIONS.map((option) => (
                        <button
                          key={option.weight}
                          onClick={() => handleStandardWeightSelect(option.weight)}
                          className={`p-2 rounded-md text-xs font-medium transition-all ${
                            !data.services.extraBaggage.isCustom && 
                            data.services.extraBaggage.weight === option.weight
                              ? "bg-blue-600 text-white"
                              : "bg-gray-100 text-gray-700 hover:bg-blue-100"
                          }`}
                        >
                          {option.weight}kg - ${option.price}
                        </button>
                      ))}
                    </div>

                    {/* Custom Weight Option */}
                    <div className="pt-2 border-t border-gray-100">
                      <div className="flex items-center space-x-2">
                        <button
                          onClick={handleCustomWeightToggle}
                          className={`px-3 py-1 rounded-md text-xs font-medium transition-all ${
                            data.services.extraBaggage.isCustom
                              ? "bg-blue-600 text-white"
                              : "bg-gray-100 text-gray-700 hover:bg-blue-100"
                          }`}
                        >
                          Custom
                        </button>
                        {data.services.extraBaggage.isCustom && (
                          <div className="flex items-center space-x-1">
                            <input
                              type="number"
                              min="1"
                              max="50"
                              value={customWeight}
                              onChange={(e) => handleCustomWeightChange(e.target.value)}
                              className="w-16 px-2 py-1 text-xs border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                              placeholder="kg"
                            />
                            <span className="text-xs text-gray-500">
                              kg (${calculateBaggagePrice(data.services.extraBaggage.weight)})
                            </span>
                          </div>
                        )}
                      </div>
                      {data.services.extraBaggage.isCustom && (
                        <p className="text-xs text-gray-500 mt-1">
                          Enter weight between 1-50kg
                        </p>
                      )}
                    </div>
                  </div>
                )}
              </div>

              <label className="flex items-center justify-between p-3 bg-gray-50 rounded-lg hover:bg-blue-50 transition-all cursor-pointer group">
                <div className="flex items-center flex-1">
                  <div className="relative flex items-center">
                    <input
                      type="checkbox"
                      className="peer sr-only"
                      checked={data.services.travelInsurance}
                      onChange={() => onServiceToggle("travelInsurance")}
                    />
                    <div
                      className={`w-5 h-5 bg-white border-2 rounded-md transition-all ${
                        data.services.travelInsurance
                          ? "bg-blue-600 border-blue-600"
                          : "border-gray-300"
                      }`}
                    ></div>
                    <i
                      className={`fas fa-check text-white text-xs absolute left-1.5 transition-opacity ${
                        data.services.travelInsurance
                          ? "opacity-100"
                          : "opacity-0"
                      }`}
                    ></i>
                  </div>
                  <div className="ml-3">
                    <span className="text-sm font-medium text-gray-700 group-hover:text-blue-700">
                      Travel Insurance
                    </span>
                    <p className="text-xs text-gray-500">Full coverage</p>
                  </div>
                </div>
                <div className="flex items-center text-sm font-medium text-blue-600">
                  +$25
                </div>
              </label>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AdvancedSearchContent;