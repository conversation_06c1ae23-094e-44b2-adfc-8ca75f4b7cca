import type { PerformanceMetrics } from '../models/flight/flight-list-models';

/**
 * Performance tracking utility for flight search operations
 */
export class PerformanceTracker {
  private metrics: PerformanceMetrics;
  private startTimes: Map<string, number> = new Map();

  constructor() {
    this.metrics = {
      searchStartTime: 0,
      searchEndTime: 0,
      totalResponseTime: 0,
      cacheHitRate: 0,
      apiCallCount: 0,
      dataProcessingTime: 0
    };
  }

  /**
   * Start tracking a search operation
   */
  startSearch(): void {
    this.metrics.searchStartTime = performance.now();
    this.startTimes.set('search', this.metrics.searchStartTime);
  }

  /**
   * End tracking a search operation
   */
  endSearch(): void {
    this.metrics.searchEndTime = performance.now();
    this.metrics.totalResponseTime = this.metrics.searchEndTime - this.metrics.searchStartTime;
  }

  /**
   * Start tracking data processing
   */
  startDataProcessing(): void {
    this.startTimes.set('dataProcessing', performance.now());
  }

  /**
   * End tracking data processing
   */
  endDataProcessing(): void {
    const startTime = this.startTimes.get('dataProcessing');
    if (startTime) {
      this.metrics.dataProcessingTime = performance.now() - startTime;
    }
  }

  /**
   * Record an API call
   */
  recordApiCall(cacheHit: boolean = false): void {
    this.metrics.apiCallCount++;
    
    // Update cache hit rate
    const totalCacheHits = this.metrics.cacheHitRate * (this.metrics.apiCallCount - 1) + (cacheHit ? 1 : 0);
    this.metrics.cacheHitRate = totalCacheHits / this.metrics.apiCallCount;
  }

  /**
   * Get current metrics
   */
  getMetrics(): PerformanceMetrics {
    return { ...this.metrics };
  }

  /**
   * Reset all metrics
   */
  reset(): void {
    this.metrics = {
      searchStartTime: 0,
      searchEndTime: 0,
      totalResponseTime: 0,
      cacheHitRate: 0,
      apiCallCount: 0,
      dataProcessingTime: 0
    };
    this.startTimes.clear();
  }

  /**
   * Get formatted performance summary
   */
  getFormattedSummary(): string {
    const metrics = this.getMetrics();
    return `
Search Performance Summary:
- Total Response Time: ${metrics.totalResponseTime.toFixed(2)}ms
- Data Processing Time: ${metrics.dataProcessingTime.toFixed(2)}ms
- API Calls Made: ${metrics.apiCallCount}
- Cache Hit Rate: ${(metrics.cacheHitRate * 100).toFixed(1)}%
- Network Time: ${(metrics.totalResponseTime - metrics.dataProcessingTime).toFixed(2)}ms
    `.trim();
  }
}

/**
 * Global performance tracker instance
 */
export const globalPerformanceTracker = new PerformanceTracker();

/**
 * Performance monitoring hook for React components
 */
export const usePerformanceMonitoring = () => {
  const startOperation = (operationName: string) => {
    console.time(operationName);
    return performance.now();
  };

  const endOperation = (operationName: string, startTime: number) => {
    const endTime = performance.now();
    console.timeEnd(operationName);
    return endTime - startTime;
  };

  const logPerformance = (operationName: string, duration: number, additionalData?: any) => {
    console.log(`🚀 Performance: ${operationName} took ${duration.toFixed(2)}ms`, additionalData);
  };

  return {
    startOperation,
    endOperation,
    logPerformance
  };
};

/**
 * Debounce utility for performance optimization
 */
export const debounce = <T extends (...args: any[]) => any>(
  func: T,
  delay: number
): ((...args: Parameters<T>) => void) => {
  let timeoutId: NodeJS.Timeout;
  
  return (...args: Parameters<T>) => {
    clearTimeout(timeoutId);
    timeoutId = setTimeout(() => func(...args), delay);
  };
};

/**
 * Throttle utility for performance optimization
 */
export const throttle = <T extends (...args: any[]) => any>(
  func: T,
  delay: number
): ((...args: Parameters<T>) => void) => {
  let lastCall = 0;
  
  return (...args: Parameters<T>) => {
    const now = Date.now();
    if (now - lastCall >= delay) {
      lastCall = now;
      func(...args);
    }
  };
};

/**
 * Memoization utility for expensive calculations
 */
export const memoize = <T extends (...args: any[]) => any>(
  func: T,
  getKey?: (...args: Parameters<T>) => string
): T => {
  const cache = new Map();
  
  return ((...args: Parameters<T>) => {
    const key = getKey ? getKey(...args) : JSON.stringify(args);
    
    if (cache.has(key)) {
      return cache.get(key);
    }
    
    const result = func(...args);
    cache.set(key, result);
    return result;
  }) as T;
};

/**
 * Batch processing utility for large datasets
 */
export const batchProcess = async <T, R>(
  items: T[],
  processor: (batch: T[]) => Promise<R[]>,
  batchSize: number = 100
): Promise<R[]> => {
  const results: R[] = [];
  
  for (let i = 0; i < items.length; i += batchSize) {
    const batch = items.slice(i, i + batchSize);
    const batchResults = await processor(batch);
    results.push(...batchResults);
    
    // Allow other tasks to run
    await new Promise(resolve => setTimeout(resolve, 0));
  }
  
  return results;
};

/**
 * Virtual scrolling helper for large lists
 */
export const calculateVirtualScrolling = (
  totalItems: number,
  itemHeight: number,
  containerHeight: number,
  scrollTop: number
) => {
  const visibleItemCount = Math.ceil(containerHeight / itemHeight);
  const startIndex = Math.floor(scrollTop / itemHeight);
  const endIndex = Math.min(startIndex + visibleItemCount + 1, totalItems);
  
  return {
    startIndex: Math.max(0, startIndex),
    endIndex,
    visibleItemCount,
    totalHeight: totalItems * itemHeight,
    offsetY: startIndex * itemHeight
  };
};

/**
 * Memory usage monitoring
 */
export const getMemoryUsage = () => {
  if ('memory' in performance) {
    const memory = (performance as any).memory;
    return {
      usedJSHeapSize: memory.usedJSHeapSize,
      totalJSHeapSize: memory.totalJSHeapSize,
      jsHeapSizeLimit: memory.jsHeapSizeLimit,
      usagePercentage: (memory.usedJSHeapSize / memory.jsHeapSizeLimit) * 100
    };
  }
  return null;
};

/**
 * Network performance monitoring
 */
export const measureNetworkPerformance = async (url: string): Promise<{
  responseTime: number;
  downloadTime: number;
  totalTime: number;
}> => {
  const startTime = performance.now();
  
  try {
    const response = await fetch(url);
    const responseTime = performance.now() - startTime;
    
    const downloadStartTime = performance.now();
    await response.text();
    const downloadTime = performance.now() - downloadStartTime;
    
    const totalTime = performance.now() - startTime;
    
    return {
      responseTime,
      downloadTime,
      totalTime
    };
  } catch (error) {
    throw new Error(`Network performance measurement failed: ${error}`);
  }
};
