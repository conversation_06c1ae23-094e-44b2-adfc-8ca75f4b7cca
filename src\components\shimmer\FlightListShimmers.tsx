import React from 'react';
import {
  FlightCardShimmer,
  MultiCityFlightCardShimmer,
  SegmentHeaderShimmer,
  FilterBarShimmer,
  FilterSidebarShimmer,
  CompactFilterShimmer,
  TripSummaryShimmer
} from './FlightShimmer';
import '../../pages/flight/flight-list/components/RoundTripFlightList.scss';

// One-Way Flight List Shimmer - Exact replica of OneWayFlightList layout
export const OneWayFlightListShimmer: React.FC = () => (
  <div className="flex gap-4 lg:gap-6 xl:gap-8">
    {/* Filters Sidebar */}
    <FilterSidebarShimmer />

    {/* Flight List */}
    <div className="flex-1">
      {/* Sort/Filter Header */}
      <div className="bg-white rounded-lg border border-gray-200 p-4 mb-4 animate-pulse">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <div className="bg-gray-200 rounded h-4 w-24"></div>
            <div className="bg-gray-200 rounded h-8 w-32"></div>
          </div>
          <div className="bg-gray-200 rounded h-4 w-20"></div>
        </div>
      </div>

      {/* Flight Cards */}
      <div className="space-y-2 lg:space-y-3 pb-8">
        {[...Array(6)].map((_, index) => (
          <FlightCardShimmer key={index} />
        ))}
      </div>

      {/* Pagination Shimmer */}
      <div className="bg-white rounded-lg border border-gray-200 p-4 mt-6 animate-pulse">
        <div className="flex items-center justify-between">
          <div className="bg-gray-200 rounded h-4 w-32"></div>
          <div className="flex space-x-2">
            {[...Array(5)].map((_, index) => (
              <div key={index} className="bg-gray-200 rounded w-8 h-8"></div>
            ))}
          </div>
          <div className="bg-gray-200 rounded h-4 w-24"></div>
        </div>
      </div>
    </div>
  </div>
);

// Round-Trip Flight List Shimmer - Exact replica of RoundTripFlightList structure
export const RoundTripFlightListShimmer: React.FC = () => (
  <div className="domestic-flight-list">
    <div className="flight-columns-container">
      {/* Outbound Flight Column */}
      <div className="flight-column">
        {/* Column Header */}
        <div className="flight-column-header bg-white rounded-lg border border-gray-200 p-4 mb-4">
          <div className="header-title-row flex items-center justify-between mb-2">
            <div className="animate-pulse">
              <div className="bg-gray-200 rounded h-6 w-32 mb-2"></div>
              <div className="bg-gray-200 rounded h-4 w-48"></div>
            </div>
            <div className="flight-count-indicator animate-pulse">
              <div className="bg-gray-200 rounded h-6 w-16"></div>
            </div>
          </div>
          
          {/* Progress Bar */}
          <div className="progress-bar bg-gray-200 rounded-full h-2 w-full animate-pulse"></div>
        </div>

        {/* Flight List */}
        <div className="flight-list-scrollable flex-1 overflow-y-auto">
          <div className="flight-cards-container space-y-3">
            {[...Array(4)].map((_, index) => (
              <FlightCardShimmer key={`outbound-${index}`} />
            ))}
          </div>
        </div>
      </div>

      {/* Return Flight Column */}
      <div className="flight-column">
        {/* Column Header */}
        <div className="flight-column-header bg-white rounded-lg border border-gray-200 p-4 mb-4">
          <div className="header-title-row flex items-center justify-between mb-2">
            <div className="animate-pulse">
              <div className="bg-gray-200 rounded h-6 w-32 mb-2"></div>
              <div className="bg-gray-200 rounded h-4 w-48"></div>
            </div>
            <div className="flight-count-indicator animate-pulse">
              <div className="bg-gray-200 rounded h-6 w-16"></div>
            </div>
          </div>
          
          {/* Progress Bar */}
          <div className="progress-bar bg-gray-200 rounded-full h-2 w-full animate-pulse"></div>
        </div>

        {/* Flight List */}
        <div className="flight-list-scrollable flex-1 overflow-y-auto">
          <div className="flight-cards-container space-y-3">
            {[...Array(4)].map((_, index) => (
              <FlightCardShimmer key={`return-${index}`} />
            ))}
          </div>
        </div>
      </div>
    </div>
  </div>
);

// Multi-City Flight List Shimmer - Exact replica of MultiCityList structure
export const MultiCityFlightListShimmer: React.FC = () => (
  <div className="flex gap-6">
    {/* Main Content - 70% width */}
    <div className="w-[70%] space-y-6">
      {/* Segment 1 */}
      <div className="space-y-4">
        <SegmentHeaderShimmer />
        
        {/* Filters and Flight List Side by Side */}
        <div className="flex gap-4">
          {/* Filters - Left side */}
          <CompactFilterShimmer />
          
          {/* Flight List - Right side */}
          <div className="flex-1 space-y-3">
            {[...Array(3)].map((_, index) => (
              <MultiCityFlightCardShimmer key={`segment1-${index}`} />
            ))}
          </div>
        </div>
      </div>

      {/* Segment 2 */}
      <div className="space-y-4">
        <SegmentHeaderShimmer />
        
        {/* Filters and Flight List Side by Side */}
        <div className="flex gap-4">
          {/* Filters - Left side */}
          <CompactFilterShimmer />
          
          {/* Flight List - Right side */}
          <div className="flex-1 space-y-3">
            {[...Array(3)].map((_, index) => (
              <MultiCityFlightCardShimmer key={`segment2-${index}`} />
            ))}
          </div>
        </div>
      </div>
    </div>

    {/* Trip Summary Sidebar - 30% width */}
    <div className="w-[30%] sticky top-24 self-start">
      <TripSummaryShimmer />
    </div>
  </div>
);

// Main Flight List Shimmer Component that shows appropriate shimmer based on trip type
interface FlightListShimmerProps {
  tripType: 'oneWay' | 'roundTrip' | 'multiCity';
}

export const FlightListShimmer: React.FC<FlightListShimmerProps> = ({ tripType }) => {
  console.log('🎭 FlightListShimmer rendering with tripType:', tripType);

  switch (tripType) {
    case 'oneWay':
      console.log('🎭 Rendering OneWayFlightListShimmer');
      return <OneWayFlightListShimmer />;
    case 'roundTrip':
      console.log('🎭 Rendering RoundTripFlightListShimmer');
      return <RoundTripFlightListShimmer />;
    case 'multiCity':
      console.log('🎭 Rendering MultiCityFlightListShimmer');
      return <MultiCityFlightListShimmer />;
    default:
      console.log('🎭 Rendering default OneWayFlightListShimmer');
      return <OneWayFlightListShimmer />;
  }
};
