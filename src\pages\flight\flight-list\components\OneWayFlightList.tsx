import React, { useState, useEffect, useMemo, useCallback, useRef } from 'react';
import { FlightCard } from './';
import EnhancedFlightFilters from './EnhancedFlightFilters';
import { applySorting } from '../../../../utils/flightSortingUtils';
import type { FlightResponse } from '../../../../models/flight/flight-list.model';
import type {
  FlightListItem,
  FlightFilterState,
  SortOption,
  PaginationState
} from '../../../../models/flight/flight-list-models';
import {
  DEFAULT_FILTER_STATE,
  DEFAULT_PAGINATION_STATE
} from '../../../../models/flight/flight-list-models';
import { transformFlightResponse } from '../../../../utils/flightDataTransformer';
import {
  applyFilters,
  paginateFlights,
  updateAirlineFilters,
  updateConnectionAirportFilters,
  updatePriceRangeFilter
} from '../../../../utils/flightFilterUtils';
import {
  extractAirlinesFromFlights,
  extractConnectionAirportsFromFlights
} from '../../../../utils/flightDataTransformer';
import type { SortBy } from './types';

// Type alias for compatibility
type NewSortOption = SortOption;

interface OneWayFlightListProps {
  apiData: FlightResponse | null;
  apiMetadata: any;
  isLoading: boolean;
  onShowFlightDetails?: (flight: FlightListItem) => void;
  isScrolled?: boolean; // Add prop to track if page is scrolled (sticky search active)
}

const OneWayFlightList: React.FC<OneWayFlightListProps> = ({
  apiData,
  apiMetadata,
  isLoading,
  onShowFlightDetails,
  isScrolled = false
}) => {
  // Component state for data processing
  const [flights, setFlights] = useState<FlightListItem[]>([]);
  const [filters, setFilters] = useState<FlightFilterState>(DEFAULT_FILTER_STATE);
  const [sortOption, setSortOption] = useState<NewSortOption>('price_low_high');
  const [pagination, setPagination] = useState<PaginationState>(DEFAULT_PAGINATION_STATE);
  const [isDataComplete, setIsDataComplete] = useState<boolean>(false);

  // Infinite scroll state
  const [displayedFlights, setDisplayedFlights] = useState<FlightListItem[]>([]);
  const [currentBatch, setCurrentBatch] = useState(1);
  const [isLoadingMore, setIsLoadingMore] = useState(false);
  const observerRef = useRef<IntersectionObserver | null>(null);
  const loadMoreTriggerRef = useRef<HTMLDivElement | null>(null);

  // Process API data when it changes
  useEffect(() => {
    if (apiData) {
      console.log('🔄 OneWayFlightList: Processing API data:', {
        completed: apiData.Completed,
        tripsCount: apiData.Trips?.length || 0,
        firstTripJourneyCount: apiData.Trips?.[0]?.Journey?.length || 0,
        sampleJourney: apiData.Trips?.[0]?.Journey?.[0]
      });

      const transformedData = transformFlightResponse(apiData, {
        cacheHit: apiMetadata?.cacheHit || false,
        responseTimeMs: apiMetadata?.responseTimeMS || 0
      });

      console.log('🔄 OneWayFlightList: Transformation result:', {
        outboundFlights: transformedData.outboundFlights.length,
        returnFlights: transformedData.returnFlights.length,
        totalResults: transformedData.totalResults
      });

      // For one-way flights, use outbound flights
      const outboundFlights = transformedData.outboundFlights;
      setFlights(outboundFlights);

      // Track if data is complete for pricing display
      setIsDataComplete(apiData.Completed === true);

      // Update filters with new flight data
      console.log('🔄 OneWayFlightList: Updating filters with flight data...');

      // Extract filter data from flights
      const availableAirlines = extractAirlinesFromFlights(outboundFlights);
      const availableAirports = extractConnectionAirportsFromFlights(outboundFlights);

      console.log('🔄 OneWayFlightList: Extracted filter data:', {
        airlines: availableAirlines.length,
        airports: availableAirports.length,
        sampleAirline: availableAirlines[0],
        sampleAirport: availableAirports[0],
        allAirlines: availableAirlines,
        flightSample: outboundFlights[0]
      });

      // Update filters while preserving existing selections
      setFilters(currentFilters => {
        let updatedFilters = updateAirlineFilters(currentFilters, availableAirlines);
        updatedFilters = updateConnectionAirportFilters(updatedFilters, availableAirports);
        updatedFilters = updatePriceRangeFilter(updatedFilters, outboundFlights);

        console.log('✅ OneWayFlightList: Filters updated:', {
          airlinesCount: updatedFilters.airlines.length,
          airportsCount: updatedFilters.connectionAirports.length,
          priceRange: updatedFilters.priceRange
        });

        return updatedFilters;
      });

      console.log('✅ OneWayFlightList: Processed flights:', outboundFlights.length, 'Complete:', apiData.Completed);
    }
  }, [apiData, apiMetadata]);

  // Process flights with filters and sorting (for infinite scroll)
  const processedFlights = useMemo(() => {
    // Apply filters
    const filteredFlights = applyFilters(flights, filters);

    // Apply sorting using the new applySorting function
    const sortedFlights = applySorting(filteredFlights, sortOption);

    return {
      flights: sortedFlights,
      totalFiltered: filteredFlights.length
    };
  }, [flights, filters, sortOption]);

  // Load more flights function
  const loadMoreFlights = useCallback(() => {
    if (isLoadingMore) {
      console.log('🚫 Load more blocked - already loading');
      return;
    }

    const BATCH_SIZE = 20;
    const startIndex = (currentBatch - 1) * BATCH_SIZE;
    const endIndex = startIndex + BATCH_SIZE;
    const nextBatch = processedFlights.flights.slice(startIndex, endIndex);

    console.log('📊 Load more flights:', {
      currentBatch,
      startIndex,
      endIndex,
      nextBatchLength: nextBatch.length,
      totalFlights: processedFlights.flights.length,
      displayedCount: displayedFlights.length
    });

    if (nextBatch.length > 0) {
      setIsLoadingMore(true);

      // Simulate loading delay
      setTimeout(() => {
        setDisplayedFlights(prev => {
          const newFlights = [...prev, ...nextBatch];
          console.log('✅ Added flights, new total:', newFlights.length);
          return newFlights;
        });
        setCurrentBatch(prev => prev + 1);
        setIsLoadingMore(false);
      }, 800); // Slightly longer delay to see shimmer
    } else {
      console.log('🏁 No more flights to load');
    }
  }, [processedFlights.flights, currentBatch, isLoadingMore, displayedFlights.length]);

  // Reset displayed flights when filters or sorting changes
  useEffect(() => {
    const BATCH_SIZE = 20;
    const firstBatch = processedFlights.flights.slice(0, BATCH_SIZE);
    setDisplayedFlights(firstBatch);
    setCurrentBatch(2); // Next batch will be batch 2
  }, [processedFlights.flights]);

  // Intersection Observer for infinite scroll
  useEffect(() => {
    if (observerRef.current) {
      observerRef.current.disconnect();
    }

    observerRef.current = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting && !isLoadingMore) {
            console.log('🔄 Infinite scroll triggered - loading more flights');
            loadMoreFlights();
          }
        });
      },
      {
        threshold: 0.5, // Trigger when 50% of the element is visible
        rootMargin: '200px' // Start loading 200px before the element comes into view
      }
    );

    // Observe all trigger elements
    const triggerElements = document.querySelectorAll('[data-infinite-scroll-trigger]');
    triggerElements.forEach((element) => {
      if (observerRef.current) {
        observerRef.current.observe(element);
      }
    });

    return () => {
      if (observerRef.current) {
        observerRef.current.disconnect();
      }
    };
  }, [loadMoreFlights, isLoadingMore, displayedFlights.length]);

  // Convert new sort option to old format for compatibility
  const legacySortBy: SortBy = sortOption.includes('price') ? 'price' :
                               sortOption.includes('duration') ? 'duration' :
                               sortOption.includes('departure') ? 'departure' : 'recommended';

  // Handle sort change
  const handleSortChange = (newSortBy: SortBy) => {
    const sortMapping: Record<SortBy, NewSortOption> = {
      'price': 'price_low_high',
      'duration': 'duration_short_long',
      'departure': 'departure_early_late',
      'recommended': 'price_low_high'
    };
    setSortOption(sortMapping[newSortBy]);
    // Reset to first batch when sort changes
    setCurrentBatch(1);
  };

  // Handle filter changes
  const handleFilterChange = (newFilters: FlightFilterState) => {
    setFilters(newFilters);
    // Reset to first batch when filters change
    setCurrentBatch(1);
  };

  if (isLoading) {
    return (
      <div className="flex gap-4 lg:gap-6 xl:gap-8">
        {/* Enhanced Filters Sidebar Shimmer */}
        <div className="w-full max-w-xs flex-shrink-0">
          <div className="bg-white rounded-lg border border-gray-200 p-4 sticky top-4">
            <div className="space-y-4">
              {/* Filter Header Shimmer */}
              <div className="flex justify-between items-center">
                <div className="h-5 bg-gray-200 rounded w-16 animate-pulse"></div>
                <div className="h-4 bg-gray-200 rounded w-20 animate-pulse"></div>
              </div>

              {/* Stops Filter Shimmer */}
              <div className="space-y-2">
                <div className="h-4 bg-gray-200 rounded w-12 animate-pulse"></div>
                <div className="flex gap-2">
                  {[...Array(3)].map((_, index) => (
                    <div key={index} className="h-8 bg-gray-200 rounded w-16 animate-pulse"></div>
                  ))}
                </div>
              </div>

              {/* Price Range Shimmer */}
              <div className="space-y-2">
                <div className="h-4 bg-gray-200 rounded w-20 animate-pulse"></div>
                <div className="h-8 bg-gray-200 rounded w-full animate-pulse"></div>
              </div>

              {/* Airlines Shimmer */}
              <div className="space-y-2">
                <div className="h-4 bg-gray-200 rounded w-16 animate-pulse"></div>
                <div className="space-y-2">
                  {[...Array(4)].map((_, index) => (
                    <div key={index} className="flex items-center space-x-2">
                      <div className="w-4 h-4 bg-gray-200 rounded animate-pulse"></div>
                      <div className="h-3 bg-gray-200 rounded w-24 animate-pulse"></div>
                    </div>
                  ))}
                </div>
              </div>

              {/* Time Filters Shimmer */}
              <div className="space-y-2">
                <div className="h-4 bg-gray-200 rounded w-24 animate-pulse"></div>
                <div className="grid grid-cols-2 gap-2">
                  {[...Array(6)].map((_, index) => (
                    <div key={index} className="h-8 bg-gray-200 rounded animate-pulse"></div>
                  ))}
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Flight List Shimmer */}
        <div className="flex-1 space-y-4">
          {/* Sort Bar Shimmer */}
          <div className="flex justify-between items-center bg-white p-4 rounded-lg border border-gray-200">
            <div className="h-4 bg-gray-200 rounded w-32 animate-pulse"></div>
            <div className="h-8 bg-gray-200 rounded w-40 animate-pulse"></div>
          </div>

          {/* Flight Cards Shimmer */}
          {[...Array(5)].map((_, index) => (
            <div key={index} className="bg-white rounded-xl border border-gray-200 p-4">
              <div className="flex items-center justify-between mb-3">
                <div className="flex items-center flex-1">
                  <div className="w-6 h-6 bg-gray-200 rounded-full animate-pulse mr-3"></div>
                  <div className="space-y-1">
                    <div className="h-4 bg-gray-200 rounded w-32 animate-pulse"></div>
                    <div className="h-3 bg-gray-200 rounded w-24 animate-pulse"></div>
                  </div>
                </div>
                <div className="text-right space-y-1">
                  <div className="h-3 bg-gray-200 rounded w-16 animate-pulse"></div>
                  <div className="h-6 bg-gray-200 rounded w-20 animate-pulse"></div>
                  <div className="h-3 bg-gray-200 rounded w-14 animate-pulse"></div>
                </div>
              </div>
              <div className="flex justify-between items-center">
                <div className="flex space-x-8">
                  <div className="space-y-1">
                    <div className="h-5 bg-gray-200 rounded w-16 animate-pulse"></div>
                    <div className="h-3 bg-gray-200 rounded w-12 animate-pulse"></div>
                  </div>
                  <div className="space-y-1">
                    <div className="h-5 bg-gray-200 rounded w-16 animate-pulse"></div>
                    <div className="h-3 bg-gray-200 rounded w-12 animate-pulse"></div>
                  </div>
                </div>
                <div className="h-8 bg-gray-200 rounded w-24 animate-pulse"></div>
              </div>
            </div>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="flex gap-2 lg:gap-3 xl:gap-4">
      {/* Enhanced Filters Sidebar */}
      <EnhancedFlightFilters
        filters={filters}
        flights={flights}
        onFiltersChange={handleFilterChange}
        totalFiltered={processedFlights.totalFiltered}
        showReturnTimeFilters={false}
        isScrolled={isScrolled}
      />

      {/* Flight Results */}
      <div className="flex-1 min-w-0">
        {/* Sort Bar */}
        {/* Sort and Results Header */}
        <div className="flex items-center justify-between mb-4 p-4 bg-white rounded-lg border border-gray-200">
          <div>
            <h2 className="text-lg font-semibold text-gray-900">
              {processedFlights.totalFiltered} flights found
            </h2>
            <p className="text-sm text-gray-500">
              Showing {displayedFlights.length} of {processedFlights.totalFiltered} flights
            </p>
          </div>

          <div className="flex items-center space-x-4">
            <label className="text-sm font-medium text-gray-700">Sort by:</label>
            <select
              value={sortOption}
              onChange={(e) => setSortOption(e.target.value as NewSortOption)}
              className="border border-gray-300 rounded-md px-3 py-2 text-sm focus:ring-blue-500 focus:border-blue-500"
            >
              <option value="price_low_high">Price: Low to High</option>
              <option value="price_high_low">Price: High to Low</option>
              <option value="duration_short_long">Duration: Short to Long</option>
              <option value="duration_long_short">Duration: Long to Short</option>
              <option value="departure_early_late">Departure: Early to Late</option>
              <option value="departure_late_early">Departure: Late to Early</option>
              <option value="arrival_early_late">Arrival: Early to Late</option>
              <option value="arrival_late_early">Arrival: Late to Early</option>
            </select>
          </div>
        </div>

        {/* Flight Cards */}
        <div className="space-y-2 lg:space-y-3 pb-8">
          {displayedFlights.length === 0 ? (
            <div className="text-center py-12">
              <div className="text-gray-400 mb-4">
                <i className="fas fa-plane text-4xl"></i>
              </div>
              <h3 className="text-lg font-medium text-gray-900 mb-2">No flights found</h3>
              <p className="text-gray-500">Try adjusting your filters or search criteria</p>
            </div>
          ) : (
            <>
              {displayedFlights.map((flight: FlightListItem, index: number) => {
                console.log('🛫 Rendering flight:', {
                  id: flight.id,
                  airlineName: flight.airline.name,
                  airlineCode: flight.airline.code,
                  price: flight.price.amount,
                  index: index + 1
                });

                // Check if this is a trigger point (every 15th item: 15, 35, 55, etc.)
                const isLoadTrigger = (index + 1) % 20 === 15;

                return (
                  <div key={flight.id}>
                    <div
                      onClick={() => console.log('Selected flight:', flight)}
                      className="cursor-pointer"
                      {...(isLoadTrigger ? { 'data-infinite-scroll-trigger': 'true' } : {})}
                    >
                      {/* Convert FlightListItem to Flight format for FlightCard */}
                      <FlightCard
                        flight={{
                          id: flight.flightNumber || flight.id,
                          airline: flight.airline.name,
                          alliance: flight.airline.code, // Use airline code instead of 'Unknown'
                          corporateRate: flight.fareType === 'CORPORATE',
                          departure: {
                            city: flight.departure.city,
                            time: flight.departure.time,
                            terminal: flight.departure.terminal || 'N/A'
                          },
                          arrival: {
                            city: flight.arrival.city,
                            time: flight.arrival.time,
                            terminal: flight.arrival.terminal || 'N/A'
                          },
                          duration: flight.duration,
                          stops: flight.stops,
                          price: Math.round(flight.price.amount), // Round to remove decimals
                          originalPrice: Math.round(flight.price.amount * 1.15), // Round original price too
                          seatsAvailable: flight.availability.seats,
                          aircraft: 'Unknown', // FlightListItem doesn't have aircraft
                          amenities: [], // FlightListItem doesn't have amenities
                          fareTypes: [flight.fareType],
                          carbonEmission: '1.5 tons', // Estimate
                          baggageAllowance: flight.baggage.checked
                        }}
                        onFlightSelect={() => console.log('Selected flight:', flight)}
                        onShowDetails={() => onShowFlightDetails?.(flight)} // Pass flight to parent handler
                        showPriceShimmer={!flight.sh_price} // Show shimmer when sh_price is false
                      />
                    </div>
                  </div>
                );
              })}

              {/* Loading more shimmer cards */}
              {isLoadingMore && (
                <>
                  {Array.from({ length: 5 }).map((_, shimmerIndex) => (
                    <div key={`shimmer-${shimmerIndex}`} className="bg-white rounded-lg border border-gray-200 p-4 mb-4">
                      <div className="animate-pulse">
                        <div className="flex items-center justify-between">
                          {/* Left side - Departure */}
                          <div className="flex-1">
                            <div className="h-4 bg-gray-200 rounded w-16 mb-2"></div>
                            <div className="h-3 bg-gray-200 rounded w-20 mb-1"></div>
                            <div className="h-3 bg-gray-200 rounded w-24"></div>
                          </div>

                          {/* Center - Duration */}
                          <div className="flex-1 text-center">
                            <div className="h-3 bg-gray-200 rounded w-16 mx-auto mb-2"></div>
                            <div className="h-8 bg-gray-200 rounded w-20 mx-auto"></div>
                          </div>

                          {/* Right side - Arrival */}
                          <div className="flex-1 text-right">
                            <div className="h-4 bg-gray-200 rounded w-16 ml-auto mb-2"></div>
                            <div className="h-3 bg-gray-200 rounded w-20 ml-auto mb-1"></div>
                            <div className="h-3 bg-gray-200 rounded w-24 ml-auto"></div>
                          </div>

                          {/* Price section */}
                          <div className="ml-6">
                            <div className="h-6 bg-gray-200 rounded w-20 mb-1"></div>
                            <div className="h-4 bg-gray-200 rounded w-16"></div>
                          </div>
                        </div>
                      </div>
                    </div>
                  ))}
                </>
              )}

              {/* End of results indicator */}
              {displayedFlights.length >= processedFlights.totalFiltered && processedFlights.totalFiltered > 0 && (
                <div className="text-center py-8 text-gray-500">
                  <p>You've reached the end of the results</p>
                </div>
              )}
            </>
          )}
        </div>

        {/* Performance Metrics (Development only) */}
        {apiMetadata && (
          <div className="mt-4 p-3 bg-gray-100 rounded-lg text-xs text-gray-600">
            <div>Response Time: {apiMetadata.responseTimeMS || 0}ms</div>
            <div>Cache Hit: {apiMetadata.cacheHit ? 'Yes' : 'No'}</div>
            <div>Data Source: {apiMetadata.dataSource || 'Unknown'}</div>
            <div>Total Flights: {processedFlights.totalFiltered}</div>
          </div>
        )}
      </div>
    </div>
  );
};

export default OneWayFlightList;
