/** @type {import('tailwindcss').Config} */
export default {
  content: ["./index.html", "./src/**/*.{js,ts,jsx,tsx}"],
  theme: {
    extend: {
      borderRadius: {
        'button': '0.75rem', // Custom button border radius
      },
      // Note: Colors are handled by our custom SCSS utility classes
      // This ensures no conflicts between Tailwind colors and our centralized system
      colors: {
        // We can optionally extend with semantic color names that map to CSS custom properties
        // if needed for specific Tailwind utilities that don't have SCSS equivalents
      }
    },
  },
  plugins: [],
  // Ensure our custom color utilities take precedence over Tailwind's color classes
  corePlugins: {
    // We keep Tailwind's color utilities but our custom classes will override them
    // due to CSS specificity and !important declarations
  }
}

