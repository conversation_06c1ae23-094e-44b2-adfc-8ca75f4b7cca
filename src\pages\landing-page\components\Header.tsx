import React from 'react';

const Header: React.FC = () => {
  return (
    <header className="fixed w-full bg-white shadow-sm z-50">
      <div className="max-w-7xl mx-auto px-4">
        <div className="flex items-center justify-between h-16">
          <div className="flex items-center">
            <i className="fas fa-globe text-blue-600 text-3xl mr-2"></i>
            <span className="text-xl font-semibold text-gray-800">TravelPro</span>
          </div>
          <nav className="hidden md:flex items-center space-x-8">
            <a href="#" className="text-gray-600 hover:text-blue-600 text-sm font-medium">Home</a>
            <a href="#" className="text-gray-600 hover:text-blue-600 text-sm font-medium">About</a>
            <a href="#" className="text-gray-600 hover:text-blue-600 text-sm font-medium">Services</a>
            <a href="#" className="text-gray-600 hover:text-blue-600 text-sm font-medium">Contact</a>
          </nav>
        </div>
      </div>
    </header>
  );
};

export default Header;
