export interface BookNowBody {
  flight_booking: FlightBooking
  Travellers: FLightBookingTraveller[]
  ContactInfo: FlightBookingContactInfo
}

export interface FlightBooking {
  provider_info: FlightBookingProviderInfo
  TUI: string
  ADT: number
  CHD: number
  INF: number
  NetAmount: number
  Trips: FLightBookingTrip[]
  AirlineNetFare: number
  SSRAmount: number
  CrossSellAmount: number
  GrossAmount: number
  Hold: boolean
  ActualHoldTime: number
  ActualDisplayTime: number
}

export interface FLightBookingTrip {
  Journey: FLightBookingJourney[]
}

export interface FLightBookingJourney {
  Provider: string
  Stops: number
  Index: string  
  Segments: FLightBookingSegment[]
  Offer: string
  OrderID: number
  GrossFare: number
  NetFare: number
}

export interface FLightBookingSegment {
  Flight: BookingsFlight
  Fares: FLightBookingFares
}

export interface BookingsFlight {
  FUID: string
  VAC: string
  MAC: string
  OAC: string
  Airline: string
  FlightNo: string
  ArrivalTime: string
  DepartureTime: string
  ArrivalCode: string
  DepartureCode: string
  Duration: string
  FareBasisCode: string
  ArrAirportName: string
  DepAirportName: string
  RBD: string
  Cabin: string
  Refundable: string
}

export interface FLightBookingFares {
  GrossFare: number
  NetFare: number
}

export interface FlightBookingProviderInfo {
  code: string
}

export interface FLightBookingTraveller {
  ID: number
  PaxID: number
  Title: string
  FName: string
  LName: string
  Age: number
  DOB: string
  Gender: string
  PTC: string
  PLI: string
  PDOE: string
  Nationality: string
  PassportNo: string
  VisaType: any
  DocType: string
}

export interface FlightBookingContactInfo {
  Title: string
  FName: string
  LName: string
  Mobile: string
  Phone: any
  Email: string
  Address: string
  CountryCode: string
  MobileCountryCode: string
  State: string
  City: string
  PIN: any
  GSTAddress: any
  GSTCompanyName: any
  GSTTIN: any
  UpdateProfile: boolean
  IsGuest: boolean
  SaveGST: boolean
  Language: any
}



export interface DetailedBookingResponse {
  MasterBooking: DetailedMasterBooking
  FlightBooking: DetailedFlightBooking
  FlightRoutes?: FlightRoute[]
  FareBreakdown?: FareBreakdown[]
  BaggageAllowances?: BaggageAllowance[]
  FlightRules?: FlightRule[]
  FlightSSRs?: FlightSSR[]
}

export interface DetailedMasterBooking {
  id: number
  booking_reference: string
  service_type: string
  status: string
  payment_status: string
  user_id: number
  created_at: string
  updated_at: string
  is_deleted: any
}

export interface DetailedFlightBooking {
  id: number
  master_booking_id: string
  provider_info: FlightBookingProviderInfo
  TUI: string
  Mode: any
  TransactionID: string
  ADT: number
  CHD: number
  INF: number
  NetAmount: number
  AirlineNetFare: number
  SSRAmount: number
  GrossAmount: number
  Hold: boolean
  ActualHoldTime: number
  ActualDisplayTime: number
  Code: any
  Msg: any
  ContactInfo: DetailedContactInfo
  created_at: string
  updated_at: string
  is_deleted: any
  Trips: DetailedTrip[]
  Travellers: DetailedTraveller[]
}

export interface FlightRoute {
  id: number
  segment_number: number
  origin_airport: string
  destination_airport: string
  flight_number: string
  aircraft_type: string
  cabin_class: string
  booking_class: string
  departure_datetime: string
  arrival_datetime: string
  departure_timezone?: string
  arrival_timezone?: string
  flight_duration_minutes: number
  layover_duration_minutes: number
  terminal_departure?: string
  terminal_arrival?: string
  gate_departure?: string
  gate_arrival?: string
  is_connecting_flight: boolean
  connection_time_minutes: number
  airline?: {
    code: string
    name: string
    logo_url?: string
  }
}


export interface FareBreakdown {
  id: number
  passenger_type: string
  base_fare: number
  taxes: number
  surcharges: number
  total_fare: number
  currency: string
  fare_basis_code?: string
  fare_family?: string
}

export interface BaggageAllowance {
  id: number
  segment_number: number
  baggage_type: string
  weight_limit_kg?: number
  piece_limit?: number
  dimension_limit?: string
  additional_info?: string
}

export interface FlightRule {
  id: number
  rule_type: string
  is_allowed: boolean
  penalty_amount: number
  penalty_percentage: number
  currency: string
  time_limit_hours?: number
  conditions?: string
}

export interface FlightSSR {
  id: number
  ssr_type: string
  ssr_code: string
  description: string
  amount: number
  currency: string
  passenger_id?: number
  segment_number: number
}

export interface DetailedContactInfo {
  id: number
  flight_booking_id: string
  Title: string
  FName: string
  LName: string
  Mobile: string
  Phone: any
  Email: string
  Address: string
  CountryCode: string
  MobileCountryCode: string
  State: string
  City: string
  PIN: any
  GSTAddress: any
  GSTCompanyName: any
  GSTTIN: any
  UpdateProfile: boolean
  IsGuest: boolean
  SaveGST: boolean
  Language: any
}

export interface DetailedTrip {
  id: number
  flight_booking_id: string
  Provider: string
  Stops: string
  Offer: string
  OrderID: number
  GrossFare: number
  NetFare: number
  Promo: any
  Segments: DetailedSegment[]
}

export interface DetailedSegment {
  id: number
  trip_id: string
  Flight: DetailedFlight
  Fares: any
}

export interface DetailedFlight {
  id: number
  segment_id: string
  FUID: string
  VAC: string
  MAC: string
  OAC: string
  FareBasisCode: string
  Airline: string
  FlightNo: string
  ArrivalTime: string
  DepartureTime: string
  ArrivalCode: string
  DepartureCode: string
  ArrAirportName: string
  DepAirportName: string
  ArrivalTerminal: any
  DepartureTerminal: any
  EquipmentType: any
  RBD: string
  Cabin: string
  Refundable: string
  Duration: string
}

export interface DetailedTraveller {
  id: number
  flight_booking_id: string
  PaxID: number
  Title: string
  FName: string
  LName: string
  Age: number
  DOB: string
  Gender: string
  PTC: string
  Nationality: string
  PassportNo: string
  PLI: string
  PDOE: string
  VisaType: any
  DocType: string
}


// ==================== FLIGHT BOOKING LIST  ====================

export interface BookingList {
  MasterBooking: MasterBooking
  FlightBooking: FlightBookingListResponse
}

export interface MasterBooking {
  id: number
  booking_reference: string
  service_type: string
  status: string
  payment_status: string
  user_id: number
  created_at: string
  updated_at: string
  is_deleted: any
}

export interface FlightBookingListResponse {
  id: number
  master_booking_id: string
  provider_info: FlightBookingProviderInfo
  TUI: string
  Mode: any
  TransactionID: string
  ADT: number
  CHD: number
  INF: number
  NetAmount: number
  AirlineNetFare: number
  SSRAmount: number
  GrossAmount: number
  Hold: boolean
  ActualHoldTime: number
  ActualDisplayTime: number
  Code: any
  Msg: any
  ContactInfo: FlightBookingContactInfo
  created_at: string
  updated_at: string
  is_deleted: any
  Trips: BookingResponseTrip[]
  Travellers: FLightBookingTraveller[]
}

export interface BookingResponseTrip {
  id: number
  flight_booking_id: string
  Provider: string
  Stops: string
  Offer: string
  OrderID: number
  GrossFare: number
  NetFare: number
  Promo: any
  Segments: BookingResponseSegment[]
}

export interface BookingResponseSegment {
  id: number
  trip_id: string
  Flight: BookingResponseFlight
  Fares: any
}

export interface BookingResponseFlight {
  id: number
  segment_id: string
  FUID: string
  VAC: string
  MAC: string
  OAC: string
  FareBasisCode: string
  Airline: string
  FlightNo: string
  ArrivalTime: string
  DepartureTime: string
  ArrivalCode: string
  DepartureCode: string
  ArrAirportName: string
  DepAirportName: string
  ArrivalTerminal: any
  DepartureTerminal: any
  EquipmentType: any
  RBD: string
  Cabin: string
  Refundable: string
  Duration: string
}

// ==================== FLIGHT BOOKING LIST END  ====================
