import React from 'react';

interface SearchButtonProps {
  onSearch: () => void;
  disabled?: boolean;
  loading?: boolean;
}

const SearchButton: React.FC<SearchButtonProps> = ({
  onSearch,
  disabled = false,
  loading = false
}) => {
  return (
    <div className="flex justify-center mb-6">
      <button
        onClick={onSearch}
        disabled={disabled || loading}
        className={`!rounded-button px-8 py-3 text-white font-semibold rounded-xl transition-all cursor-pointer whitespace-nowrap ${
          disabled || loading
            ? 'bg-gray-400 cursor-not-allowed'
            : 'bg-blue-600 hover:bg-blue-700 hover:shadow-lg transform hover:scale-105'
        }`}
      >
        <div className="flex items-center">
          {loading ? (
            <>
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
              Searching...
            </>
          ) : (
            <>
              <i className="fas fa-search mr-2"></i>
              Search Flights
            </>
          )}
        </div>
      </button>
    </div>
  );
};

export default SearchButton;
