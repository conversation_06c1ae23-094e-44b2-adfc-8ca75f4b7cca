import React from 'react';
import type { SegmentFiltersProps } from './types';

const SegmentFilters: React.FC<SegmentFiltersProps> = ({
  segment,
  filters,
  onFiltersChange,
  onClose,
  onApply,
  onReset
}) => {
  const uniqueAirlines = Array.from(
    new Set(segment.flights.map((f) => f.airline.name))
  );
  
  const uniqueFareTypes = Array.from(
    new Set(segment.flights.map((f) => f.fare.type))
  );

  const priceRange = {
    min: Math.min(...segment.flights.map((f) => f.fare.price)),
    max: Math.max(...segment.flights.map((f) => f.fare.price))
  };

  return (
    <div className="absolute right-0 top-full mt-2 w-80 bg-white rounded-lg shadow-xl z-20 p-4">
      {/* Airlines */}
      <div className="mb-4">
        <h3 className="font-medium mb-2">Airlines</h3>
        <div className="space-y-2">
          {uniqueAirlines.map((airline) => (
            <div key={airline} className="flex items-center">
              <input
                type="checkbox"
                id={`airline-${segment.id}-${airline}`}
                className="mr-2"
                checked={filters.airlines.includes(airline)}
                onChange={(e) => {
                  const newAirlines = e.target.checked
                    ? [...filters.airlines, airline]
                    : filters.airlines.filter(a => a !== airline);
                  onFiltersChange({
                    ...filters,
                    airlines: newAirlines
                  });
                }}
              />
              <label htmlFor={`airline-${segment.id}-${airline}`}>
                {airline}
              </label>
            </div>
          ))}
        </div>
      </div>

      {/* Price Range */}
      <div className="mb-4">
        <h3 className="font-medium mb-2">Price Range</h3>
        <div className="px-2">
          <input
            type="range"
            id={`price-range-${segment.id}`}
            min={priceRange.min}
            max={priceRange.max}
            value={filters.priceRange.max}
            className="w-full"
            onChange={(e) => {
              onFiltersChange({
                ...filters,
                priceRange: {
                  ...filters.priceRange,
                  max: parseInt(e.target.value)
                }
              });
            }}
          />
          <div className="flex justify-between text-sm text-gray-500">
            <span>${priceRange.min}</span>
            <span>${priceRange.max}</span>
          </div>
        </div>
      </div>

      {/* Stops */}
      <div className="mb-4">
        <h3 className="font-medium mb-2">Stops</h3>
        <div className="space-y-2">
          <div className="flex items-center">
            <input
              type="checkbox"
              id={`stops-0-${segment.id}`}
              className="mr-2"
              checked={filters.stops.includes(0)}
              onChange={(e) => {
                const newStops = e.target.checked
                  ? [...filters.stops, 0]
                  : filters.stops.filter(s => s !== 0);
                onFiltersChange({
                  ...filters,
                  stops: newStops
                });
              }}
            />
            <label htmlFor={`stops-0-${segment.id}`}>Nonstop</label>
          </div>
          <div className="flex items-center">
            <input
              type="checkbox"
              id={`stops-1-${segment.id}`}
              className="mr-2"
              checked={filters.stops.includes(1)}
              onChange={(e) => {
                const newStops = e.target.checked
                  ? [...filters.stops, 1]
                  : filters.stops.filter(s => s !== 1);
                onFiltersChange({
                  ...filters,
                  stops: newStops
                });
              }}
            />
            <label htmlFor={`stops-1-${segment.id}`}>1 Stop</label>
          </div>
        </div>
      </div>

      {/* Departure Time */}
      <div className="mb-4">
        <h3 className="font-medium mb-2">Departure Time</h3>
        <div className="grid grid-cols-2 gap-2">
          <div>
            <label className="text-sm text-gray-500">From</label>
            <input
              type="time"
              id={`departure-time-from-${segment.id}`}
              className="w-full border rounded px-2 py-1"
              value={filters.departureTime.from}
              onChange={(e) => {
                onFiltersChange({
                  ...filters,
                  departureTime: {
                    ...filters.departureTime,
                    from: e.target.value
                  }
                });
              }}
            />
          </div>
          <div>
            <label className="text-sm text-gray-500">To</label>
            <input
              type="time"
              id={`departure-time-to-${segment.id}`}
              className="w-full border rounded px-2 py-1"
              value={filters.departureTime.to}
              onChange={(e) => {
                onFiltersChange({
                  ...filters,
                  departureTime: {
                    ...filters.departureTime,
                    to: e.target.value
                  }
                });
              }}
            />
          </div>
        </div>
      </div>

      {/* Flight Duration */}
      <div className="mb-4">
        <h3 className="font-medium mb-2">Flight Duration</h3>
        <div className="px-2">
          <input
            type="range"
            id={`duration-${segment.id}`}
            min="0"
            max="24"
            value={filters.duration.max}
            className="w-full"
            onChange={(e) => {
              onFiltersChange({
                ...filters,
                duration: {
                  max: parseInt(e.target.value)
                }
              });
            }}
          />
          <div className="flex justify-between text-sm text-gray-500">
            <span>0h</span>
            <span>24h</span>
          </div>
        </div>
      </div>

      {/* Fare Types */}
      <div className="mb-4">
        <h3 className="font-medium mb-2">Fare Types</h3>
        <div className="space-y-2">
          {uniqueFareTypes.map((type) => (
            <div key={type} className="flex items-center">
              <input
                type="checkbox"
                id={`fare-${segment.id}-${type}`}
                className="mr-2"
                checked={filters.fareTypes.includes(type)}
                onChange={(e) => {
                  const newFareTypes = e.target.checked
                    ? [...filters.fareTypes, type]
                    : filters.fareTypes.filter(t => t !== type);
                  onFiltersChange({
                    ...filters,
                    fareTypes: newFareTypes
                  });
                }}
              />
              <label htmlFor={`fare-${segment.id}-${type}`}>
                {type}
              </label>
            </div>
          ))}
        </div>
      </div>

      {/* Actions */}
      <div className="flex justify-between pt-3 border-t">
        <button
          onClick={onReset}
          className="text-gray-600 hover:text-gray-800"
        >
          Reset
        </button>
        <button
          onClick={onApply}
          className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-1 rounded-button whitespace-nowrap cursor-pointer"
        >
          Apply Filters
        </button>
      </div>
    </div>
  );
};

export default SegmentFilters;
