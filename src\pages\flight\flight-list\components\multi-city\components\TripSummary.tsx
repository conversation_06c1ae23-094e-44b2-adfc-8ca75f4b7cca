import React from 'react';
import AirlineIcon from './AirlineIcon';
import type { TripSummaryProps } from './types';

const TripSummary: React.FC<TripSummaryProps> = ({
  segments,
  selectedFlights,
  totalPrice,
  onBookItinerary,
  onEmailItinerary
}) => {
  const selectedCount = Object.keys(selectedFlights).length;
  const totalSegments = segments.length;

  return (
    <div className="bg-white rounded-lg shadow-md overflow-hidden">
      <div className="bg-gradient-to-r from-blue-700 to-blue-900 text-white p-3">
        <div className="flex items-center justify-between">
          <h2 className="text-lg font-bold">Trip Summary</h2>
          <div className="text-sm bg-blue-600/50 px-2 py-1 rounded">
            {selectedCount}/{totalSegments} Selected
          </div>
        </div>
      </div>
      <div className="p-3">
        {selectedCount === 0 ? (
          <div className="text-center py-8 text-gray-500">
            <i className="fas fa-plane-departure text-4xl mb-3"></i>
            <p>Select flights to build your itinerary</p>
          </div>
        ) : (
          <>
            {segments.map((segment) => {
              const selectedFlight = selectedFlights[segment.id];
              if (!selectedFlight) {
                return (
                  <div
                    key={segment.id}
                    className="mb-4 p-3 border border-dashed border-gray-300 rounded-md text-center text-gray-500"
                  >
                    <p>
                      {segment.origin.code} to {segment.destination.code}
                    </p>
                    <p className="text-sm">No flight selected</p>
                  </div>
                );
              }
              return (
                <div
                  key={segment.id}
                  className="mb-3 border-b border-gray-100 last:border-b-0 last:mb-0 pb-3 last:pb-0"
                >
                  <div className="flex items-center justify-between mb-2 text-sm">
                    <div className="flex items-center space-x-2">
                      <span className="w-2 h-2 rounded-full bg-blue-500"></span>
                      <span>
                        {segment.origin.code} → {segment.destination.code}
                      </span>
                    </div>
                    <span className="text-gray-500">{segment.date}</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-3">
                      <AirlineIcon code={selectedFlight.airline.code} size="sm" />
                      <div>
                        <div className="flex items-center space-x-2">
                          <span className="font-medium text-sm">
                            {selectedFlight.departure.time}
                          </span>
                          <div className="text-gray-400">
                            <i className="fas fa-long-arrow-alt-right text-xs"></i>
                          </div>
                          <span className="font-medium text-sm">
                            {selectedFlight.arrival.time}
                          </span>
                        </div>
                        <div className="text-xs text-gray-500">
                          {selectedFlight.duration} •{" "}
                          {selectedFlight.stops === 0
                            ? "Nonstop"
                            : `${selectedFlight.stops} stop${
                                selectedFlight.stops > 1 ? "s" : ""
                              }`}
                        </div>
                      </div>
                    </div>
                    <div className="text-right">
                      <div className="font-semibold">
                        ${selectedFlight.fare.price}
                      </div>
                      <div className="text-xs text-gray-500">
                        {selectedFlight.fare.type}
                      </div>
                    </div>
                  </div>
                </div>
              );
            })}
            
            {/* Price breakdown */}
            <div className="mt-6 border-t pt-4">
              <div className="flex justify-between mb-2">
                <div>Subtotal</div>
                <div className="font-medium">${totalPrice}</div>
              </div>
              <div className="flex justify-between mb-2 text-sm text-gray-500">
                <div>Taxes & Fees</div>
                <div>Included</div>
              </div>
              <div className="flex justify-between mt-4 text-lg font-bold">
                <div>Total</div>
                <div>${totalPrice}</div>
              </div>
            </div>
            
            {/* Action buttons */}
            <div className="mt-6">
              <button 
                onClick={onBookItinerary}
                className="w-full bg-blue-600 hover:bg-blue-700 text-white py-2 rounded-button whitespace-nowrap cursor-pointer mb-3"
              >
                <i className="fas fa-check-circle mr-2"></i>Book Itinerary
              </button>
              <button 
                onClick={onEmailItinerary}
                className="w-full bg-gray-100 hover:bg-gray-200 text-gray-700 py-2 rounded-button whitespace-nowrap cursor-pointer"
              >
                <i className="fas fa-envelope mr-2"></i>Email Itinerary
              </button>
            </div>
          </>
        )}
      </div>
    </div>
  );
};

export default TripSummary;
