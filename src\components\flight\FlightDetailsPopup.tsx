import React, { useState, useEffect, useRef, useCallback, useMemo } from 'react';
import type {
  FlightDetailsEmitData,
  TripJackApiDataInfo,
  TransformedLegacyFormat,
  LoadingStateWithCache,
  CacheMetadata
} from '../../models/flight/flight-details.model';
import {
  getEnhancedComprehensiveFlightDetails,
  createFlightDetailsLoadingManager,
  flightDetailsCacheUtils,
  flightDetailsPerformance
} from '../../api/api/flight-api.service';
import { transformTripJackToLegacyFormat, extractBaggageFromTripJackResponse, extractFareRulesFromTripJackResponse, createFallbackFareSummary, formatCurrencyAmount, formatFlightTime, formatFlightDuration, handleIncompleteTripJackData, createRetryHandler, calculateSegmentDuration } from '../../helper/flight/flight-details-helper';
import { TripJackResponseAdapter } from '../../helper/flight/tripjack-response-adapter';
import FlightDetailsShimmer from '../shimmer/FlightDetailsShimmer';

interface FlightDetailsPopupProps {
  isMobile: boolean;
  currency: string;
  flightDetailsBody: FlightDetailsEmitData;
  isOpen: boolean;
  onClose: () => void;
  onSelectFlight?: (flightData: any) => void;
}

// Use the enhanced loading state from models
interface EnhancedLoadingState extends LoadingStateWithCache {
  performanceMetrics?: {
    fetchTime: number;
    cacheEfficiency: number;
    apiCallCount: number;
  };
}

// Performance optimization: Cache for transformed data
const transformationCache = new Map<string, TransformedLegacyFormat>();
const CACHE_EXPIRY_TIME = 5 * 60 * 1000; // 5 minutes

const FlightDetailsPopup: React.FC<FlightDetailsPopupProps> = ({
  isMobile,
  currency,
  flightDetailsBody,
  isOpen,
  onClose,
  onSelectFlight
}) => {
  const [flightData, setFlightData] = useState<TransformedLegacyFormat | null>(null);
  const [rawData, setRawData] = useState<TripJackApiDataInfo | null>(null);
  const [loadingState, setLoadingState] = useState<EnhancedLoadingState>({
    isLoading: false,
    error: null,
    loadingStage: 'initial',
    retryCount: 0,
    cacheStatus: undefined,
    performanceMetrics: undefined
  });
  const modalRef = useRef<HTMLDivElement>(null);
  const abortControllerRef = useRef<AbortController | null>(null);
  const isMountedRef = useRef(true);

  // Create enhanced loading manager
  const loadingManager = useMemo(() => createFlightDetailsLoadingManager(), []);

  // Create retry handler with exponential backoff
  const retryHandler = useMemo(() => createRetryHandler(3, 1000), []);

  // Memoized cache key for transformation
  const cacheKey = useMemo(() => {
    if (!flightDetailsBody) return '';
    return JSON.stringify({
      tripType: flightDetailsBody.TripType,
      trips: flightDetailsBody.Trips.map(tripGroup =>
        tripGroup.map(trip => ({
          tui: trip.TUI,
          index: trip.Index,
          orderId: trip.OrderID
        }))
      )
    });
  }, [flightDetailsBody]);

  // Component mount/unmount cleanup
  useEffect(() => {
    isMountedRef.current = true;

    return () => {
      isMountedRef.current = false;
      // Cancel any pending requests
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
      }
    };
  }, []);



  // Handle click outside to close
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (modalRef.current && !modalRef.current.contains(event.target as Node)) {
        onClose();
      }
    };

    if (isOpen) {
      document.addEventListener('mousedown', handleClickOutside);
      document.body.style.overflow = 'hidden';
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
      document.body.style.overflow = 'unset';
    };
  }, [isOpen, onClose]);

  // Enhanced fetch function with cache-aware loading states
  const fetchFlightDetails = useCallback(async () => {
    const fetchStartTime = Date.now();

    // Check local transformation cache first
    if (cacheKey && transformationCache.has(cacheKey)) {
      const cachedData = transformationCache.get(cacheKey);
      if (cachedData) {
        setFlightData(cachedData);
        setLoadingState({
          isLoading: false,
          error: null,
          loadingStage: 'complete',
          retryCount: 0,
          cacheStatus: {
            hit: true,
            source: 'memory',
            freshness: 'fresh'
          },
          performanceMetrics: {
            fetchTime: 0,
            cacheEfficiency: 1.0,
            apiCallCount: 0
          }
        });
        return;
      }
    }

    // Cancel any existing request
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
    }

    // Create new abort controller
    abortControllerRef.current = new AbortController();

    // Update loading state with cache awareness
    setLoadingState(prev => ({
      ...prev,
      isLoading: true,
      error: null,
      loadingStage: 'fetching',
      cacheStatus: undefined,
      performanceMetrics: {
        fetchTime: 0,
        cacheEfficiency: 0,
        apiCallCount: 1
      }
    }));

    try {
      // Update loading stage
      setLoadingState(prev => ({
        ...prev,
        loadingStage: 'fetching'
      }));

      // Handle both one-way and round-trip flights with enhanced API
      const promises: Promise<TripJackApiDataInfo & { _cache: CacheMetadata }>[] = [];

      for (const tripGroup of flightDetailsBody.Trips) {
        for (const trip of tripGroup) {
          const requestBody = {
            ClientID: '',
            TripType: flightDetailsBody.TripType,
            Trips: [{
              TUI: trip.TUI,
              Amount: trip.Amount,
              Index: trip.Index,
              OrderID: trip.OrderID,
              ChannelCode: trip.ChannelCode
            }]
          };

          // Use enhanced comprehensive flight details with cache optimization
          promises.push(getEnhancedComprehensiveFlightDetails(requestBody, {
            cacheConfig: {
              ttl: 5 * 60 * 1000, // 5 minutes
              maxAge: 15 * 60 * 1000, // 15 minutes
              staleWhileRevalidate: true,
              forceRefresh: false
            },
            includeSSR: true,
            includeFareRules: true,
            useTripJackFormat: true,
            parallelFetch: true
          }));
        }
      }

      if (!isMountedRef.current) return;

      setLoadingState(prev => ({ ...prev, loadingStage: 'processing' }));

      const responses = await Promise.allSettled(promises);

      // Handle partial failures gracefully with cache metadata tracking
      const successfulResponses = responses
        .filter((result): result is PromiseFulfilledResult<TripJackApiDataInfo & { _cache: CacheMetadata }> =>
          result.status === 'fulfilled'
        )
        .map(result => result.value);

      if (successfulResponses.length === 0) {
        throw new Error('All flight detail requests failed');
      }

      if (!isMountedRef.current) return;

      // Use the first successful response and extract cache metadata
      const primaryResponse = successfulResponses[0];
      const cacheMetadata = primaryResponse._cache;

      // Calculate performance metrics
      const fetchTime = Date.now() - fetchStartTime;
      const cacheEfficiency = successfulResponses.filter(r => r._cache.hit).length / successfulResponses.length;

      setRawData(primaryResponse);

      // Track performance metrics
      flightDetailsPerformance.trackMetrics(cacheMetadata, 'comprehensive-flight-details');

      // Check data completeness and handle incomplete data
      const dataCheck = handleIncompleteTripJackData(primaryResponse);

      let transformedData: TransformedLegacyFormat;

      if (dataCheck.canProceed) {
        try {
          // Transform the data with memoization
          transformedData = transformTripJackToLegacyFormat(primaryResponse);

          // Add warnings if data is incomplete
          if (!dataCheck.isComplete && transformedData.metadata) {
            transformedData.metadata.warnings = [
              ...transformedData.metadata.warnings,
              ...dataCheck.missingFields.map(field => `Missing: ${field}`)
            ];
          }
        } catch (transformError) {
          console.warn('Error transforming data, using fallback:', transformError);

          // Use fallback data if transformation fails
          if (dataCheck.fallbackData) {
            transformedData = {
              ...dataCheck.fallbackData,
              metadata: {
                transformedAt: new Date().toISOString(),
                sourceDataValid: false,
                warnings: ['Data transformation failed, using fallback data', ...dataCheck.missingFields],
                segmentCount: dataCheck.fallbackData.flightSegments?.length || 0,
                totalPassengers: 1
              }
            } as TransformedLegacyFormat;
          } else {
            throw transformError;
          }
        }
      } else {
        throw new Error(`Incomplete flight data: ${dataCheck.missingFields.join(', ')}`);
      }

      // Cache the transformed data
      if (cacheKey) {
        transformationCache.set(cacheKey, transformedData);
        // Clean up old cache entries
        setTimeout(() => {
          transformationCache.delete(cacheKey);
        }, CACHE_EXPIRY_TIME);
      }

      setFlightData(transformedData);

      // Update loading state with comprehensive cache and performance information
      setLoadingState({
        isLoading: false,
        error: null,
        loadingStage: 'complete',
        retryCount: 0,
        cacheStatus: {
          hit: cacheMetadata.hit,
          source: cacheMetadata.source,
          freshness: cacheMetadata.dataFreshness
        },
        performanceMetrics: {
          fetchTime,
          cacheEfficiency,
          apiCallCount: successfulResponses.length
        }
      });

    } catch (error) {
      if (!isMountedRef.current) return;

      // Don't show error if request was aborted
      if (error instanceof Error && error.name === 'AbortError') {
        return;
      }

      console.error('Error fetching flight details:', error);

      // Try to get stale cache data as fallback
      const cacheStats = flightDetailsCacheUtils.getStats();
      if (cacheStats.staleEntries > 0) {
        console.warn('⚠️ Attempting to use stale cache data due to API error');
      }

      // Enhanced error handling with user-friendly messages
      let errorMessage = 'Failed to fetch flight details';
      let canRetry = true;

      if (error instanceof Error) {
        if (error.message.includes('network') || error.message.includes('fetch')) {
          errorMessage = 'Network error. Please check your connection and try again.';
        } else if (error.message.includes('timeout')) {
          errorMessage = 'Request timed out. Please try again.';
        } else if (error.message.includes('Incomplete flight data')) {
          errorMessage = 'Flight data is incomplete. Some information may not be available.';
          canRetry = false;
        } else {
          errorMessage = error.message;
        }
      }

      setLoadingState(prev => ({
        isLoading: false,
        error: errorMessage,
        loadingStage: 'initial',
        retryCount: prev.retryCount + 1,
        cacheStatus: {
          hit: false,
          source: 'error',
          freshness: 'expired'
        },
        performanceMetrics: {
          fetchTime: Date.now() - fetchStartTime,
          cacheEfficiency: 0,
          apiCallCount: 1
        }
      }));

      // Auto-retry for certain types of errors (up to 2 times)
      if (canRetry && loadingState.retryCount < 2 &&
          (errorMessage.includes('Network error') || errorMessage.includes('timeout'))) {
        setTimeout(() => {
          if (isMountedRef.current) {
            console.log(`Auto-retrying flight details fetch (attempt ${loadingState.retryCount + 1})`);
            fetchFlightDetails();
          }
        }, 2000 * (loadingState.retryCount + 1)); // Exponential backoff
      }
    }
  }, [flightDetailsBody, cacheKey]);

  // Fetch flight details when popup opens (moved here after fetchFlightDetails is defined)
  useEffect(() => {
    if (isOpen && flightDetailsBody) {
      fetchFlightDetails();
    }

    // Cleanup when popup closes
    return () => {
      if (!isOpen && abortControllerRef.current) {
        abortControllerRef.current.abort();
      }
    };
  }, [isOpen, fetchFlightDetails]);

  // Memoized handlers to prevent unnecessary re-renders
  const handleSelectFlight = useCallback(() => {
    if (onSelectFlight && flightData) {
      onSelectFlight({
        flightData,
        rawData,
        flightDetailsBody
      });
    }
    onClose();
  }, [onSelectFlight, flightData, rawData, flightDetailsBody, onClose]);

  const handleRetry = useCallback(async () => {
    // Reset retry count and try again
    setLoadingState(prev => ({
      ...prev,
      retryCount: 0
    }));

    try {
      await retryHandler(fetchFlightDetails);
    } catch (error) {
      console.error('Retry failed:', error);
      // Error is already handled in fetchFlightDetails
    }
  }, [fetchFlightDetails, retryHandler]);

  // Memoized modal header content
  const modalHeaderContent = useMemo(() => (
    <div className="flex items-center">
      <i className="fas fa-plane text-2xl primary-text mr-4"></i>
      <div>
        <h3 id="flight-details-title" className="text-xl font-semibold text-primary">
          Flight Details
        </h3>
        <p className="text-sm text-secondary">
          {flightDetailsBody.TripType === 'RT' ? 'Round Trip' : 'One Way'} •
          {flightDetailsBody.Trips.length} {flightDetailsBody.Trips.length === 1 ? 'Trip' : 'Trips'}
        </p>
      </div>
    </div>
  ), [flightDetailsBody.TripType, flightDetailsBody.Trips.length]);

  // Memoized error state component
  const errorStateComponent = useMemo(() => {
    if (!loadingState.error) return null;

    return (
      <ErrorState
        error={loadingState.error}
        onRetry={handleRetry}
        retryCount={loadingState.retryCount}
      />
    );
  }, [loadingState.error, handleRetry, loadingState.retryCount]);

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center p-4">
      <div
        className="absolute inset-0 bg-black bg-opacity-50"
        onClick={onClose}
        aria-hidden="true"
      ></div>
      
      <div
        ref={modalRef}
        className={`relative flight-card-background w-full ${
          isMobile ? 'max-w-sm max-h-[95vh]' : 'max-w-4xl max-h-[90vh]'
        } overflow-y-auto rounded-2xl shadow-2xl border flight-card-border`}
        role="dialog"
        aria-modal="true"
        aria-labelledby="flight-details-title"
      >
        {/* Modal Header */}
        <div className="sticky top-0 flight-card-background px-6 py-4 border-b border-primary flex items-center justify-between z-10">
          {modalHeaderContent}
          <div className="flex items-center space-x-3">
            {/* Cache Status Indicator */}
            {loadingState.cacheStatus && (
              <div className={`px-2 py-1 rounded-full text-xs font-medium ${
                loadingState.cacheStatus.hit
                  ? 'bg-green-100 text-green-700'
                  : 'bg-orange-100 text-orange-700'
              }`}>
                <i className={`fas ${
                  loadingState.cacheStatus.hit ? 'fa-bolt' : 'fa-cloud-download-alt'
                } mr-1`}></i>
                {loadingState.cacheStatus.hit ? 'Cached' : 'Live'}
              </div>
            )}
            {/* Performance Indicator */}
            {loadingState.performanceMetrics && (
              <div className="text-xs text-gray-500">
                {loadingState.performanceMetrics.fetchTime}ms
              </div>
            )}
            <button
              className="!rounded-button text-tertiary hover:text-secondary p-2 hover-neutral-background-50 rounded-lg transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-primary-color focus:ring-offset-1"
              onClick={onClose}
              aria-label="Close flight details"
            >
              <i className="fas fa-times text-xl"></i>
            </button>
          </div>
        </div>

        {/* Modal Content with enhanced loading state handling */}
        <div className="p-6">
          {loadingState.isLoading ? (
            <div className="space-y-4">
              <FlightDetailsShimmer tripType={flightDetailsBody.TripType} />
              {/* Loading timeout indicator */}
              {loadingState.loadingStage === 'fetching' && (
                <div className="text-center">
                  <p className="text-sm text-gray-500">
                    <i className="fas fa-spinner fa-spin mr-2"></i>
                    Fetching flight details...
                  </p>
                </div>
              )}
              {loadingState.loadingStage === 'processing' && (
                <div className="text-center">
                  <p className="text-sm text-gray-500">
                    <i className="fas fa-cog fa-spin mr-2"></i>
                    Processing flight information...
                  </p>
                </div>
              )}
            </div>
          ) : loadingState.error ? (
            errorStateComponent
          ) : flightData ? (
            <FlightDetailsContent
              flightData={flightData}
              rawData={rawData}
              currency={currency}
              tripType={flightDetailsBody.TripType}
              isMobile={isMobile}
            />
          ) : (
            <div className="text-center py-8 space-y-4">
              <div className="text-gray-400">
                <i className="fas fa-plane-slash text-4xl mb-4"></i>
                <h4 className="text-lg font-semibold text-gray-600">No Flight Details Available</h4>
                <p className="text-sm text-gray-500 mt-2">
                  Unable to load flight information at this time.
                </p>
              </div>
              <button
                onClick={handleRetry}
                className="!rounded-button bg-blue-600 text-white px-6 py-2 font-medium hover:bg-blue-700 transition-colors cursor-pointer"
              >
                <i className="fas fa-refresh mr-2"></i>
                Try Loading Again
              </button>
            </div>
          )}
        </div>

        {/* Modal Footer */}
        {!loadingState.isLoading && !loadingState.error && flightData && (
          <div className="sticky bottom-0 flight-card-background px-6 py-4 border-t border-primary flex justify-end space-x-4">
            <button
              className="!rounded-button bg-gray-100 text-gray-700 px-6 py-3 font-medium hover:bg-gray-200 transition-colors cursor-pointer whitespace-nowrap"
              onClick={onClose}
            >
              Close
            </button>
            {onSelectFlight && (
              <button 
                className="!rounded-button bg-blue-600 text-white px-8 py-3 font-medium hover:bg-blue-700 transition-colors cursor-pointer whitespace-nowrap"
                onClick={handleSelectFlight}
              >
                Select Flight
              </button>
            )}
          </div>
        )}
      </div>
    </div>
  );
};

// Enhanced Error State Component with retry count
const ErrorState: React.FC<{
  error: string;
  onRetry: () => void;
  retryCount?: number;
}> = React.memo(({ error, onRetry, retryCount = 0 }) => (
  <div className="text-center py-8 space-y-4">
    <div className="text-red-500">
      <i className="fas fa-exclamation-triangle text-4xl mb-4"></i>
      <h4 className="text-lg font-semibold">Error Loading Flight Details</h4>
      <p className="text-sm text-secondary mt-2">{error}</p>
      {retryCount > 0 && (
        <p className="text-xs text-gray-500 mt-1">
          Retry attempt: {retryCount}
        </p>
      )}
    </div>
    <div className="space-x-3">
      <button
        onClick={onRetry}
        className="!rounded-button bg-blue-600 text-white px-6 py-2 font-medium hover:bg-blue-700 transition-colors cursor-pointer"
        disabled={retryCount >= 3}
      >
        {retryCount >= 3 ? 'Max Retries Reached' : 'Try Again'}
      </button>
      {retryCount > 0 && (
        <button
          onClick={() => window.location.reload()}
          className="!rounded-button bg-gray-600 text-white px-6 py-2 font-medium hover:bg-gray-700 transition-colors cursor-pointer"
        >
          Refresh Page
        </button>
      )}
    </div>
  </div>
));

// Memoized Flight Details Content Component for performance
const FlightDetailsContent: React.FC<{
  flightData: TransformedLegacyFormat;
  rawData: TripJackApiDataInfo | null;
  currency: string;
  tripType: 'ON' | 'RT';
  isMobile: boolean;
}> = React.memo(({ flightData, rawData, currency, tripType, isMobile }) => {
  return (
    <div className="space-y-6">
      {/* Flight Segments Section */}
      <FlightSegmentsSection 
        segments={flightData.flightSegments}
        tripType={tripType}
        isMobile={isMobile}
      />

      {/* Fare Summary Section */}
      <FareSummarySection 
        fareBreakdown={flightData.fareBreakdown}
        currency={currency}
        isMobile={isMobile}
      />

      {/* Baggage Information Section */}
      <BaggageInfoSection 
        baggageInfo={flightData.baggageInfo}
        currency={currency}
        isMobile={isMobile}
      />

      {/* Fare Rules Section */}
      <FareRulesSection 
        fareRules={flightData.fareRules}
        isMobile={isMobile}
      />
    </div>
  );
});

// Memoized Flight Segments Section Component
const FlightSegmentsSection: React.FC<{
  segments: TransformedLegacyFormat['flightSegments'];
  tripType: 'ON' | 'RT';
  isMobile: boolean;
}> = React.memo(({ segments, tripType, isMobile }) => {
  return (
    <div className="space-y-4">
      <h4 className="text-lg font-semibold text-gray-900 flex items-center">
        <i className="fas fa-route text-blue-600 mr-3"></i>
        Flight Segments
      </h4>

      <div className="space-y-4">
        {segments.map((segment, index) => (
          <div key={index} className="border border-gray-200 rounded-xl p-4 space-y-4">
            {/* Airline and Flight Number */}
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-3">
                <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                  <i className="fas fa-plane text-blue-600 text-sm"></i>
                </div>
                <div>
                  <h5 className="font-semibold text-gray-900">{segment.airline}</h5>
                  <p className="text-sm text-gray-600">{segment.flightNumber}</p>
                </div>
              </div>
              <div className="text-right">
                <p className="text-sm font-medium text-gray-900">{segment.aircraft}</p>
                <p className="text-xs text-gray-600">{segment.cabin}</p>
              </div>
            </div>

            {/* Flight Route */}
            <div className="flex items-center justify-between">
              <div className="text-center">
                <p className="text-2xl font-bold text-gray-900">
                  {formatFlightTime(segment.departure.time)}
                </p>
                <p className="text-sm font-medium text-gray-900">{segment.departure.code}</p>
                <p className="text-xs text-gray-600">{segment.departure.name}</p>
                {segment.departure.terminal && (
                  <p className="text-xs text-gray-500">Terminal {segment.departure.terminal}</p>
                )}
              </div>

              <div className="flex-1 flex items-center justify-center space-x-2 px-4">
                <div className="text-center">
                  <p className="text-sm text-gray-600">{segment.duration}</p>
                  <div className="flex items-center space-x-1">
                    <div className="w-2 h-2 bg-blue-600 rounded-full"></div>
                    <div className="flex-1 h-0.5 bg-blue-600"></div>
                    <i className="fas fa-plane text-blue-600 text-sm"></i>
                    <div className="flex-1 h-0.5 bg-blue-600"></div>
                    <div className="w-2 h-2 bg-blue-600 rounded-full"></div>
                  </div>
                  <p className="text-xs text-gray-500">Non-stop</p>
                </div>
              </div>

              <div className="text-center">
                <p className="text-2xl font-bold text-gray-900">
                  {formatFlightTime(segment.arrival.time)}
                </p>
                <p className="text-sm font-medium text-gray-900">{segment.arrival.code}</p>
                <p className="text-xs text-gray-600">{segment.arrival.name}</p>
                {segment.arrival.terminal && (
                  <p className="text-xs text-gray-500">Terminal {segment.arrival.terminal}</p>
                )}
              </div>
            </div>

            {/* Additional Details */}
            <div className="grid grid-cols-2 md:grid-cols-3 gap-4 pt-2 border-t border-gray-100">
              <div>
                <p className="text-xs text-gray-500">Fare Basis</p>
                <p className="text-sm font-medium text-gray-900">{segment.fareBasis || 'N/A'}</p>
              </div>
              <div>
                <p className="text-xs text-gray-500">Baggage</p>
                <p className="text-sm font-medium text-gray-900">{segment.baggageAllowance || 'Included'}</p>
              </div>
              <div className="col-span-2 md:col-span-1">
                <p className="text-xs text-gray-500">Class</p>
                <p className="text-sm font-medium text-gray-900">{segment.cabin}</p>
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
});

// Memoized Fare Summary Section Component
const FareSummarySection: React.FC<{
  fareBreakdown: TransformedLegacyFormat['fareBreakdown'];
  currency: string;
  isMobile: boolean;
}> = React.memo(({ fareBreakdown, currency, isMobile }) => {
  return (
    <div className="space-y-4">
      <h4 className="text-lg font-semibold text-gray-900 flex items-center">
        <i className="fas fa-receipt text-green-600 mr-3"></i>
        Fare Summary
      </h4>

      <div className="border border-gray-200 rounded-xl p-4 space-y-3">
        {/* Passenger Count with proper field access */}
        <div className="flex justify-between items-center text-sm">
          <span className="text-gray-600">
            {fareBreakdown.passengers.ADULT || fareBreakdown.passengers.adults || 1} Adult{(fareBreakdown.passengers.ADULT || fareBreakdown.passengers.adults || 1) > 1 ? 's' : ''}
            {(fareBreakdown.passengers.CHILD || fareBreakdown.passengers.children || 0) > 0 && `, ${fareBreakdown.passengers.CHILD || fareBreakdown.passengers.children} Child${(fareBreakdown.passengers.CHILD || fareBreakdown.passengers.children) > 1 ? 'ren' : ''}`}
            {(fareBreakdown.passengers.INFANT || fareBreakdown.passengers.infants || 0) > 0 && `, ${fareBreakdown.passengers.INFANT || fareBreakdown.passengers.infants} Infant${(fareBreakdown.passengers.INFANT || fareBreakdown.passengers.infants) > 1 ? 's' : ''}`}
          </span>
        </div>

        {/* Fare Breakdown with enhanced display */}
        <div className="space-y-2">
          <div className="flex justify-between items-center">
            <span className="text-gray-600">Base Fare</span>
            <span className="font-medium text-gray-900">
              {fareBreakdown.formattedAmounts?.baseFare || formatCurrencyAmount(fareBreakdown.baseFare, currency)}
            </span>
          </div>
          <div className="flex justify-between items-center">
            <span className="text-gray-600">Taxes & Fees</span>
            <span className="font-medium text-gray-900">
              {fareBreakdown.formattedAmounts?.taxes || formatCurrencyAmount(fareBreakdown.taxes, currency)}
            </span>
          </div>

          {/* Per-passenger breakdown if available */}
          {fareBreakdown.perPassengerBreakdown && (
            <div className="mt-3 pt-2 border-t border-gray-100">
              <div className="text-xs text-gray-500 mb-2">Per Passenger Breakdown:</div>
              <div className="space-y-1 text-sm">
                <div className="flex justify-between">
                  <span className="text-gray-600">Base Fare (per person)</span>
                  <span className="text-gray-700">
                    {formatCurrencyAmount(fareBreakdown.perPassengerBreakdown.baseFarePerPerson, currency)}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Taxes (per person)</span>
                  <span className="text-gray-700">
                    {formatCurrencyAmount(fareBreakdown.perPassengerBreakdown.taxesPerPerson, currency)}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Total (per person)</span>
                  <span className="font-medium text-gray-800">
                    {formatCurrencyAmount(fareBreakdown.perPassengerBreakdown.totalPerPerson, currency)}
                  </span>
                </div>
              </div>
            </div>
          )}
        </div>

        {/* Total with enhanced display */}
        <div className="border-t border-gray-200 pt-3">
          <div className="flex justify-between items-center">
            <span className="text-lg font-semibold text-gray-900">Total Amount</span>
            <span className="text-lg font-bold text-blue-600">
              {fareBreakdown.formattedAmounts?.totalAmount || formatCurrencyAmount(fareBreakdown.totalAmount, currency)}
            </span>
          </div>

          {/* Show total passengers count */}
          {fareBreakdown.metadata?.totalPassengers && fareBreakdown.metadata.totalPassengers > 1 && (
            <div className="flex justify-between items-center mt-1">
              <span className="text-sm text-gray-500">
                For {fareBreakdown.metadata.totalPassengers} passenger{fareBreakdown.metadata.totalPassengers > 1 ? 's' : ''}
              </span>
            </div>
          )}
        </div>
      </div>
    </div>
  );
});

// Memoized Baggage Information Section Component
const BaggageInfoSection: React.FC<{
  baggageInfo: TransformedLegacyFormat['baggageInfo'];
  currency: string;
  isMobile: boolean;
}> = React.memo(({ baggageInfo, currency, isMobile }) => {
  return (
    <div className="space-y-4">
      <h4 className="text-lg font-semibold text-gray-900 flex items-center">
        <i className="fas fa-suitcase text-purple-600 mr-3"></i>
        Baggage Information
      </h4>

      <div className="border border-gray-200 rounded-xl p-4 space-y-4">
        {/* Included Baggage with validation */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="bg-gray-50 rounded-lg p-3">
            <div className="flex items-center mb-2">
              <i className="fas fa-briefcase text-blue-600 mr-2"></i>
              <span className="font-medium text-gray-900">Cabin Baggage</span>
            </div>
            <p className="text-lg font-semibold text-gray-900">
              {baggageInfo.cabin || 'Not specified'}
            </p>
            {!baggageInfo.isValidFormat && (
              <p className="text-xs text-orange-600 mt-1">
                <i className="fas fa-exclamation-triangle mr-1"></i>
                Please verify with airline
              </p>
            )}
          </div>
          <div className="bg-gray-50 rounded-lg p-3">
            <div className="flex items-center mb-2">
              <i className="fas fa-suitcase text-green-600 mr-2"></i>
              <span className="font-medium text-gray-900">Checked Baggage</span>
            </div>
            <p className="text-lg font-semibold text-gray-900">
              {baggageInfo.checked || 'Not specified'}
            </p>
            {!baggageInfo.isValidFormat && (
              <p className="text-xs text-orange-600 mt-1">
                <i className="fas fa-exclamation-triangle mr-1"></i>
                Please verify with airline
              </p>
            )}
          </div>
        </div>

        {/* Additional Services with tabs for better organization */}
        {((baggageInfo.additionalServices && baggageInfo.additionalServices.length > 0) ||
          (baggageInfo.mealServices && baggageInfo.mealServices.length > 0) ||
          (baggageInfo.seatServices && baggageInfo.seatServices.length > 0)) && (
          <div className="space-y-3">
            <h5 className="font-medium text-gray-900">Additional Services</h5>

            {/* Baggage Services */}
            {baggageInfo.additionalServices && baggageInfo.additionalServices.length > 0 && (
              <div className="space-y-2">
                <h6 className="text-sm font-medium text-gray-700 flex items-center">
                  <i className="fas fa-plus-circle text-blue-600 mr-2"></i>
                  Extra Baggage
                </h6>
                <div className="space-y-2">
                  {baggageInfo.additionalServices.map((service, index) => (
                    <div key={`baggage-${index}`} className="flex justify-between items-center py-2 px-3 bg-blue-50 rounded-lg">
                      <div>
                        <p className="font-medium text-gray-900">{service.description}</p>
                        <p className="text-sm text-gray-600">Code: {service.code}</p>
                      </div>
                      <p className="font-semibold text-blue-700">
                        {formatCurrencyAmount(parseFloat(service.amount || '0'), service.currency)}
                      </p>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* Meal Services */}
            {baggageInfo.mealServices && baggageInfo.mealServices.length > 0 && (
              <div className="space-y-2">
                <h6 className="text-sm font-medium text-gray-700 flex items-center">
                  <i className="fas fa-utensils text-green-600 mr-2"></i>
                  Meal Options
                </h6>
                <div className="space-y-2">
                  {baggageInfo.mealServices.map((service, index) => (
                    <div key={`meal-${index}`} className="flex justify-between items-center py-2 px-3 bg-green-50 rounded-lg">
                      <div>
                        <p className="font-medium text-gray-900">{service.description}</p>
                        <p className="text-sm text-gray-600">Code: {service.code}</p>
                      </div>
                      <p className="font-semibold text-green-700">
                        {formatCurrencyAmount(parseFloat(service.amount || '0'), service.currency)}
                      </p>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* Seat Services */}
            {baggageInfo.seatServices && baggageInfo.seatServices.length > 0 && (
              <div className="space-y-2">
                <h6 className="text-sm font-medium text-gray-700 flex items-center">
                  <i className="fas fa-chair text-purple-600 mr-2"></i>
                  Seat Selection
                </h6>
                <div className="space-y-2">
                  {baggageInfo.seatServices.map((service, index) => (
                    <div key={`seat-${index}`} className="flex justify-between items-center py-2 px-3 bg-purple-50 rounded-lg">
                      <div>
                        <p className="font-medium text-gray-900">{service.description}</p>
                        <p className="text-sm text-gray-600">Code: {service.code}</p>
                      </div>
                      <p className="font-semibold text-purple-700">
                        {formatCurrencyAmount(parseFloat(service.amount || '0'), service.currency)}
                      </p>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  );
});

// Memoized Fare Rules Section Component
const FareRulesSection: React.FC<{
  fareRules: TransformedLegacyFormat['fareRules'];
  isMobile: boolean;
}> = React.memo(({ fareRules, isMobile }) => {
  return (
    <div className="space-y-4">
      <h4 className="text-lg font-semibold text-gray-900 flex items-center">
        <i className="fas fa-file-contract text-orange-600 mr-3"></i>
        Fare Rules & Policies
      </h4>

      <div className="border border-gray-200 rounded-xl p-4 space-y-4">
        {fareRules && fareRules.length > 0 ? (
          fareRules.map((rule, index) => (
            <div key={index} className="space-y-2">
              <div className="flex items-start space-x-3">
                <div className="w-6 h-6 bg-orange-100 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                  <i className="fas fa-info text-orange-600 text-xs"></i>
                </div>
                <div className="flex-1">
                  <h5 className="font-medium text-gray-900 capitalize">
                    {rule.ruleType.replace(/([A-Z])/g, ' $1').trim()}
                  </h5>
                  <p className="text-sm text-gray-600 mt-1">{rule.description}</p>
                  {rule.penalty && (
                    <div className="mt-2 p-2 bg-red-50 rounded-lg">
                      <p className="text-sm text-red-700">
                        <i className="fas fa-exclamation-triangle mr-1"></i>
                        Penalty: {rule.penalty}
                      </p>
                    </div>
                  )}
                </div>
              </div>
              {index < fareRules.length - 1 && <div className="border-b border-gray-100"></div>}
            </div>
          ))
        ) : (
          <div className="text-center py-4">
            <i className="fas fa-info-circle text-gray-400 text-2xl mb-2"></i>
            <p className="text-gray-500">Fare rules information not available</p>
            <p className="text-sm text-gray-400 mt-1">
              Please contact customer service for detailed fare conditions
            </p>
          </div>
        )}
      </div>
    </div>
  );
});

export default FlightDetailsPopup;
