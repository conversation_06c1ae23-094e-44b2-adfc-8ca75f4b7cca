import React, { useState } from 'react';
import { ModifySearchForm, ModifySearchButton } from './index';

/**
 * Example component demonstrating how to use the ModifySearchForm and ModifySearchButton
 * This shows both modal and inline usage patterns
 */
const ModifySearchExample: React.FC = () => {
    const [showModal, setShowModal] = useState(false);

    const handleSearch = (searchData: any) => {
        console.log('Search data:', searchData);
        // Here you would typically:
        // 1. Update your application state
        // 2. Make API calls
        // 3. Navigate to search results
        // 4. Update URL parameters
        alert('Search updated! Check console for search data.');
    };

    return (
        <div className="p-6 space-y-6">
            <h1 className="text-2xl font-bold text-gray-800">Modify Search Examples</h1>
            
            {/* Example 1: Simple Button Trigger */}
            <div className="space-y-2">
                <h2 className="text-lg font-semibold text-gray-700">1. Simple Button Trigger</h2>
                <p className="text-gray-600">Click the button to open the modify search modal:</p>
                <ModifySearchButton 
                    onSearch={handleSearch}
                    className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg"
                />
            </div>

            {/* Example 2: Inline Search Summary */}
            <div className="space-y-2">
                <h2 className="text-lg font-semibold text-gray-700">2. Inline Search Summary</h2>
                <p className="text-gray-600">Shows current search with modify button:</p>
                <ModifySearchButton 
                    onSearch={handleSearch}
                    variant="inline"
                />
            </div>

            {/* Example 3: Manual Modal Control */}
            <div className="space-y-2">
                <h2 className="text-lg font-semibold text-gray-700">3. Manual Modal Control</h2>
                <p className="text-gray-600">Manually control when the modal opens:</p>
                <button
                    onClick={() => setShowModal(true)}
                    className="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg"
                >
                    Open Custom Modal
                </button>

                {showModal && (
                    <ModifySearchForm
                        onSearch={(data) => {
                            handleSearch(data);
                            setShowModal(false);
                        }}
                        onClose={() => setShowModal(false)}
                        isModal={true}
                    />
                )}
            </div>

            {/* Example 4: Inline Form (No Modal) */}
            <div className="space-y-2">
                <h2 className="text-lg font-semibold text-gray-700">4. Inline Form (No Modal)</h2>
                <p className="text-gray-600">Embed the form directly in your page:</p>
                <div className="border border-gray-200 rounded-lg p-4 bg-gray-50">
                    <ModifySearchForm
                        onSearch={handleSearch}
                        isModal={false}
                    />
                </div>
            </div>

            {/* Usage Instructions */}
            <div className="mt-8 p-4 bg-blue-50 border border-blue-200 rounded-lg">
                <h3 className="text-lg font-semibold text-blue-800 mb-2">Usage Instructions</h3>
                <div className="text-blue-700 space-y-2">
                    <p><strong>ModifySearchButton:</strong> Ready-to-use component with built-in modal</p>
                    <ul className="list-disc list-inside ml-4 space-y-1">
                        <li><code>variant="button"</code> - Simple button trigger</li>
                        <li><code>variant="inline"</code> - Shows search summary with modify button</li>
                        <li><code>onSearch</code> - Callback when search is modified</li>
                    </ul>
                    
                    <p><strong>ModifySearchForm:</strong> The core form component</p>
                    <ul className="list-disc list-inside ml-4 space-y-1">
                        <li><code>isModal={true}</code> - Renders as modal overlay</li>
                        <li><code>isModal={false}</code> - Renders inline</li>
                        <li><code>onSearch</code> - Required callback for search submission</li>
                        <li><code>onClose</code> - Optional callback for modal close</li>
                    </ul>

                    <p><strong>Features:</strong></p>
                    <ul className="list-disc list-inside ml-4 space-y-1">
                        <li>Reuses all FlightSearchForm components and functionality</li>
                        <li>Horizontal layout matching your design requirements</li>
                        <li>Responsive design with mobile-friendly layout</li>
                        <li>Professional styling with color-coded input sections</li>
                        <li>Integrates with FlightSearchContext for state management</li>
                    </ul>
                </div>
            </div>
        </div>
    );
};

export default ModifySearchExample;
