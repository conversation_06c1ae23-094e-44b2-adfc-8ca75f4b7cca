import React from 'react';
import AirlineIcon from './AirlineIcon';
import type { MultiCityFlight } from './types';

interface SelectedFlightSummaryProps {
  flight: MultiCityFlight;
  onRemove: () => void;
}

const SelectedFlightSummary: React.FC<SelectedFlightSummaryProps> = ({
  flight,
  onRemove
}) => {
  return (
    <div className="py-1.5 px-4 bg-white border-b border-gray-200">
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-3">
          <AirlineIcon code={flight.airline.code} size="sm" />
          <div className="flex items-center space-x-2">
            <span className="font-medium text-sm">
              {flight.departure.time}
            </span>
            <i className="fas fa-arrow-right text-xs text-gray-400"></i>
            <span className="font-medium text-sm">
              {flight.arrival.time}
            </span>
            <span className="text-xs text-gray-500 ml-2">
              ({flight.duration})
            </span>
          </div>
        </div>
        <div className="flex items-center space-x-4">
          <span className="text-sm text-gray-600">
            <i className="fas fa-suitcase mr-1"></i>
            {flight.fare.baggage}
          </span>
          <span className="font-semibold">
            ${flight.fare.price}
          </span>
          {/* <button
            onClick={(e) => {
              e.stopPropagation();
              onRemove();
            }}
            className="text-red-500 hover:text-red-700"
          >
            <i className="fas fa-times-circle"></i>
          </button> */}
        </div>
      </div>
    </div>
  );
};

export default SelectedFlightSummary;
