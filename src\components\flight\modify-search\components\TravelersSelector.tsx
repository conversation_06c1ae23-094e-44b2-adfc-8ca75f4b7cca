import React, { useState, useEffect } from 'react';

interface Passengers {
  adults: number;
  children: number;
  infants: number;
}

interface TravelersSelectorProps {
  passengers: Passengers;
  onPassengerChange: (type: 'adults' | 'children' | 'infants', action: 'increase' | 'decrease') => void;
  onApply: () => void;
}

const TravelersSelector: React.FC<TravelersSelectorProps> = ({
  passengers,
  onPassengerChange,
  onApply
}) => {
  const [localPassengers, setLocalPassengers] = useState(passengers);

  useEffect(() => {
    setLocalPassengers(passengers);
  }, [passengers]);

  const handlePassengerChange = (type: 'adults' | 'children' | 'infants', action: 'increase' | 'decrease') => {
    const newPassengers = { ...localPassengers };
    
    if (action === 'increase') {
      if (type === 'adults' && newPassengers.adults < 9) {
        newPassengers.adults++;
      } else if (type === 'children' && newPassengers.children < 8) {
        newPassengers.children++;
      } else if (type === 'infants' && newPassengers.infants < newPassengers.adults) {
        newPassengers.infants++;
      }
    } else if (action === 'decrease') {
      if (type === 'adults' && newPassengers.adults > 1) {
        newPassengers.adults--;
        // Adjust infants if they exceed adults
        if (newPassengers.infants > newPassengers.adults) {
          newPassengers.infants = newPassengers.adults;
        }
      } else if (type === 'children' && newPassengers.children > 0) {
        newPassengers.children--;
      } else if (type === 'infants' && newPassengers.infants > 0) {
        newPassengers.infants--;
      }
    }

    setLocalPassengers(newPassengers);
    onPassengerChange(type, action);
  };

  const renderCounter = (
    type: 'adults' | 'children' | 'infants',
    label: string,
    subtitle: string,
    count: number,
    minValue: number,
    maxValue: number
  ) => (
    <div className="flex items-center justify-between py-3">
      <div>
        <div className="text-sm font-medium text-gray-800">{label}</div>
        <div className="text-xs text-gray-500">{subtitle}</div>
      </div>
      <div className="flex items-center gap-3">
        <button
          type="button"
          onClick={() => handlePassengerChange(type, 'decrease')}
          disabled={count <= minValue}
          className="w-8 h-8 rounded-full border border-gray-300 flex items-center justify-center text-gray-600 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          <i className="fas fa-minus text-xs"></i>
        </button>
        <span className="w-8 text-center text-sm font-medium text-gray-800">{count}</span>
        <button
          type="button"
          onClick={() => handlePassengerChange(type, 'increase')}
          disabled={count >= maxValue}
          className="w-8 h-8 rounded-full border border-gray-300 flex items-center justify-center text-gray-600 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          <i className="fas fa-plus text-xs"></i>
        </button>
      </div>
    </div>
  );

  return (
    <div className="bg-white rounded-xl shadow-xl border border-gray-200 p-4 min-w-72">
      <div className="mb-4">
        <h4 className="text-sm font-semibold text-gray-800 mb-3">Travelers</h4>
        
        <div className="space-y-1">
          {renderCounter('adults', 'Adults', '12 yrs or above', localPassengers.adults, 1, 9)}
          <div className="border-t border-gray-100"></div>
          {renderCounter('children', 'Children', '2 - 12 Years', localPassengers.children, 0, 8)}
          <div className="border-t border-gray-100"></div>
          {renderCounter('infants', 'Infants', 'Under 2 Years', localPassengers.infants, 0, localPassengers.adults)}
        </div>
      </div>

      <button
        type="button"
        className="w-full bg-blue-600 text-white py-2 px-4 rounded-lg hover:bg-blue-700 transition-colors text-sm font-medium"
        onClick={onApply}
      >
        Apply
      </button>
    </div>
  );
};

export default TravelersSelector;
