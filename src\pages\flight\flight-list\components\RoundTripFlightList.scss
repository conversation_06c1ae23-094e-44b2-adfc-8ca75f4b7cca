/* Professional Scrollable Flight List Styling */
@use '../../../../styles/variables' as *;

.domestic-flight-list {
  /* Custom scrollbar styling for consistency */
  .custom-scrollbar {
    /* Hide scrollbar by default, show on hover */
    scrollbar-width: none; /* Firefox */
    -ms-overflow-style: none; /* IE and Edge */

    &::-webkit-scrollbar {
      width: 0px;
      background: transparent;
    }

    /* Show minimal scrollbar on hover */
    &:hover {
      &::-webkit-scrollbar {
        width: 4px;
      }

      &::-webkit-scrollbar-track {
        background: transparent;
      }

      &::-webkit-scrollbar-thumb {
        background: rgba(0, 0, 0, 0.2);
        border-radius: 2px;

        &:hover {
          background: rgba(0, 0, 0, 0.3);
        }
      }

      /* Firefox */
      scrollbar-width: thin;
      scrollbar-color: rgba(0, 0, 0, 0.2) transparent;
    }

    /* Focus state for accessibility */
    &:focus {
      &::-webkit-scrollbar {
        width: 4px;
      }

      &::-webkit-scrollbar-thumb {
        background: $primary_color;
        opacity: 0.6;
      }

      scrollbar-width: thin;
      scrollbar-color: $primary_color transparent;
    }

    /* Smooth scrolling */
    scroll-behavior: smooth;
  }

  /* Main container styling */
  .flight-columns-container {
    display: flex;
    gap: 1rem;
    height: calc(100vh - 280px);
    min-height: 600px;
    
    @media (max-width: 1024px) {
      height: calc(100vh - 320px);
      min-height: 500px;
    }
    
    @media (max-width: 768px) {
      flex-direction: column;
      height: auto;
      min-height: auto;
      gap: 1.5rem;
    }
  }

  /* Individual flight column styling */
  .flight-column {
    flex: 1;
    display: flex;
    flex-direction: column;
    
    @media (max-width: 768px) {
      height: 400px;
    }
  }

  /* Fixed header styling */
  .flight-column-header {
    background: white;
    border-radius: 0.75rem;
    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
    padding: 1rem;
    margin-bottom: 1rem;
    flex-shrink: 0;
    border: 1px solid $neutral_200;
    
    .header-title-row {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 0.5rem;
      
      h2 {
        font-size: 1.125rem;
        font-weight: 700;
        color: $neutral_800;
        margin: 0;
      }
      
      .flight-count-indicator {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        
        .status-dot {
          width: 0.75rem;
          height: 0.75rem;
          border-radius: 50%;
          
          &.outbound {
            background-color: $primary_color;
          }
          
          &.return {
            background-color: $success_color;
          }
        }
        
        .count-text {
          font-size: 0.75rem;
          color: $neutral_500;
          font-weight: 500;
        }
      }
    }
    
    .route-description {
      font-size: 0.875rem;
      color: $neutral_600;
      margin-bottom: 0.75rem;
      font-weight: 400;
    }
    
    .progress-bar {
      height: 0.25rem;
      width: 100%;
      border-radius: 0.125rem;
      
      &.outbound {
        background-color: $primary_color;
      }
      
      &.return {
        background-color: $success_color;
      }
    }
  }

  /* Scrollable content area */
  .flight-list-scrollable {
    flex: 1;
    overflow-y: auto;
    padding-right: 0.25rem;
    margin-right: -0.25rem;

    /* Hide scrollbar by default, show on hover */
    scrollbar-width: none; /* Firefox */
    -ms-overflow-style: none; /* IE and Edge */

    &::-webkit-scrollbar {
      width: 0px;
      background: transparent;
    }

    /* Show minimal scrollbar on hover */
    &:hover {
      &::-webkit-scrollbar {
        width: 4px;
      }

      &::-webkit-scrollbar-track {
        background: transparent;
      }

      &::-webkit-scrollbar-thumb {
        background: rgba(0, 0, 0, 0.2);
        border-radius: 2px;

        &:hover {
          background: rgba(0, 0, 0, 0.3);
        }
      }

      /* Firefox */
      scrollbar-width: thin;
      scrollbar-color: rgba(0, 0, 0, 0.2) transparent;
    }

    /* Focus state for accessibility */
    &:focus {
      &::-webkit-scrollbar {
        width: 4px;
      }

      &::-webkit-scrollbar-thumb {
        background: $primary_color;
        opacity: 0.6;
      }

      scrollbar-width: thin;
      scrollbar-color: $primary_color transparent;
    }

    /* Smooth scrolling */
    scroll-behavior: smooth;

    /* Content padding */
    .flight-cards-container {
      padding-bottom: 1rem;
      padding-right: 0.5rem;

      /* Flight card spacing */
      > * + * {
        margin-top: 0.75rem;
      }
    }
  }

  /* Responsive adjustments */
  @media (max-width: 1400px) {
    .flight-columns-container {
      height: calc(100vh - 300px);
      min-height: 550px;
    }
  }

  @media (max-width: 1200px) {
    .flight-columns-container {
      gap: 0.75rem;
      height: calc(100vh - 320px);
      min-height: 500px;
    }

    .flight-column-header {
      padding: 0.875rem;

      .header-title-row h2 {
        font-size: 1rem;
      }

      .route-description {
        font-size: 0.8125rem;
      }

      .flight-count-indicator {
        .status-dot {
          width: 0.625rem;
          height: 0.625rem;
        }

        .count-text {
          font-size: 0.6875rem;
        }
      }
    }
  }

  @media (max-width: 1024px) {
    .flight-columns-container {
      height: calc(100vh - 340px);
      min-height: 450px;
    }
  }

  @media (max-width: 768px) {
    .flight-columns-container {
      flex-direction: column;
      height: auto;
      min-height: auto;
      gap: 1rem;
    }

    .flight-column {
      height: 350px;

      .flight-column-header {
        padding: 0.75rem;
        margin-bottom: 0.75rem;

        .header-title-row {
          margin-bottom: 0.375rem;

          h2 {
            font-size: 0.9375rem;
          }
        }

        .route-description {
          font-size: 0.75rem;
          margin-bottom: 0.5rem;
        }

        .progress-bar {
          height: 0.1875rem;
        }
      }

      .flight-list-scrollable {
        padding-right: 0.125rem;
        margin-right: -0.125rem;

        &:hover {
          &::-webkit-scrollbar {
            width: 3px;
          }

          &::-webkit-scrollbar-thumb {
            background: rgba(0, 0, 0, 0.15);
          }
        }

        &:focus {
          &::-webkit-scrollbar {
            width: 3px;
          }
        }

        .flight-cards-container {
          padding-bottom: 0.75rem;
          padding-right: 0.25rem;

          > * + * {
            margin-top: 0.5rem;
          }
        }
      }
    }
  }

  @media (max-width: 640px) {
    .flight-columns-container {
      gap: 0.75rem;
    }

    .flight-column {
      height: 320px;

      .flight-column-header {
        padding: 0.625rem;

        .header-title-row {
          flex-direction: column;
          align-items: flex-start;
          gap: 0.25rem;

          .flight-count-indicator {
            align-self: flex-end;
          }
        }
      }
    }
  }

  @media (max-width: 480px) {
    .flight-column {
      height: 300px;

      .flight-column-header {
        .header-title-row {
          h2 {
            font-size: 0.875rem;
          }
        }

        .route-description {
          font-size: 0.6875rem;
        }
      }

      .flight-list-scrollable {
        &:hover {
          &::-webkit-scrollbar {
            width: 2px;
          }

          &::-webkit-scrollbar-thumb {
            background: rgba(0, 0, 0, 0.1);
          }
        }

        &:focus {
          &::-webkit-scrollbar {
            width: 2px;
          }
        }
      }
    }
  }

  /* Loading and empty states */
  .flight-list-empty {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 200px;
    color: $neutral_500;
    
    .empty-icon {
      font-size: 2rem;
      margin-bottom: 0.5rem;
      opacity: 0.5;
    }
    
    .empty-text {
      font-size: 0.875rem;
      text-align: center;
    }
  }

  /* Selection indicators */
  .flight-column.has-selection {
    .flight-column-header {
      border-color: $primary_color;
      box-shadow: 0 0 0 1px $primary_color;
    }
  }

  /* Accessibility improvements */
  .flight-list-scrollable {
    &:focus {
      outline: 2px solid $primary_color;
      outline-offset: 2px;
      border-radius: 0.375rem;
    }

    &:focus-visible {
      outline: 2px solid $primary_color;
      outline-offset: 2px;
    }
  }

  /* Keyboard navigation support */
  .flight-column {
    &:focus-within {
      .flight-column-header {
        border-color: $primary_color;
        box-shadow: 0 0 0 1px $primary_color;
      }
    }
  }

  /* Enhanced visual feedback */
  .flight-list-scrollable {
    transition: all 0.2s ease-in-out;
    position: relative;

    /* Subtle scroll indicators instead of visible scrollbars */
    &.scrolled {
      &::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 2px;
        background: linear-gradient(90deg, transparent, rgba(0, 0, 0, 0.05), transparent);
        z-index: 1;
        pointer-events: none;
      }
    }

    /* Bottom fade indicator for more content */
    &::after {
      content: '';
      position: absolute;
      bottom: 0;
      left: 0;
      right: 0;
      height: 16px;
      background: linear-gradient(to top, rgba(249, 250, 251, 0.9), transparent);
      pointer-events: none;
      opacity: 0;
      transition: opacity 0.3s ease-in-out;
      z-index: 1;
    }

    &.scrolled::after {
      opacity: 1;
    }

    /* Hover state shows content is scrollable */
    &:hover {
      &::after {
        opacity: 0.7;
      }
    }
  }

  /* Smooth scroll behavior for better UX */
  .flight-cards-container {
    scroll-margin-top: 1rem;

    > * {
      scroll-margin: 0.5rem;
    }
  }

  /* Loading state styling */
  .flight-list-loading {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100px;

    .loading-spinner {
      width: 2rem;
      height: 2rem;
      border: 2px solid $neutral_200;
      border-top: 2px solid $primary_color;
      border-radius: 50%;
      animation: spin 1s linear infinite;
    }

    .loading-text {
      margin-left: 0.75rem;
      color: $neutral_600;
      font-size: 0.875rem;
    }
  }

  @keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }
}

/* Animation for smooth transitions */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.domestic-flight-list .flight-column {
  animation: fadeInUp 0.3s ease-out;
}
