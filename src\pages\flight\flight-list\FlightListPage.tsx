import React, { useState, useEffect, useCallback, useRef } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import { PageWrapper } from '../../../components/layout';
import { useFlightSearch, FlightSearchProvider } from '../../../contexts/FlightSearchContext';
import type { FlightSearchData } from '../../../contexts/FlightSearchContext';
import { unifiedFlightSearch, pollForCompleteData } from '../../../api/unified-flight-search';
import { scheduleBodySet } from '../../../utils/flightPayloadUtils';
import type { FlightType } from '../../../api/unified-flight-search';
import type { FlightResponse } from '../../../models/flight/flight-list.model';
import {OneWayFlightList,RoundTripFlightList,MultiCityList} from './components';
import type { TripTypeValue } from './components';
import ModifySearchForm from '../../../components/flight/modify-search/ModifySearchForm';
import FlightDetailsPopup from '../../../components/flight/FlightDetailsPopup';
import { useFlightDetailsPopup } from '../../../hooks/useFlightDetailsPopup';
import './FlightListPage.scss';

// Wrapper component for ModifySearchForm that initializes context
interface StickyModifySearchProps {
  onSearchUpdate: (searchData: FlightSearchData) => void;
  isScrolled: boolean;
}

const StickyModifySearch: React.FC<StickyModifySearchProps> = ({
  onSearchUpdate,
  isScrolled
}) => {
  // No need to update context here - the search data is already managed by the parent component

  return (
    <div
      className={`sticky-modify-search ${isScrolled ? 'is-scrolled' : 'not-scrolled'}`}
      style={{
        position: isScrolled ? 'fixed' : 'relative',
        top: isScrolled ? '0px' : 'auto',
        left: isScrolled ? '0px' : 'auto',
        right: isScrolled ? '0px' : 'auto',
        zIndex: isScrolled ? 2147483647 : 10,
        width: '100%',
        backgroundColor: isScrolled ? 'rgba(255, 255, 255, 0.98)' : 'rgba(255, 255, 255, 1)', // Professional styling
        backdropFilter: isScrolled ? 'blur(12px) saturate(1.2)' : 'none',
        borderBottom: isScrolled ? '2px solid rgba(59, 130, 246, 0.2)' : '1px solid rgba(229, 231, 235, 1)',
        boxShadow: isScrolled ? '0 4px 20px -2px rgba(0, 0, 0, 0.1), 0 8px 40px -4px rgba(0, 0, 0, 0.15)' : 'none',
        transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
        minHeight: '60px', // Make sure it's visible
        // Force it to be above everything
        WebkitTransform: isScrolled ? 'translateZ(0)' : 'none',
        transform: isScrolled ? 'translateZ(0)' : 'none'
      }}
    >
      <div className="modify-search-container" style={{
        padding: isScrolled ? '0.5rem 1rem' : '0.75rem 1rem',
        maxWidth: '1920px',
        margin: '0 auto'
      }}>
        <ModifySearchForm
          onSearch={onSearchUpdate}
          isModal={false}
        />
      </div>
    </div>
  );
};

// Main component content
const FlightListPageContent: React.FC = () => {
  const location = useLocation();
  const navigate = useNavigate();
  const { currentSearch, updateSearch } = useFlightSearch();
  const isMountedRef = useRef(true);
  const timeoutRef = useRef<number | null>(null);
  const searchInProgressRef = useRef(false);

  // Scroll state for sticky modify search
  const [isScrolled, setIsScrolled] = useState(false);

  // Core state for flight search and results
  const [searchData, setSearchData] = useState<FlightSearchData | null>(null);

  // API response state - only store raw API data (child components will process this)
  const [apiFlightData, setApiFlightData] = useState<FlightResponse | null>(null);
  const [apiMetadata, setApiMetadata] = useState<any>(null);
  const [isApiDataLoaded, setIsApiDataLoaded] = useState(false);
  const [apiError, setApiError] = useState<string | null>(null);
  const [isPolling, setIsPolling] = useState(false);

  // UI state
  const [showFiltersMobile, setShowFiltersMobile] = useState(false);
  const [isSearching, setIsSearching] = useState(false);
  const [hasInitialSearchRun, setHasInitialSearchRun] = useState(false);

  // Flight details popup state
  const {
    isOpen: isFlightDetailsOpen,
    flightDetailsBody,
    selectedFlight,
    openFlightDetails,
    openRoundTripFlightDetails,
    closeFlightDetails,
    handleFlightSelection
  } = useFlightDetailsPopup();



  // Cleanup function
  useEffect(() => {
    return () => {
      isMountedRef.current = false;
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
        timeoutRef.current = null;
      }
    };
  }, []);



  // SHIMMER LOGIC: Show full shimmer only when no trip data is available
  // When search_refresh returns trips, render them immediately with selective price shimmers
  // Individual price shimmers are controlled by sh_price flag in FlightCard components

  // Background polling function
  const startBackgroundPolling = async (searchData: FlightSearchData, flightType: FlightType) => {
    try {
      const scheduleBody = scheduleBodySet(searchData, flightType);
      const pollingResult = await pollForCompleteData(scheduleBody);

      if (isMountedRef.current) {
        setApiFlightData(pollingResult.data);
        setApiMetadata(pollingResult.metadata);
        setIsPolling(false);
      }
    } catch (error) {
      if (isMountedRef.current) {
        setIsPolling(false);
      }
    }
  };

  // Main search function
  const handleInitialSearch = useCallback(async (searchData: FlightSearchData) => {
    if (searchInProgressRef.current || hasInitialSearchRun) {
      return;
    }

    searchInProgressRef.current = true;
    setIsSearching(true);
    // Clear previous API data when starting new search
    setApiFlightData(null);
    setApiMetadata(null);
    setApiError(null);
    setIsApiDataLoaded(false);

    // Set a maximum timeout to prevent infinite loading
    timeoutRef.current = window.setTimeout(() => {
      searchInProgressRef.current = false;
      setApiError('Search timed out. Please try again.');
      setIsApiDataLoaded(true);
      setIsSearching(false);
    }, 30000);

    try {
      const flightType: FlightType = searchData.tripType;

      // Call the unified flight search API
      const searchResult = await unifiedFlightSearch(searchData, flightType);

      // Check if we have flight data
      const hasFlightData = searchResult.data && searchResult.data.Trips && searchResult.data.Trips.length > 0;

      // Clear the timeout since we got a response
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
        timeoutRef.current = null;
      }

      // Set the API data
      setApiFlightData(searchResult.data);
      setApiMetadata(searchResult.metadata);
      setIsApiDataLoaded(true);
      setHasInitialSearchRun(true);

      // Only do polling logic if component is still mounted
      if (isMountedRef.current) {

        // Handle background polling if we have flight data but it's incomplete
        if (hasFlightData && !searchResult.data.Completed && searchResult.metadata.requiresPolling) {
          setIsPolling(true);
          startBackgroundPolling(searchData, flightType);
        }
      }
    } catch (error) {
      if (isMountedRef.current) {
        if (timeoutRef.current) {
          clearTimeout(timeoutRef.current);
          timeoutRef.current = null;
        }
        setApiError(error instanceof Error ? error.message : 'An error occurred during flight search');
        setIsApiDataLoaded(true);
      }
    } finally {
      searchInProgressRef.current = false;
      setIsSearching(false);
    }
  }, []);

  // Search update function
  const updateFlightSearch = useCallback(async (updatedSearchData: FlightSearchData) => {
    if (isSearching) {
      console.log('🚫 Search update blocked - search already in progress');
      return;
    }



    // Set loading state and clear previous data
    setIsSearching(true);
    setApiFlightData(null);
    setApiMetadata(null);
    setApiError(null);
    setIsApiDataLoaded(false);

    try {
      // Update local state immediately for UI responsiveness
      setSearchData(updatedSearchData);

      // Update the context as well
      updateSearch(updatedSearchData);

      // Save to localStorage
      localStorage.setItem('flightSearchData', JSON.stringify({
        ...updatedSearchData,
        depart: updatedSearchData.depart.toISOString(),
        return: updatedSearchData.return ? updatedSearchData.return.toISOString() : null
      }));

      // Convert tripType to FlightType format
      const flightType: FlightType = updatedSearchData.tripType;

      // Call the unified flight search API
      const searchResult = await unifiedFlightSearch(updatedSearchData, flightType);

      // Store the raw API results - child components will handle processing
      if (isMountedRef.current) {
        setApiFlightData(searchResult.data);
        setApiMetadata(searchResult.metadata);
      }

    } catch (error) {
      if (isMountedRef.current) {
        setApiError(error instanceof Error ? error.message : 'An error occurred during flight search');
      }
    } finally {
      if (isMountedRef.current) {
        setIsSearching(false);
      }
    }
  }, [updateSearch]);

  // Get search data from multiple sources (priority: navigation state > context > localStorage)
  useEffect(() => {
    // Only run once when component mounts
    if (hasInitialSearchRun || searchInProgressRef.current) {
      return;
    }

    const getSearchData = (): FlightSearchData | null => {
      // 1. Check navigation state first (highest priority)
      if (location.state?.searchData) {
        return location.state.searchData;
      }

      // 2. Check context
      if (currentSearch) {
        return currentSearch;
      }

      // 3. Check localStorage as fallback
      try {
        const stored = localStorage.getItem('flightSearchData');
        if (stored) {
          const parsed = JSON.parse(stored);
          return {
            ...parsed,
            depart: new Date(parsed.depart),
            return: parsed.return ? new Date(parsed.return) : null
          };
        }
      } catch (error) {
        console.error('Error parsing stored search data:', error);
      }

      return null;
    };

    const searchDataToUse = getSearchData();

    if (searchDataToUse) {
      setSearchData(searchDataToUse);
      handleInitialSearch(searchDataToUse);
    } else {
      navigate('/flight/search');
    }
  }, []); // Empty dependency array to run only once

  // Scroll handling for sticky modify search
  useEffect(() => {
    const handleScroll = () => {
      const scrollY = window.scrollY;
      const threshold = 80; // Trigger when scrolled past header area

      setIsScrolled(scrollY > threshold);
    };

    window.addEventListener('scroll', handleScroll, { passive: true });
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  // Get trip type for display
  const tripType: TripTypeValue = searchData?.tripType || 'oneWay';





  if (!searchData) {
    return (
      <PageWrapper>
        <div className="flex items-center justify-center min-h-[400px]">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
            <p className="text-gray-600">Loading search data...</p>
          </div>
        </div>
      </PageWrapper>
    );
  }

  return (
    <PageWrapper>
      <div className="flight-list-page">
        {/* Sticky Modify Search */}
        <StickyModifySearch
          onSearchUpdate={updateFlightSearch}
          isScrolled={isScrolled}
        />

        {/* Main Content */}
        <div className={`main-content background-secondary min-h-screen ${isScrolled ? 'has-sticky-search' : ''}`}>
          <div className="max-w-[1920px] mx-auto px-4 lg:px-6 xl:px-8">
          <div className="py-4 lg:py-6">
            {/* Flight Results - Maximum Width */}
            <div className="relative">


              {/* REBUILT LOGIC: Simple and clear rendering logic */}
              {/* Show loading message when actively searching */}
              {isSearching && (
                <div className="mb-4 bg-blue-50 border border-blue-200 rounded-lg p-4 text-center">
                  <div className="flex items-center justify-center space-x-2">
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"></div>
                    <span className="text-blue-700 font-medium">Getting the best results for you...</span>
                  </div>
                  <p className="text-blue-600 text-sm mt-1">This may take a few moments</p>
                </div>
              )}

              {/* 2. Show error if there's an API error */}
              {!isSearching && apiError && (
                <div className="bg-white rounded-lg border border-red-200 p-6 text-center">
                  <div className="text-red-500 mb-4">
                    <i className="fas fa-exclamation-triangle text-4xl"></i>
                  </div>
                  <h3 className="text-lg font-medium text-gray-900 mb-2">Search Error</h3>
                  <p className="text-gray-600 mb-4">We encountered an issue while searching for flights:</p>
                  <p className="text-sm text-red-600 bg-red-50 p-3 rounded mb-4">{apiError}</p>
                  <button
                    onClick={() => {
                      setApiError(null);
                      if (searchData) {
                        handleInitialSearch(searchData);
                      }
                    }}
                    className="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700 transition-colors"
                  >
                    <i className="fas fa-redo mr-2"></i>
                    Try Again
                  </button>
                </div>
              )}

              {/* 3. Always show flight components - they handle their own loading states */}
              {!apiError && (
                <div>
                  {/* Background polling indicator */}
                  {isPolling && (
                    <div className="mb-4 bg-blue-50 border border-blue-200 rounded-lg p-3 text-center">
                      <div className="flex items-center justify-center space-x-2">
                        <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"></div>
                        <span className="text-blue-700 text-sm font-medium">Updating prices...</span>
                      </div>
                    </div>
                  )}

                  {/* Flight list component */}
                  {(() => {
                    console.log('🎯 TRIP TYPE:', tripType, 'SEARCH DATA TRIP TYPE:', searchData?.tripType);
                    return null;
                  })()}
                  {tripType === 'oneWay' ? (
                    <OneWayFlightList
                      apiData={apiFlightData}
                      apiMetadata={apiMetadata}
                      isLoading={isSearching}
                      onShowFlightDetails={(flight) => openFlightDetails(flight, apiFlightData?.TUI)}
                    />
                  ) : tripType === 'multiCity' ? (
                    <MultiCityList
                      apiData={apiFlightData}
                      apiMetadata={apiMetadata}
                      isLoading={isSearching}
                      onShowFlightDetails={(flight) => openFlightDetails(flight, apiFlightData?.TUI)}
                    />
                  ) : (
                    <RoundTripFlightList
                      apiData={apiFlightData}
                      apiMetadata={apiMetadata}
                      isLoading={isSearching}
                      searchData={searchData}
                      onShowFlightDetails={(outbound, returnFlight) =>
                        returnFlight
                          ? openRoundTripFlightDetails(outbound, returnFlight, apiFlightData?.TUI)
                          : openFlightDetails(outbound, apiFlightData?.TUI)
                      }
                    />
                  )}
                </div>
              )}


            </div>
          </div>
        </div>
      </div>
      </div>

      {/* Flight Details Popup */}
      {isFlightDetailsOpen && flightDetailsBody && (
        <FlightDetailsPopup
          isMobile={false} // You can add mobile detection logic here
          currency="INR"
          flightDetailsBody={flightDetailsBody}
          isOpen={isFlightDetailsOpen}
          onClose={closeFlightDetails}
          onSelectFlight={handleFlightSelection}
        />
      )}
    </PageWrapper>
  );
};

// Main wrapper component
const FlightListPage: React.FC = () => {
  return (
    <FlightSearchProvider>
      <FlightListPageContent />
    </FlightSearchProvider>
  );
};

export default FlightListPage;
