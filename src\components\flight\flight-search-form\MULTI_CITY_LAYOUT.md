# Multi-City Flight Search Layout

## Overview
This document describes the implementation of the multi-city specific layout for the SearchFormFlight component, while maintaining the existing one-way and round-trip layouts.

## Layout Structure

### Multi-City Layout (when FareType === "MC")

The multi-city layout is organized into three distinct lines:

#### Line 1: Airport Selections and Departure Date
- **From Airport**: Airport selection with search functionality
- **Swap Button**: Allows swapping from/to airports
- **To Airport**: Destination airport selection
- **Departure Date**: Date picker for departure
- **Remove Trip Button**: Only visible when there are multiple trips

#### Line 2: Add City/Stop Button
- **Add City/Stop Button**: Centered button to add new trip segments
- Only displayed for the last trip in the sequence
- Automatically populates the "From" field of new trip with the "To" field of the previous trip

#### Line 3: Advanced Search and Travel Class
- **Advanced Search**: Toggle for advanced search options
- **Travel Class**: Passenger count and class selection
- Displayed once for all trips (not repeated per trip)

### Regular Layout (One-Way/Round-Trip)
- Maintains the existing layout structure
- No changes to current functionality
- Preserves all existing styling and behavior

## Key Features

### Multi-City Trip Management
- **Add Trip**: `addTrip()` function creates new trip segments
- **Remove Trip**: `removeTrip(index)` removes specific trip segments
- **Smart Defaults**: New trips automatically use previous trip's destination as starting point

### Conditional Rendering
- Layout switches based on `formArray.FareType === "MC"`
- Multi-city layout for MC trip type
- Original layout for ON (One-Way) and RT (Round-Trip)

### Responsive Design
- Uses Tailwind CSS classes for responsive behavior
- `lg:flex-row` for larger screens, `flex-col` for mobile
- Maintains consistent spacing and alignment

## Implementation Details

### State Management
- Uses existing form state management with react-hook-form
- No additional state variables required
- Leverages existing trip array structure

### Styling Classes
- **Multi-city container**: `multi-city-form`
- **Trip section**: `multi-city-trip-section`
- **Responsive layout**: `flex flex-col lg:flex-row lg:items-end gap-4 lg:gap-3`

### Button Styling
- **Add City/Stop**: Blue primary button with icon
- **Remove Trip**: Red accent button with X icon
- **Advanced Search**: Full-width toggle button
- **Travel Class**: Dropdown button with user icon

## Testing

Use the test component `test-multi-city.tsx` to verify:

1. Multi-city layout appears when MC is selected
2. All three lines are properly organized
3. Add/remove trip functionality works
4. Original layout preserved for ON/RT
5. Responsive behavior on different screen sizes

## Browser Compatibility

- Modern browsers supporting CSS Grid and Flexbox
- Tailwind CSS responsive utilities
- FontAwesome icons for UI elements

## Future Enhancements

- Trip reordering functionality
- Drag-and-drop trip management
- Enhanced validation for multi-city trips
- Trip duration calculations between segments
