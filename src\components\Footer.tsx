import React from 'react';
import { Container } from './layout';

export interface FooterLink {
  id: string;
  label: string;
  href: string;
  external?: boolean;
}

export interface FooterSection {
  id: string;
  title: string;
  links: FooterLink[];
}

export interface SocialLink {
  id: string;
  name: string;
  icon: string;
  href: string;
}

export interface TrustedCompany {
  id: string;
  name: string;
  icon: string;
  description?: string;
}

interface FooterProps {
  companyName?: string;
  year?: number;
  sections?: FooterSection[];
  socialLinks?: SocialLink[];
  trustedCompanies?: TrustedCompany[];
  backgroundColor?: string;
  textColor?: string;
  showNewsletter?: boolean;
  showTrustedCompanies?: boolean;
  onNewsletterSubmit?: (email: string) => void;
}

const Footer: React.FC<FooterProps> = ({
  trustedCompanies = [],
  showTrustedCompanies = true,
}) => {



  return (
    <>
      {/* Trusted Companies Section - Outside footer with different background */}
      {showTrustedCompanies && trustedCompanies.length > 0 && (
        <div className="bg-white py-16">
          <Container maxWidth="xl" padding="md">
            <div className="text-center mb-12">
              <h4 className="text-2xl font-bold text-gray-900 mb-4">Trusted by Leading Companies</h4>
              <p className="text-gray-600 max-w-2xl mx-auto">Join thousands of businesses that rely on TravelPro for their corporate travel needs</p>
            </div>
            <div className="grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-5 gap-8 md:gap-16 items-center justify-items-center">
              {trustedCompanies.map((company) => (
                <div
                  key={company.id}
                  className="flex flex-col items-center group"
                >
                  <i className={`${company.icon} text-3xl md:text-4xl text-gray-400 group-hover:text-gray-600 transition-colors duration-300`}></i>
                  <span className="mt-2 text-gray-600 font-medium text-sm text-center group-hover:text-gray-800 transition-colors">
                    {company.name}
                  </span>
                </div>
              ))}
            </div>
          </Container>
        </div>
      )}

      {/* Main Footer */}
      <footer className="bg-gray-900 text-gray-300">
        <Container maxWidth="xl" padding="md" className="py-12">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
            {/* Company Info */}
            <div>
              <div className="flex items-center mb-4">
                <i className="fas fa-globe text-blue-400 text-2xl mr-2"></i>
                <span className="text-lg font-semibold text-white">TravelPro</span>
              </div>
              <p className="text-sm text-gray-400">Your trusted partner for business travel solutions since 2020.</p>
            </div>

            {/* Company Links */}
            <div>
              <h4 className="text-white font-medium mb-4">Company</h4>
              <ul className="space-y-2 text-sm">
                <li><a href="/about" className="hover:text-white transition-colors">About Us</a></li>
                <li><a href="/careers" className="hover:text-white transition-colors">Careers</a></li>
                <li><a href="/press" className="hover:text-white transition-colors">Press</a></li>
                <li><a href="/blog" className="hover:text-white transition-colors">Blog</a></li>
              </ul>
            </div>

            {/* Support Links */}
            <div>
              <h4 className="text-white font-medium mb-4">Support</h4>
              <ul className="space-y-2 text-sm">
                <li><a href="/help" className="hover:text-white transition-colors">Help Center</a></li>
                <li><a href="/contact" className="hover:text-white transition-colors">Contact Us</a></li>
                <li><a href="/privacy" className="hover:text-white transition-colors">Privacy Policy</a></li>
                <li><a href="/terms" className="hover:text-white transition-colors">Terms of Service</a></li>
              </ul>
            </div>

            {/* Payment Methods */}
            <div>
              <h4 className="text-white font-medium mb-4">Payment Methods</h4>
              <div className="flex space-x-4 text-2xl">
                <i className="fab fa-cc-visa text-gray-400"></i>
                <i className="fab fa-cc-mastercard text-gray-400"></i>
                <i className="fab fa-cc-amex text-gray-400"></i>
                <i className="fab fa-cc-paypal text-gray-400"></i>
              </div>
            </div>

          </div>

          <div className="border-t border-gray-800 mt-12 pt-8 flex flex-col md:flex-row justify-between items-center">
            <p className="text-sm text-gray-400">© 2025 TravelPro. All rights reserved.</p>
            <div className="flex space-x-6 mt-4 md:mt-0">
              <a href="#" className="text-gray-400 hover:text-white transition-colors">
                <i className="fab fa-facebook"></i>
              </a>
              <a href="#" className="text-gray-400 hover:text-white transition-colors">
                <i className="fab fa-twitter"></i>
              </a>
              <a href="#" className="text-gray-400 hover:text-white transition-colors">
                <i className="fab fa-linkedin"></i>
              </a>
              <a href="#" className="text-gray-400 hover:text-white transition-colors">
                <i className="fab fa-instagram"></i>
              </a>
            </div>
          </div>
        </Container>
      </footer>
    </>
  );
};

export default Footer;
