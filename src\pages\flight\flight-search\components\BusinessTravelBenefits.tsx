import React from 'react';

export interface Benefit {
  id: string;
  title: string;
  description: string;
  icon: string;
  gradient?: string;
}

interface BusinessTravelBenefitsProps {
  benefits: Benefit[];
  title?: string;
  subtitle?: string;
  backgroundColor?: string;
}

const BusinessTravelBenefits: React.FC<BusinessTravelBenefitsProps> = ({ 
  benefits,
  title = "Why Choose TravelPro for Business",
  subtitle = "Streamlined corporate travel management with exclusive benefits",
  backgroundColor = "bg-gray-50"
}) => {
  return (
    <div className={`${backgroundColor} py-8 md:py-16`}>
      <div className="max-w-7xl mx-auto px-4">
        <div className="text-center mb-8 md:mb-12">
          <h2 className="text-2xl md:text-3xl font-bold text-gray-900 mb-2 md:mb-4">
            {title}
          </h2>
          <p className="text-gray-600 max-w-2xl mx-auto">
            {subtitle}
          </p>
        </div>
        
        <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-4 gap-4 md:gap-8">
          {benefits.map((benefit) => (
            <div 
              key={benefit.id}
              className="bg-white p-6 md:p-8 rounded-2xl shadow-lg hover:shadow-xl transition-all transform hover:-translate-y-1 group"
            >
              <div className={`w-12 h-12 md:w-14 md:h-14 ${benefit.gradient || 'bg-gradient-to-br from-blue-100 to-blue-50'} rounded-xl flex items-center justify-center mb-4 md:mb-6 group-hover:scale-110 transition-transform`}>
                <i className={`${benefit.icon} text-xl md:text-2xl text-blue-600`}></i>
              </div>
              <h3 className="text-lg md:text-xl font-semibold text-gray-900 mb-2 md:mb-4">
                {benefit.title}
              </h3>
              <p className="text-gray-600">
                {benefit.description}
              </p>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default BusinessTravelBenefits;
