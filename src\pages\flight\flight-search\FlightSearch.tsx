// The exported code uses Tailwind CSS. Install Tailwind CSS in your dev environment to ensure all styles work.
import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { PageWrapper } from '../../../components/layout';
import PopularDestinations, { type Destination } from './components/PopularDestinations';
import BusinessTravelBenefits, { type Benefit } from './components/BusinessTravelBenefits';
import { FlightSearchProvider, useFlightSearch } from '../../../contexts/FlightSearchContext';
import { navigateToResults } from '../../../utils/flightNavigationUtils';
import SearchFormFlight from '../../../components/flight/flight-search-form/SearchFormFlight';
import type { flight_search_form } from '../../../models/flight/flight-search.model';


// Inner component that uses the context
const FlightSearchContent: React.FC = () => {
    const [currentView] = useState('search');
    const navigate = useNavigate();

    // Access flight search context
    const {
        currentSearch,
        saveSearch,
        isSearchValid
    } = useFlightSearch();

    const popularDestinations: Destination[] = [
        {
            id: 'new-york',
            name: 'New York',
            country: 'United States',
            imageUrl: 'https://readdy.ai/api/search-image?query=new%20york%20city%20skyline%20with%20iconic%20buildings%20and%20business%20district%2C%20professional%20photography%20with%20dramatic%20lighting%2C%20high%20resolution%20cityscape&width=600&height=400&seq=10&orientation=landscape',
            startingPrice: 450,
            hasDirectFlights: true,
            description: 'The business capital of the world with endless opportunities'
        },
        {
            id: 'london',
            name: 'London',
            country: 'United Kingdom',
            imageUrl: 'https://readdy.ai/api/search-image?query=london%20city%20skyline%20with%20iconic%20landmarks%20like%20big%20ben%20and%20the%20thames%20river%2C%20professional%20photography%20with%20dramatic%20lighting%2C%20high%20resolution%20cityscape&width=600&height=400&seq=11&orientation=landscape',
            startingPrice: 520,
            hasDirectFlights: true,
            description: 'Historic financial center with modern business districts'
        },
        {
            id: 'singapore',
            name: 'Singapore',
            country: 'Singapore',
            imageUrl: 'https://readdy.ai/api/search-image?query=singapore%20skyline%20with%20marina%20bay%20sands%20and%20business%20district%2C%20professional%20photography%20with%20dramatic%20lighting%2C%20high%20resolution%20cityscape&width=600&height=400&seq=12&orientation=landscape',
            startingPrice: 680,
            hasDirectFlights: true,
            description: 'Gateway to Asia with world-class business infrastructure'
        }
    ];

    const businessBenefits: Benefit[] = [
        {
            id: 'corporate-rates',
            title: 'Corporate Rates',
            description: 'Exclusive discounted rates negotiated specifically for business travelers',
            icon: 'fas fa-percentage'
        },
        {
            id: 'expense-management',
            title: 'Expense Management',
            description: 'Integrated tools to track, report and manage all travel expenses',
            icon: 'fas fa-file-invoice-dollar'
        },
        {
            id: 'support',
            title: '24/7 Support',
            description: 'Dedicated business travel support team available around the clock',
            icon: 'fas fa-headset'
        },
        {
            id: 'duty-of-care',
            title: 'Duty of Care',
            description: 'Comprehensive traveler tracking and risk management solutions',
            icon: 'fas fa-shield-alt'
        }
    ];

    // Enhanced search handler that uses context and localStorage
    const handleSearch = (searchData: flight_search_form) => {
        console.log('🔍 Search initiated with data:', searchData);

        // Validate search data
        if (!searchData.trips || searchData.trips.length === 0) {
            console.error('❌ No trip data provided');
            return;
        }

        const firstTrip = searchData.trips[0];
        if (!firstTrip) {
            console.error('❌ No trip data found');
            return;
        }

        // Convert flight_search_form to FlightSearchData format
        const convertedSearchData = {
            from: {
                iata: firstTrip.from.iata,
                name: firstTrip.from.airport,
                city: firstTrip.from.city,
                country: firstTrip.from.country,
                id: firstTrip.from.iata
            },
            to: {
                iata: firstTrip.to.iata,
                name: firstTrip.to.airport,
                city: firstTrip.to.city,
                country: firstTrip.to.country,
                id: firstTrip.to.iata
            },
            depart: firstTrip.depart,
            return: firstTrip.return,
            tripType: searchData.FareType === 'RT' ? 'roundTrip' as const :
                     searchData.FareType === 'ON' ? 'oneWay' as const :
                     'multiCity' as const,
            passengers: {
                adults: searchData.travellers.adult,
                children: searchData.travellers.child,
                infants: searchData.travellers.infant
            },
            class: searchData.cabin === 'E' ? 'economy' as const :
                   searchData.cabin === 'PE' ? 'premiumEconomy' as const :
                   searchData.cabin === 'B' ? 'business' as const :
                   'first' as const,
            advanced_search: {
                selectedAirlines: [],
                flightOptions: {
                    directFlights: false,
                    refundableFares: false,
                    corporateRates: false,
                },
                services: {
                    airportLounge: false,
                    extraBaggage: false,
                    travelInsurance: false,
                },
                stops: 'any' as const,
                baggage: {
                    carryOn: false,
                    checked: false,
                },
            }
        };

        console.log('🔄 Converted search data:', convertedSearchData);

        // Save search to context and localStorage
        if (isSearchValid()) {
            saveSearch();
            console.log('✅ Search saved to context and localStorage');
        }

        // Navigate to flight list page with search data
        navigateToResults(navigate, convertedSearchData, searchData);
        console.log('🛫 Navigating to flight results...');
    };

    const handleDestinationClick = (destination: Destination) => {
        console.log('Destination clicked:', destination);
        // You can add logic here to pre-fill the search form or navigate to search results
    };
    return (
        <PageWrapper background="gray" padding="none">
            {/* Main Content */}
            {currentView === 'search' && (
                <div className="pt-16">
                    {/* Flight Search Form */}
                    <SearchFormFlight onSearch={handleSearch} />

                    {/* Popular Destinations */}
                    <PopularDestinations
                        destinations={popularDestinations}
                        onDestinationClick={handleDestinationClick}
                    />
                    {/* Business Travel Benefits */}
                    <BusinessTravelBenefits benefits={businessBenefits} />
                </div>
            )}
        </PageWrapper>
    );
};

// Main component wrapped with FlightSearchProvider
const FlightSearch: React.FC = () => {
    return (
        <FlightSearchProvider>
            <FlightSearchContent />
        </FlightSearchProvider>
    );
};

export default FlightSearch;