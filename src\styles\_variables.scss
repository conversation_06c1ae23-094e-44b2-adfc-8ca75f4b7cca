// ============================================================================
// SCSS Color Variables - Centralized Color System
// ============================================================================

// Primary Colors (Main theme colors)
// --------------------------------------------------
$primary_color: #3b82f6;           // Blue-500 - Main brand color
$primary_color_light: #60a5fa;     // Blue-400 - Lighter variant
$primary_color_lighter: #93c5fd;   // Blue-300 - Even lighter
$primary_color_lightest: #dbeafe;  // Blue-100 - Lightest variant
$primary_color_dark: #2563eb;      // Blue-600 - Darker variant
$primary_color_darker: #1d4ed8;    // Blue-700 - Even darker
$primary_color_darkest: #1e40af;   // Blue-800 - Darkest variant

// Secondary Colors (Supporting brand colors)
// --------------------------------------------------
$secondary_color: #10b981;         // Emerald-500 - Success/positive actions
$secondary_color_light: #34d399;   // Emerald-400 - Lighter variant
$secondary_color_lighter: #6ee7b7; // Emerald-300 - Even lighter
$secondary_color_lightest: #d1fae5; // Emerald-100 - Lightest variant
$secondary_color_dark: #059669;    // Emerald-600 - Darker variant
$secondary_color_darker: #047857;  // Emerald-700 - Even darker
$secondary_color_darkest: #065f46; // Emerald-800 - Darkest variant

// Accent Colors (Highlighting and special elements)
// --------------------------------------------------
$accent_color: #f59e0b;            // Amber-500 - Warning/attention
$accent_color_light: #fbbf24;      // Amber-400 - Lighter variant
$accent_color_lighter: #fcd34d;    // Amber-300 - Even lighter
$accent_color_lightest: #fef3c7;   // Amber-100 - Lightest variant
$accent_color_dark: #d97706;       // Amber-600 - Darker variant
$accent_color_darker: #b45309;     // Amber-700 - Even darker
$accent_color_darkest: #92400e;    // Amber-800 - Darkest variant

// Neutral Colors (Grays for text, backgrounds, borders)
// --------------------------------------------------
$neutral_50: #f9fafb;              // Gray-50 - Lightest background
$neutral_100: #f3f4f6;             // Gray-100 - Light background
$neutral_200: #e5e7eb;             // Gray-200 - Light borders
$neutral_300: #d1d5db;             // Gray-300 - Medium borders
$neutral_400: #9ca3af;             // Gray-400 - Disabled text
$neutral_500: #6b7280;             // Gray-500 - Secondary text
$neutral_600: #4b5563;             // Gray-600 - Primary text
$neutral_700: #374151;             // Gray-700 - Dark text
$neutral_800: #1f2937;             // Gray-800 - Darker text
$neutral_900: #111827;             // Gray-900 - Darkest text

// Semantic Colors (Status and feedback)
// --------------------------------------------------
$success_color: #10b981;           // Emerald-500 - Success states
$success_color_light: #d1fae5;     // Emerald-100 - Success backgrounds
$success_color_dark: #047857;      // Emerald-700 - Success text

$warning_color: #f59e0b;           // Amber-500 - Warning states
$warning_color_light: #fef3c7;     // Amber-100 - Warning backgrounds
$warning_color_dark: #b45309;      // Amber-700 - Warning text

$error_color: #ef4444;             // Red-500 - Error states
$error_color_light: #fee2e2;       // Red-100 - Error backgrounds
$error_color_dark: #b91c1c;        // Red-700 - Error text

$info_color: #3b82f6;              // Blue-500 - Info states
$info_color_light: #dbeafe;        // Blue-100 - Info backgrounds
$info_color_dark: #1d4ed8;         // Blue-700 - Info text

// Background Colors
// --------------------------------------------------
$background_primary: #ffffff;       // White - Main background
$background_secondary: #f9fafb;     // Gray-50 - Secondary background
$background_tertiary: #f3f4f6;     // Gray-100 - Tertiary background
$background_overlay: rgba(0, 0, 0, 0.5); // Semi-transparent overlay

// Text Colors
// --------------------------------------------------
$text_primary: #111827;            // Gray-900 - Primary text
$text_secondary: #6b7280;          // Gray-500 - Secondary text
$text_tertiary: #9ca3af;           // Gray-400 - Tertiary text
$text_disabled: #d1d5db;           // Gray-300 - Disabled text
$text_inverse: #ffffff;            // White - Text on dark backgrounds

// Border Colors
// --------------------------------------------------
$border_primary: #e5e7eb;          // Gray-200 - Primary borders
$border_secondary: #d1d5db;        // Gray-300 - Secondary borders
$border_focus: #3b82f6;            // Blue-500 - Focus borders
$border_error: #ef4444;            // Red-500 - Error borders
$border_success: #10b981;          // Emerald-500 - Success borders

// Shadow Colors
// --------------------------------------------------
$shadow_sm: rgba(0, 0, 0, 0.05);   // Small shadows
$shadow_md: rgba(0, 0, 0, 0.1);    // Medium shadows
$shadow_lg: rgba(0, 0, 0, 0.15);   // Large shadows
$shadow_xl: rgba(0, 0, 0, 0.25);   // Extra large shadows

// Component-specific Colors
// --------------------------------------------------
$header_background: #ffffff;        // Header background
$footer_background: #1f2937;        // Footer background (Gray-800)
$card_background: #ffffff;          // Card background
$input_background: #ffffff;         // Input background
$button_primary_background: $primary_color;
$button_secondary_background: $neutral_100;

// Flight-specific Colors
// --------------------------------------------------
$flight_card_background: #ffffff;
$flight_card_border: $border_primary;
$flight_card_hover_border: $primary_color_lighter;
$flight_selected_background: $primary_color_lightest;
$flight_selected_border: $primary_color;
$corporate_rate_background: linear-gradient(135deg, $primary_color, $primary_color_dark);

// Calendar-specific Colors
// --------------------------------------------------
$calendar_selected_background: $primary_color;
$calendar_selected_text: $text_inverse;
$calendar_range_background: $primary_color_lightest;
$calendar_range_text: $primary_color_dark;
$calendar_hover_background: $neutral_50;
$calendar_disabled_text: $text_disabled;
$calendar_return_background: $secondary_color;
$calendar_return_text: $text_inverse;
$calendar_modal_background: $background_primary;
$calendar_modal_border: $border_primary;
$calendar_modal_shadow: $shadow_xl;
$calendar_day_height: 48px;
$calendar_day_border_radius: 8px;
$calendar_header_background: $primary_color_lightest;
$calendar_weekend_background: $neutral_50;
$calendar_today_border: $accent_color;
$calendar_price_text: $text_secondary;
$calendar_modal_max_width: 1024px;
$calendar_modal_padding: 24px;
$calendar_month_gap: 24px;
$calendar_day_gap: 4px;
