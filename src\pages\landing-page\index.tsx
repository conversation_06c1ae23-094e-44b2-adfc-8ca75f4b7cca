import React from 'react';
import { PageWrapper, Container } from '../../components/layout';
import Header from './components/Header';
import LoginSection from './components/LoginSection';
import Footer, { type TrustedCompany } from '../../components/Footer';

const LandingPage: React.FC = () => {
  const tabs = [
    { id: 'flights', label: 'Flights', icon: 'fa-plane' },
    { id: 'stay', label: 'Stay', icon: 'fa-hotel' },
    { id: 'visa', label: 'Visa', icon: 'fa-passport' },
    { id: 'cruise', label: 'Cruise', icon: 'fa-ship' },
    { id: 'holiday', label: 'Holiday', icon: 'fa-umbrella-beach' }
  ];

  // Trusted companies data for the footer
  const trustedCompanies: TrustedCompany[] = [
    {
      id: 'microsoft',
      name: 'Microsoft',
      icon: 'fab fa-microsoft'
    },
    {
      id: 'amazon',
      name: 'Amazon',
      icon: 'fab fa-amazon'
    },
    {
      id: 'google',
      name: 'Google',
      icon: 'fab fa-google'
    },
    {
      id: 'apple',
      name: 'Apple',
      icon: 'fab fa-apple'
    },
    {
      id: 'salesforce',
      name: 'Salesforce',
      icon: 'fab fa-salesforce'
    }
  ];

  const handleNewsletterSubmit = (email: string) => {
    console.log('Newsletter subscription:', email);
    // Add your newsletter subscription logic here
  };

  return (
    <PageWrapper background="gray" padding="none" fullHeight={false}>
      {/* Header */}
      <Header />

      {/* Hero Section */}
      <div className="pt-16 relative">
        <div className="relative h-[600px] bg-cover bg-center" style={{
          backgroundImage: `url('https://readdy.ai/api/search-image?query=luxury%20business%20travel%20concept%20showing%20modern%20airport%20terminal%20with%20glass%20architecture%2C%20professional%20business%20travelers%20walking%20through%20a%20contemporary%20airport%20with%20dramatic%20lighting%20and%20reflective%20surfaces&width=1440&height=600&seq=1&orientation=landscape')`
        }}>
          <div className="absolute inset-0 bg-gradient-to-r from-gray-900/90 to-gray-900/30"></div>
          <Container maxWidth="xl" padding="md" className="relative h-full">
            <div className="flex flex-col justify-center h-full max-w-2xl text-white">
              <h1 className="text-5xl font-bold mb-6">Premier B2B Travel Solutions</h1>
              <p className="text-xl mb-8">Your one-stop platform for corporate travel management. Experience seamless business travel planning with TravelPro.</p>
            </div>
          </Container>
        </div>

        {/* Travel Categories */}
        <Container maxWidth="xl" padding="md" className="-mt-24 relative z-10">
          <div className="grid grid-cols-1 md:grid-cols-5 gap-6">
            {tabs.map(tab => (
              <div key={tab.id} className="bg-white rounded-xl shadow-lg p-6 text-center transform hover:-translate-y-1 transition-transform duration-300 cursor-pointer group">
                <div className="w-16 h-16 mx-auto mb-4 bg-blue-50 rounded-full flex items-center justify-center group-hover:bg-blue-100 transition-colors">
                  <i className={`fas ${tab.icon} text-2xl text-blue-600`}></i>
                </div>
                <h3 className="text-lg font-semibold text-gray-800 mb-2">{tab.label}</h3>
                <p className="text-sm text-gray-600">Premium {tab.label.toLowerCase()} services</p>
              </div>
            ))}
          </div>
          
          {/* Login Section */}
          <LoginSection />
          
        </Container>

        {/* Services Section */}
        <Container maxWidth="xl" padding="md" className="mt-32">
            <div className="text-center mb-16">
              <span className="inline-block px-4 py-2 bg-blue-50 text-blue-600 rounded-full text-sm font-medium mb-4">Our Services</span>
              <h2 className="text-4xl font-bold text-gray-900 mb-4">Comprehensive Travel Solutions</h2>
              <p className="text-gray-600 text-lg max-w-2xl mx-auto">End-to-end travel management services tailored for modern businesses</p>
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-16">
              {/* Corporate Travel Management */}
              <div className="bg-white rounded-2xl shadow-xl overflow-hidden group hover:shadow-2xl transition-all">
                <div className="h-64 overflow-hidden">
                  <img 
                    src="https://readdy.ai/api/search-image?query=modern%20corporate%20meeting%20room%20with%20business%20professionals%20discussing%20travel%20plans%20professional%20photography%20cinematic%20lighting%20highly%20detailed&width=800&height=600&seq=5&orientation=landscape"
                    alt="Corporate Travel" 
                    className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-500"
                  />
                </div>
                <div className="p-8">
                  <h3 className="text-2xl font-bold text-gray-900 mb-4">Corporate Travel Management</h3>
                  <ul className="space-y-3">
                    <li className="flex items-center text-gray-600">
                      <i className="fas fa-check-circle text-green-500 mr-3"></i>
                      Customized travel policies
                    </li>
                    <li className="flex items-center text-gray-600">
                      <i className="fas fa-check-circle text-green-500 mr-3"></i>
                      Expense management tools
                    </li>
                    <li className="flex items-center text-gray-600">
                      <i className="fas fa-check-circle text-green-500 mr-3"></i>
                      Real-time reporting dashboard
                    </li>
                  </ul>
                </div>
              </div>

              {/* Event Management */}
              <div className="bg-white rounded-2xl shadow-xl overflow-hidden group hover:shadow-2xl transition-all">
                <div className="h-64 overflow-hidden">
                  <img 
                    src="https://readdy.ai/api/search-image?query=elegant%20corporate%20conference%20venue%20setup%20with%20modern%20audiovisual%20equipment%20and%20business%20professionals%20networking%20professional%20photography%20cinematic%20lighting&width=800&height=600&seq=6&orientation=landscape"
                    alt="Event Management" 
                    className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-500"
                  />
                </div>
                <div className="p-8">
                  <h3 className="text-2xl font-bold text-gray-900 mb-4">Event Management</h3>
                  <ul className="space-y-3">
                    <li className="flex items-center text-gray-600">
                      <i className="fas fa-check-circle text-green-500 mr-3"></i>
                      Conference planning & execution
                    </li>
                    <li className="flex items-center text-gray-600">
                      <i className="fas fa-check-circle text-green-500 mr-3"></i>
                      Venue selection & booking
                    </li>
                    <li className="flex items-center text-gray-600">
                      <i className="fas fa-check-circle text-green-500 mr-3"></i>
                      On-site coordination
                    </li>
                  </ul>
                </div>
              </div>
            </div>

            {/* Customer Needs Section */}
            <div className="bg-gradient-to-br from-gray-50 to-blue-50 rounded-3xl p-12 mb-16">
              <div className="text-center mb-12">
                <h3 className="text-3xl font-bold text-gray-900 mb-4">Tailored to Your Needs</h3>
                <p className="text-gray-600">Solutions designed for different business sizes and requirements</p>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
                <div className="bg-white p-8 rounded-2xl shadow-lg hover:shadow-xl transition-all">
                  <div className="w-14 h-14 bg-blue-600 rounded-xl flex items-center justify-center mb-6">
                    <i className="fas fa-building text-2xl text-white"></i>
                  </div>
                  <h4 className="text-xl font-semibold text-gray-900 mb-4">Enterprise Solutions</h4>
                  <ul className="space-y-3 text-gray-600">
                    <li className="flex items-center">
                      <i className="fas fa-check text-blue-600 mr-3"></i>
                      Custom travel policies
                    </li>
                    <li className="flex items-center">
                      <i className="fas fa-check text-blue-600 mr-3"></i>
                      Dedicated account manager
                    </li>
                    <li className="flex items-center">
                      <i className="fas fa-check text-blue-600 mr-3"></i>
                      Advanced reporting tools
                    </li>
                  </ul>
                </div>

                <div className="bg-white p-8 rounded-2xl shadow-lg hover:shadow-xl transition-all">
                  <div className="w-14 h-14 bg-blue-600 rounded-xl flex items-center justify-center mb-6">
                    <i className="fas fa-store text-2xl text-white"></i>
                  </div>
                  <h4 className="text-xl font-semibold text-gray-900 mb-4">SMB Solutions</h4>
                  <ul className="space-y-3 text-gray-600">
                    <li className="flex items-center">
                      <i className="fas fa-check text-blue-600 mr-3"></i>
                      Flexible booking options
                    </li>
                    <li className="flex items-center">
                      <i className="fas fa-check text-blue-600 mr-3"></i>
                      Cost-effective packages
                    </li>
                    <li className="flex items-center">
                      <i className="fas fa-check text-blue-600 mr-3"></i>
                      Simple management tools
                    </li>
                  </ul>
                </div>

                <div className="bg-white p-8 rounded-2xl shadow-lg hover:shadow-xl transition-all">
                  <div className="w-14 h-14 bg-blue-600 rounded-xl flex items-center justify-center mb-6">
                    <i className="fas fa-user-tie text-2xl text-white"></i>
                  </div>
                  <h4 className="text-xl font-semibold text-gray-900 mb-4">Individual Business</h4>
                  <ul className="space-y-3 text-gray-600">
                    <li className="flex items-center">
                      <i className="fas fa-check text-blue-600 mr-3"></i>
                      Quick booking process
                    </li>
                    <li className="flex items-center">
                      <i className="fas fa-check text-blue-600 mr-3"></i>
                      24/7 travel support
                    </li>
                    <li className="flex items-center">
                      <i className="fas fa-check text-blue-600 mr-3"></i>
                      Loyalty rewards
                    </li>
                  </ul>
                </div>
              </div>
            </div>

            {/* Key Features Section */}
            <div className="text-center mb-16">
              <span className="inline-block px-4 py-2 bg-blue-50 text-blue-600 rounded-full text-sm font-medium mb-4">Why Choose Us</span>
              <h2 className="text-4xl font-bold text-gray-900 mb-4">The TravelPro Advantage</h2>
              <p className="text-gray-600 text-lg max-w-2xl mx-auto">Experience the difference with our premium business travel management solutions</p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
              <div className="bg-white p-8 rounded-2xl shadow-xl hover:shadow-2xl transition-all">
                <div className="w-12 h-12 bg-blue-50 rounded-lg flex items-center justify-center mb-4">
                  <i className="fas fa-shield-alt text-xl text-blue-600"></i>
                </div>
                <h3 className="text-xl font-semibold text-gray-800 mb-3">Secure Booking</h3>
                <p className="text-gray-600">Advanced encryption and secure payment processing</p>
              </div>

              <div className="bg-white p-8 rounded-2xl shadow-xl hover:shadow-2xl transition-all">
                <div className="w-12 h-12 bg-blue-50 rounded-lg flex items-center justify-center mb-4">
                  <i className="fas fa-clock text-xl text-blue-600"></i>
                </div>
                <h3 className="text-xl font-semibold text-gray-800 mb-3">24/7 Support</h3>
                <p className="text-gray-600">Round-the-clock dedicated assistance</p>
              </div>

              <div className="bg-white p-8 rounded-2xl shadow-xl hover:shadow-2xl transition-all">
                <div className="w-12 h-12 bg-blue-50 rounded-lg flex items-center justify-center mb-4">
                  <i className="fas fa-percentage text-xl text-blue-600"></i>
                </div>
                <h3 className="text-xl font-semibold text-gray-800 mb-3">Best Rates</h3>
                <p className="text-gray-600">Competitive pricing and corporate discounts</p>
              </div>

              <div className="bg-white p-8 rounded-2xl shadow-xl hover:shadow-2xl transition-all">
                <div className="w-12 h-12 bg-blue-50 rounded-lg flex items-center justify-center mb-4">
                  <i className="fas fa-chart-line text-xl text-blue-600"></i>
                </div>
                <h3 className="text-xl font-semibold text-gray-800 mb-3">Analytics</h3>
                <p className="text-gray-600">Detailed insights and spending reports</p>
              </div>
            </div>
        </Container>
      </div>

      {/* App Download Section */}
      <Container maxWidth="xl" padding="md" className="py-24">
        <div className="bg-gradient-to-br from-blue-600 to-indigo-700 rounded-3xl overflow-hidden shadow-2xl">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-12 items-center p-12">
            <div>
              <span className="inline-block px-4 py-2 bg-blue-500/20 text-white rounded-full text-sm font-medium mb-6">Mobile App</span>
              <h2 className="text-4xl font-bold text-white mb-6">Your Travel Assistant in Your Pocket</h2>
              <p className="text-blue-100 mb-8 text-lg">Experience seamless business travel management with real-time updates, instant bookings, and 24/7 support - all in one app.</p>
              <div className="space-y-6">
                <div className="flex items-center space-x-4">
                  <div className="w-12 h-12 bg-blue-500/20 rounded-xl flex items-center justify-center">
                    <i className="fas fa-bolt text-2xl text-white"></i>
                  </div>
                  <div>
                    <h3 className="text-white font-semibold">Quick Booking</h3>
                    <p className="text-blue-100 text-sm">Book flights and hotels in minutes</p>
                  </div>
                </div>
                <div className="flex items-center space-x-4">
                  <div className="w-12 h-12 bg-blue-500/20 rounded-xl flex items-center justify-center">
                    <i className="fas fa-bell text-2xl text-white"></i>
                  </div>
                  <div>
                    <h3 className="text-white font-semibold">Real-time Updates</h3>
                    <p className="text-blue-100 text-sm">Get instant notifications about your travel</p>
                  </div>
                </div>
                <div className="flex items-center space-x-4">
                  <div className="w-12 h-12 bg-blue-500/20 rounded-xl flex items-center justify-center">
                    <i className="fas fa-headset text-2xl text-white"></i>
                  </div>
                  <div>
                    <h3 className="text-white font-semibold">24/7 Support</h3>
                    <p className="text-blue-100 text-sm">Always here to help you</p>
                  </div>
                </div>
              </div>
              <div className="flex flex-wrap gap-4 mt-12">
                <button className="!rounded-button flex items-center space-x-3 bg-white text-gray-900 px-6 py-3 hover:bg-gray-50 transition-colors cursor-pointer whitespace-nowrap">
                  <i className="fab fa-apple text-2xl"></i>
                  <div className="text-left">
                    <div className="text-xs">Download on the</div>
                    <div className="text-sm font-medium">App Store</div>
                  </div>
                </button>
                <button className="!rounded-button flex items-center space-x-3 bg-white text-gray-900 px-6 py-3 hover:bg-gray-50 transition-colors cursor-pointer whitespace-nowrap">
                  <i className="fab fa-google-play text-2xl"></i>
                  <div className="text-left">
                    <div className="text-xs">Get it on</div>
                    <div className="text-sm font-medium">Google Play</div>
                  </div>
                </button>
              </div>
            </div>
            <div className="relative h-[600px] flex items-center justify-center">
              <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-[400px] h-[600px]">
                <img
                  src="https://readdy.ai/api/search-image?query=elegant%20travel%20mobile%20app%20interface%20mockup%20with%20multiple%20floating%20screens%20showing%20booking%20process%2C%20dark%20mode%20UI%20with%20professional%20design%2C%20ultra%20high%20quality%203d%20render&width=800&height=1200&seq=4&orientation=portrait"
                  alt="App Preview"
                  className="w-full h-full object-contain"
                />
              </div>
            </div>
          </div>
        </div>
      </Container>

      {/* Footer */}
      <Footer
        trustedCompanies={trustedCompanies}
        onNewsletterSubmit={handleNewsletterSubmit}
        showNewsletter={false}
      />
    </PageWrapper>
  );
};

export default LandingPage;
