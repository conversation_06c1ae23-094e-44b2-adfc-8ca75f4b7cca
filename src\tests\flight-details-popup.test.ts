/**
 * Flight Details Popup Integration Tests
 * Tests for the comprehensive flight details popup functionality
 */

import type { FlightDetailsEmitData, TripJackApiDataInfo } from '../models/flight/flight-details.model';
import { transformTripJackToLegacyFormat, extractBaggageFromTripJackResponse, extractFareRulesFromTripJackResponse, createFallbackFareSummary } from '../helper/flight/flight-details-helper';
import { TripJackResponseAdapter } from '../helper/flight/tripjack-response-adapter';

// Mock TripJack API response data
const mockTripJackResponse: TripJackApiDataInfo = {
  Trips: [{
    Journey: [{
      Provider: 'TripJack',
      Segments: [{
        FUID: 'TJ001',
        VAC: '6E',
        MAC: '6E',
        Airline: 'IndiGo',
        FlightNo: '6E 123',
        AirCraft: 'A320',
        Cabin: 'Economy',
        DepartureTime: '2024-12-25T10:30:00',
        ArrivalTime: '2024-12-25T12:45:00',
        DepartureCode: 'DEL',
        ArrivalCode: 'BOM',
        DepAirportName: 'Indira Gandhi International Airport',
        ArrAirportName: 'Chhatrapati Shivaji Maharaj International Airport',
        Duration: '2h 15m',
        FareBasis: 'SAVER',
        BaggageAllowance: '15 kg',
        SSRInfo: {}
      }]
    }]
  }],
  extra: {
    tripjack_response: {
      searchQuery: {
        paxInfo: {
          ADULT: 1,
          CHILD: 0,
          INFANT: 0
        },
        cabinClass: 'Economy'
      },
      tripInfos: [{
        sI: [],
        totalPriceList: [{
          id: 'price1',
          fareIdentifier: 'SAVER',
          msri: [],
          refundable: false,
          fd: {
            ADULT: {
              fC: {
                BF: 4500,
                TAF: 1200,
                NF: 5700
              },
              afC: {
                TAF: {
                  YQ: 800,
                  YR: 200,
                  K3: 100,
                  IN: 50,
                  WO: 30,
                  OT: 20
                }
              },
              sR: 0,
              bI: {
                iB: '15 kg',
                cB: '7 kg'
              }
            }
          }
        }]
      }],
      totalPriceList: [{
        fareRuleInformation: {
          tfr: [{
            ruleType: 'cancellation',
            description: 'Cancellation charges apply as per airline policy',
            penalty: '₹3000 + airline charges'
          }, {
            ruleType: 'dateChange',
            description: 'Date change allowed with charges',
            penalty: '₹2500 + fare difference'
          }]
        }
      }]
    }
  },
  SSRServices: {
    BaggageServices: [{
      Code: 'XBAG20',
      Description: 'Extra 20kg Baggage',
      Amount: '2500',
      Currency: 'INR'
    }],
    MealServices: [{
      Code: 'MEAL1',
      Description: 'Vegetarian Meal',
      Amount: '450',
      Currency: 'INR'
    }],
    SeatServices: [{
      Code: 'SEAT1',
      Description: 'Preferred Seat',
      Amount: '800',
      Currency: 'INR'
    }]
  },
  FareRules: [{
    RuleType: 'refund',
    Description: 'Non-refundable ticket',
    Penalty: 'No refund allowed'
  }]
};

// Mock FlightDetailsEmitData
const mockFlightDetailsBody: FlightDetailsEmitData = {
  TripType: 'ON',
  Trips: [[{
    TUI: 'test_tui_123',
    Amount: 5700,
    Index: 'flight_index_456',
    OrderID: 1,
    ChannelCode: null
  }]]
};

describe('Flight Details Helper Functions', () => {
  test('transformTripJackToLegacyFormat should transform data correctly', () => {
    const result = transformTripJackToLegacyFormat(mockTripJackResponse);
    
    expect(result).toBeDefined();
    expect(result.flightSegments).toHaveLength(1);
    expect(result.flightSegments[0].airline).toBe('IndiGo');
    expect(result.flightSegments[0].flightNumber).toBe('6E 123');
    expect(result.fareBreakdown.totalAmount).toBe(5700);
    expect(result.fareBreakdown.baseFare).toBe(4500);
    expect(result.fareBreakdown.taxes).toBe(1200);
  });

  test('extractBaggageFromTripJackResponse should extract baggage info', () => {
    const result = extractBaggageFromTripJackResponse(mockTripJackResponse, 0);
    
    expect(result).toBeDefined();
    expect(result.cabin).toBe('7 kg');
    expect(result.checked).toBe('15 kg');
    expect(result.additionalServices).toHaveLength(1);
    expect(result.additionalServices[0].description).toBe('Extra 20kg Baggage');
  });

  test('extractFareRulesFromTripJackResponse should extract fare rules', () => {
    const result = extractFareRulesFromTripJackResponse(mockTripJackResponse, 0);
    
    expect(result).toBeDefined();
    expect(result).toHaveLength(3); // 2 from totalPriceList + 1 from FareRules
    expect(result[0].ruleType).toBe('cancellation');
    expect(result[0].description).toContain('Cancellation charges apply');
  });

  test('createFallbackFareSummary should create proper fare summary', () => {
    const result = createFallbackFareSummary(mockTripJackResponse);
    
    expect(result).toBeDefined();
    expect(result.totalAmount).toBe(5700);
    expect(result.baseFare).toBe(4500);
    expect(result.taxes).toBe(1200);
    expect(result.currency).toBe('INR');
    expect(result.passengers.adults).toBe(1);
    expect(result.breakdown).toBeDefined();
  });
});

describe('TripJack Response Adapter', () => {
  test('transformSSRResponse should extract SSR services', () => {
    const result = TripJackResponseAdapter.transformSSRResponse(mockTripJackResponse);
    
    expect(result).toBeDefined();
    expect(result.BaggageServices).toHaveLength(1);
    expect(result.MealServices).toHaveLength(1);
    expect(result.SeatServices).toHaveLength(1);
  });

  test('transformFareRulesResponse should extract fare rules', () => {
    const result = TripJackResponseAdapter.transformFareRulesResponse(mockTripJackResponse);
    
    expect(result).toBeDefined();
    expect(result.length).toBeGreaterThan(0);
    expect(result[0]).toHaveProperty('RuleType');
    expect(result[0]).toHaveProperty('Description');
  });

  test('extractPassengerInfo should extract passenger information', () => {
    const result = TripJackResponseAdapter.extractPassengerInfo(mockTripJackResponse);
    
    expect(result).toBeDefined();
    expect(result.ADULT).toBe(1);
    expect(result.CHILD).toBe(0);
    expect(result.INFANT).toBe(0);
  });

  test('calculateTotalFare should calculate fare correctly', () => {
    const result = TripJackResponseAdapter.calculateTotalFare(mockTripJackResponse);
    
    expect(result).toBeDefined();
    expect(result.totalAmount).toBe(5700);
    expect(result.baseFare).toBe(4500);
    expect(result.taxes).toBe(1200);
    expect(result.currency).toBe('INR');
  });

  test('validateResponse should validate response structure', () => {
    const result = TripJackResponseAdapter.validateResponse(mockTripJackResponse);
    
    expect(result.isValid).toBe(true);
    expect(result.errors).toHaveLength(0);
  });

  test('validateResponse should detect invalid response', () => {
    const invalidResponse = { Trips: [] } as TripJackApiDataInfo;
    const result = TripJackResponseAdapter.validateResponse(invalidResponse);
    
    expect(result.isValid).toBe(false);
    expect(result.errors.length).toBeGreaterThan(0);
  });
});

describe('Flight Details Integration', () => {
  test('FlightDetailsEmitData structure should be valid', () => {
    expect(mockFlightDetailsBody.TripType).toBe('ON');
    expect(mockFlightDetailsBody.Trips).toHaveLength(1);
    expect(mockFlightDetailsBody.Trips[0]).toHaveLength(1);
    expect(mockFlightDetailsBody.Trips[0][0].TUI).toBe('test_tui_123');
    expect(mockFlightDetailsBody.Trips[0][0].Amount).toBe(5700);
  });

  test('Round trip FlightDetailsEmitData should have correct structure', () => {
    const roundTripData: FlightDetailsEmitData = {
      TripType: 'RT',
      Trips: [
        [{ TUI: 'tui1', Amount: 5700, Index: 'idx1', OrderID: 1, ChannelCode: null }],
        [{ TUI: 'tui2', Amount: 6200, Index: 'idx2', OrderID: 2, ChannelCode: null }]
      ]
    };

    expect(roundTripData.TripType).toBe('RT');
    expect(roundTripData.Trips).toHaveLength(2);
    expect(roundTripData.Trips[0][0].Amount).toBe(5700);
    expect(roundTripData.Trips[1][0].Amount).toBe(6200);
  });
});

// Export test data for use in other tests
export { mockTripJackResponse, mockFlightDetailsBody };
