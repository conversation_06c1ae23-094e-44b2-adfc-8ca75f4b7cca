import React, { useState, useEffect } from "react";
import {
  Segment<PERSON>eader,
  SelectedFlightSummary,
  TripSummary,
  type MultiCitySegment,
  type MultiCityFlight
} from './components';
import { OneWayFlightList } from '../';
import type { Flight, Filters, SortBy, Airline } from '../types';
import { TIME_SLOTS, SORT_OPTIONS, DEFAULT_FILTERS } from '../types';

const MultiCityList: React.FC = () => {
  const [segments, setSegments] = useState<MultiCitySegment[]>([]);
  const [selectedFlights, setSelectedFlights] = useState<Record<string, MultiCityFlight>>({});
  const [totalPrice, setTotalPrice] = useState<number>(0);
  const [collapsedSegments, setCollapsedSegments] = useState<Record<string, boolean>>({});
  const [showAdvancedOptions, setShowAdvancedOptions] = useState<boolean>(false);

  // State for OneWayFlightList components
  const [segmentSortBy, setSegmentSortBy] = useState<Record<string, SortBy>>({});
  const [segmentFiltersState, setSegmentFiltersState] = useState<Record<string, Filters>>({});
  const [segmentShowAllAirlines, setSegmentShowAllAirlines] = useState<Record<string, boolean>>({});
  const [segmentIsDragging, setSegmentIsDragging] = useState<Record<string, 'min' | 'max' | null>>({});
  const [selectedFlightDetails, setSelectedFlightDetails] = useState<Flight | null>(null);
  const [showFlightDetails, setShowFlightDetails] = useState(false);

  // Transform MultiCityFlight to Flight format for OneWayFlightList
  const transformMultiCityFlightToFlight = (multiCityFlight: MultiCityFlight): Flight => {
    return {
      id: multiCityFlight.id,
      airline: multiCityFlight.airline.name,
      alliance: 'Star Alliance', // Default value, could be mapped from airline
      corporateRate: true,
      departure: {
        city: `${multiCityFlight.departure.city} (${multiCityFlight.departure.code})`,
        time: multiCityFlight.departure.time,
        terminal: multiCityFlight.departure.terminal
      },
      arrival: {
        city: `${multiCityFlight.arrival.city} (${multiCityFlight.arrival.code})`,
        time: multiCityFlight.arrival.time,
        terminal: multiCityFlight.arrival.terminal
      },
      duration: multiCityFlight.duration,
      stops: multiCityFlight.stops,
      price: multiCityFlight.fare.price,
      originalPrice: multiCityFlight.fare.price + 100, // Mock original price
      seatsAvailable: 12, // Mock value
      aircraft: multiCityFlight.aircraft,
      amenities: ['Wifi', 'Power', 'Entertainment'], // Mock amenities
      fareTypes: [multiCityFlight.fare.type],
      carbonEmission: '1.8 tons', // Mock value
      baggageAllowance: multiCityFlight.fare.baggage
    };
  };



  // Mock data initialization
  useEffect(() => {
    const mockSegments: MultiCitySegment[] = [
      {
        id: "seg1",
        origin: { city: "New York", code: "JFK" },
        destination: { city: "London", code: "LHR" },
        date: "July 2, 2025",
        flights: [
          {
            id: "f1-seg1",
            airline: { name: "British Airways", code: "BA" },
            flightNumber: "BA178",
            departure: {
              city: "New York",
              code: "JFK",
              time: "19:30",
              date: "July 2, 2025",
              terminal: "T4",
            },
            arrival: {
              city: "London",
              code: "LHR",
              time: "07:45",
              date: "July 3, 2025",
              terminal: "T5",
            },
            duration: "7h 15m",
            stops: 0,
            stopDetails: [],
            aircraft: "Boeing 777-300ER",
            fare: {
              type: "Business",
              price: 2450,
              currency: "USD",
              refundable: true,
              baggage: "2 x 32kg",
            },
          },
          {
            id: "f2-seg1",
            airline: { name: "Delta Airlines", code: "DL" },
            flightNumber: "DL4",
            departure: {
              city: "New York",
              code: "JFK",
              time: "21:15",
              date: "July 2, 2025",
              terminal: "T4",
            },
            arrival: {
              city: "London",
              code: "LHR",
              time: "09:30",
              date: "July 3, 2025",
              terminal: "T3",
            },
            duration: "7h 15m",
            stops: 0,
            stopDetails: [],
            aircraft: "Airbus A330-900neo",
            fare: {
              type: "Economy Plus",
              price: 1250,
              currency: "USD",
              refundable: false,
              baggage: "1 x 23kg",
            },
          },
        ],
      },
      {
        id: "seg2",
        origin: { city: "London", code: "LHR" },
        destination: { city: "Paris", code: "CDG" },
        date: "July 5, 2025",
        flights: [
          {
            id: "f1-seg2",
            airline: { name: "British Airways", code: "BA" },
            flightNumber: "BA304",
            departure: {
              city: "London",
              code: "LHR",
              time: "08:30",
              date: "July 5, 2025",
              terminal: "T5",
            },
            arrival: {
              city: "Paris",
              code: "CDG",
              time: "10:45",
              date: "July 5, 2025",
              terminal: "2E",
            },
            duration: "1h 15m",
            stops: 0,
            stopDetails: [],
            aircraft: "Airbus A320neo",
            fare: {
              type: "Business",
              price: 450,
              currency: "USD",
              refundable: true,
              baggage: "2 x 32kg",
            },
          },
          {
            id: "f2-seg2",
            airline: { name: "Air France", code: "AF" },
            flightNumber: "AF1681",
            departure: {
              city: "London",
              code: "LHR",
              time: "10:15",
              date: "July 5, 2025",
              terminal: "T4",
            },
            arrival: {
              city: "Paris",
              code: "CDG",
              time: "12:30",
              date: "July 5, 2025",
              terminal: "2F",
            },
            duration: "1h 15m",
            stops: 0,
            stopDetails: [],
            aircraft: "Airbus A320",
            fare: {
              type: "Economy",
              price: 220,
              currency: "USD",
              refundable: false,
              baggage: "1 x 23kg",
            },
          },
        ],
      },
      {
        id: "seg3",
        origin: { city: "Paris", code: "CDG" },
        destination: { city: "Rome", code: "FCO" },
        date: "July 8, 2025",
        flights: [
          {
            id: "f1-seg3",
            airline: { name: "Air France", code: "AF" },
            flightNumber: "AF1204",
            departure: {
              city: "Paris",
              code: "CDG",
              time: "10:30",
              date: "July 8, 2025",
              terminal: "2F",
            },
            arrival: {
              city: "Rome",
              code: "FCO",
              time: "12:35",
              date: "July 8, 2025",
              terminal: "3",
            },
            duration: "2h 05m",
            stops: 0,
            stopDetails: [],
            aircraft: "Airbus A320",
            fare: {
              type: "Business",
              price: 520,
              currency: "USD",
              refundable: true,
              baggage: "2 x 32kg",
            },
          },
          {
            id: "f2-seg3",
            airline: { name: "Alitalia", code: "AZ" },
            flightNumber: "AZ335",
            departure: {
              city: "Paris",
              code: "CDG",
              time: "14:15",
              date: "July 8, 2025",
              terminal: "2F",
            },
            arrival: {
              city: "Rome",
              code: "FCO",
              time: "16:20",
              date: "July 8, 2025",
              terminal: "1",
            },
            duration: "2h 05m",
            stops: 0,
            stopDetails: [],
            aircraft: "Airbus A320",
            fare: {
              type: "Economy",
              price: 280,
              currency: "USD",
              refundable: false,
              baggage: "1 x 23kg",
            },
          },
        ],
      },
    ];

    setSegments(mockSegments);

    // Initialize state for OneWayFlightList components
    const initialSortBy: Record<string, SortBy> = {};
    const initialFiltersState: Record<string, Filters> = {};
    const initialShowAllAirlines: Record<string, boolean> = {};
    const initialIsDragging: Record<string, 'min' | 'max' | null> = {};

    mockSegments.forEach(segment => {
      initialSortBy[segment.id] = 'recommended';
      initialFiltersState[segment.id] = DEFAULT_FILTERS;
      initialShowAllAirlines[segment.id] = false;
      initialIsDragging[segment.id] = null;
    });

    setSegmentSortBy(initialSortBy);
    setSegmentFiltersState(initialFiltersState);
    setSegmentShowAllAirlines(initialShowAllAirlines);
    setSegmentIsDragging(initialIsDragging);
  }, []);

  // Calculate total price when selected flights change
  useEffect(() => {
    let total = 0;
    Object.values(selectedFlights).forEach((flight) => {
      total += flight.fare.price;
    });
    setTotalPrice(total);
  }, [selectedFlights]);

  const handleSelectFlight = (segmentId: string, flight: MultiCityFlight) => {
    setSelectedFlights((prev) => {
      const newSelected = {
        ...prev,
        [segmentId]: flight,
      };

      // Find next unselected segment and scroll to it
      setTimeout(() => {
        const nextSegment = segments.find((seg) => !newSelected[seg.id]);
        if (nextSegment) {
          const element = document.getElementById(`segment-${nextSegment.id}`);
          if (element) {
            element.scrollIntoView({ behavior: "smooth", block: "center" });
          }
        }
      }, 100);

      return newSelected;
    });

    // Collapse the segment after selection
    setCollapsedSegments((prev) => ({
      ...prev,
      [segmentId]: true,
    }));
  };

  const handleRemoveSelectedFlight = (segmentId: string) => {
    setSelectedFlights((prev) => {
      const newSelected = { ...prev };
      delete newSelected[segmentId];
      return newSelected;
    });
    setCollapsedSegments((prev) => ({
      ...prev,
      [segmentId]: false,
    }));
  };

  const toggleSegmentCollapse = (segmentId: string) => {
    setCollapsedSegments((prev) => ({
      ...prev,
      [segmentId]: !prev[segmentId],
    }));
  };

  // Handlers for OneWayFlightList components
  const handleSegmentSortChange = (segmentId: string, sortBy: SortBy) => {
    setSegmentSortBy(prev => ({
      ...prev,
      [segmentId]: sortBy
    }));
  };

  const handleSegmentFiltersChange = (segmentId: string, filters: Filters) => {
    setSegmentFiltersState(prev => ({
      ...prev,
      [segmentId]: filters
    }));
  };

  const handleSegmentToggleAirlines = (segmentId: string) => {
    setSegmentShowAllAirlines(prev => ({
      ...prev,
      [segmentId]: !prev[segmentId]
    }));
  };

  const handleSegmentDragStart = (segmentId: string, handle: 'min' | 'max') => {
    setSegmentIsDragging(prev => ({
      ...prev,
      [segmentId]: handle
    }));
  };

  const handleFlightSelect = (segmentId: string, flight: Flight) => {
    // Convert back to MultiCityFlight format for consistency
    const segment = segments.find(s => s.id === segmentId);
    const originalFlight = segment?.flights.find(f => f.id === flight.id);
    if (originalFlight) {
      handleSelectFlight(segmentId, originalFlight);
    }
  };

  const handleShowDetails = (flight: Flight) => {
    setSelectedFlightDetails(flight);
    setShowFlightDetails(true);
  };

  const handleCloseDetails = () => {
    setShowFlightDetails(false);
    setSelectedFlightDetails(null);
  };

  // Get airlines for a specific segment
  const getSegmentAirlines = (segment: MultiCitySegment): Airline[] => {
    const uniqueAirlines = Array.from(
      new Set(segment.flights.map(f => f.airline.name))
    ).map(name => ({
      name,
      rating: 4.5 // Mock rating
    }));
    return uniqueAirlines;
  };



  const handleBookItinerary = () => {
    console.log('Booking itinerary:', selectedFlights);
    // Implement booking logic
  };

  const handleEmailItinerary = () => {
    console.log('Emailing itinerary:', selectedFlights);
    // Implement email logic
  };

  return (
    <div className="min-h-screen bg-gray-50 text-gray-900">
      {/* Header */}
      <header className="bg-white shadow-md py-4 px-6 sticky top-0 z-10">
        <div className="max-w-7xl mx-auto flex justify-between items-center">
          <div className="flex items-center">
            <div className="text-xl font-bold text-blue-800">TravelPro</div>
            <div className="ml-10 flex items-center space-x-2">
              {segments.map((segment, index) => (
                <React.Fragment key={segment.id}>
                  <div className="font-medium">{segment.origin.code}</div>
                  {index < segments.length - 1 && (
                    <i className="fas fa-arrow-right text-gray-400 mx-1"></i>
                  )}
                </React.Fragment>
              ))}
              {segments.length > 0 && (
                <div className="font-medium">
                  {segments[segments.length - 1].destination.code}
                </div>
              )}
            </div>
          </div>
          <div className="flex items-center space-x-4">
            <button className="bg-gray-100 hover:bg-gray-200 text-gray-700 px-3 py-1 rounded-button whitespace-nowrap cursor-pointer">
              <i className="fas fa-save mr-2"></i>Save
            </button>
            <button className="bg-gray-100 hover:bg-gray-200 text-gray-700 px-3 py-1 rounded-button whitespace-nowrap cursor-pointer">
              <i className="fas fa-share-alt mr-2"></i>Share
            </button>
            <div className="relative">
              <button className="bg-gray-100 hover:bg-gray-200 text-gray-700 px-3 py-1 rounded-button whitespace-nowrap cursor-pointer">
                <i className="fas fa-user-tie mr-2"></i>Agent Tools
              </button>
            </div>
          </div>
        </div>
      </header>

      <div className="max-w-[1920px] mx-auto px-4 lg:px-6 xl:px-8 py-4 lg:py-6">
        <div className="flex gap-4 lg:gap-6 xl:gap-8">
          {/* Main content - 70% width for segments */}
          <div className="w-[70%] space-y-6">
          {segments.map((segment) => {
            const isCollapsed = collapsedSegments[segment.id];
            const selectedFlight = selectedFlights[segment.id];

            return (
              <div
                id={`segment-${segment.id}`}
                key={segment.id}
                className="bg-white rounded-xl shadow-lg border border-gray-100 overflow-hidden hover:shadow-xl transition-shadow duration-300"
              >
                {/* Segment Header */}
                <SegmentHeader
                  segment={segment}
                  selectedFlight={selectedFlight}
                  isCollapsed={isCollapsed}
                  onToggleCollapse={() => toggleSegmentCollapse(segment.id)}
                />

                {/* Selected Flight Summary (when collapsed) */}
                {isCollapsed && selectedFlight && (
                  <SelectedFlightSummary
                    flight={selectedFlight}
                    onRemove={() => handleRemoveSelectedFlight(segment.id)}
                  />
                )}

                {/* Flight List with integrated filters - only show when not collapsed */}
                {!isCollapsed && (
                  <div className="pt-4">
                    <OneWayFlightList
                      flights={segment.flights.map(transformMultiCityFlightToFlight)}
                      sortBy={segmentSortBy[segment.id] || 'recommended'}
                      onSortChange={(sortBy) => handleSegmentSortChange(segment.id, sortBy)}
                      onShowFilters={() => {}} // Mobile filters not needed here
                      sortOptions={SORT_OPTIONS}
                      selectedFlight={selectedFlight ? transformMultiCityFlightToFlight(selectedFlight) : null}
                      showFlightDetails={showFlightDetails}
                      onShowDetails={handleShowDetails}
                      onCloseDetails={handleCloseDetails}
                      onFlightSelect={(flight) => handleFlightSelect(segment.id, flight)}
                      filters={segmentFiltersState[segment.id] || DEFAULT_FILTERS}
                      onFiltersChange={(filters) => handleSegmentFiltersChange(segment.id, filters)}
                      timeSlots={TIME_SLOTS}
                      airlines={getSegmentAirlines(segment)}
                      showAllAirlines={segmentShowAllAirlines[segment.id] || false}
                      onToggleAirlines={() => handleSegmentToggleAirlines(segment.id)}
                      isDragging={segmentIsDragging[segment.id] || null}
                      onDragStart={(handle) => handleSegmentDragStart(segment.id, handle)}
                    />
                  </div>
                )}
              </div>
            );
          })}
        </div>

          {/* Trip Summary Sidebar - 30% width */}
          <div className="w-[30%] sticky top-24 self-start">
          <TripSummary
            segments={segments}
            selectedFlights={selectedFlights}
            totalPrice={totalPrice}
            onBookItinerary={handleBookItinerary}
            onEmailItinerary={handleEmailItinerary}
          />

          {/* Agent Tools Card */}
          <div className="bg-white rounded-lg shadow-md overflow-hidden mt-4">
            <div className="bg-gray-800 text-white p-3">
              <h3 className="font-medium">Agent Tools</h3>
            </div>
            <div className="p-3 text-sm">
              <div className="flex justify-between mb-2">
                <div>PNR Status</div>
                <div className="text-green-600">Ready to book</div>
              </div>
              <div className="flex justify-between mb-2">
                <div>Commission</div>
                <div>${(totalPrice * 0.07).toFixed(2)} (7%)</div>
              </div>
              <div className="flex justify-between">
                <div>Fare Expiry</div>
                <div>24h remaining</div>
              </div>
              <button
                onClick={() => setShowAdvancedOptions(true)}
                className="w-full mt-3 bg-gray-100 hover:bg-gray-200 text-gray-700 py-1 text-sm rounded-button whitespace-nowrap cursor-pointer"
              >
                <i className="fas fa-cog mr-1"></i>Advanced Options
              </button>
            </div>
          </div>
        </div>
        </div>
      </div>
    </div>
  );
};

export default MultiCityList;
