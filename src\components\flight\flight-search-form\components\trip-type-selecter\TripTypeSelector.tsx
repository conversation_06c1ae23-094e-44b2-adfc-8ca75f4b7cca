import React from 'react';
import type { TripTypeValue } from '../../../../../models/flight/common-flight.model';

interface TripType {
  id: TripTypeValue;
  label: string;
}

interface TripTypeSelectorProps {
  tripTypes: TripType[];
  selectedTripType: TripTypeValue;
  onTripTypeChange: (tripType: TripTypeValue) => void;
  compact?: boolean;
}

const TripTypeSelector: React.FC<TripTypeSelectorProps> = ({
  tripTypes,
  selectedTripType,
  onTripTypeChange,
  compact = false
}) => {
  return (
    <div className={`flex flex-wrap gap-2 md:gap-4 ${compact ? 'mb-4' : 'mb-6'} overflow-x-auto`}>
      {tripTypes.map(type => (
        <button
          key={type.id}
          className={`!rounded-button ${compact ? 'px-3 py-2' : 'px-3 md:px-5 py-2 md:py-2.5'} text-sm font-medium cursor-pointer whitespace-nowrap ${
            selectedTripType === type.id
              ? 'bg-blue-600 text-white'
              : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
          }`}
          onClick={() => onTripTypeChange(type.id)}
        >
          {type.label}
        </button>
      ))}
    </div>
  );
};

export default TripTypeSelector;
