import React, { useState, useRef, useEffect, useCallback } from 'react';
import TravelClassPicker from '../flight-search-form/components/travel-class/TravelClassPicker';
import AirportSearch from '../flight-search-form/components/airport-search/AirportSearch';
import ModifyCalendarModal from './components/ModifyCalendarModal';
import { useFlightSearch } from '../../../contexts/FlightSearchContext';
import { parseAirportString, formatAirportDisplay } from '../../../utils/airportUtils';
import { getAirportApi, getPopularAirportApi } from '../../../api/flight/search-service';
import { getPopularAirportFromLocal } from '../../../helper/flight/flight-search-helper';
import type { Airport } from '../../../models/flight/common-flight.model';
import './ModifySearch.scss';

// Types
export interface ModifySearchFormProps {
    onSearch: (searchData: any) => void;
    onClose?: () => void;
    isModal?: boolean;
    isOpen?: boolean; // For compatibility with modal usage pattern
}

const ModifySearchForm: React.FC<ModifySearchFormProps> = ({
    onSearch,
    onClose,
    isModal = true,
    isOpen = true
}) => {
    const { currentSearch, updateSearch } = useFlightSearch();

    // Define popular airports locally (same as in FlightSearchForm)
    const popularAirports = [
        { code: 'JFK', city: 'New York', name: 'John F. Kennedy International Airport', country: 'United States' },
        { code: 'LHR', city: 'London', name: 'Heathrow Airport', country: 'United Kingdom' },
        { code: 'LAX', city: 'Los Angeles', name: 'Los Angeles International Airport', country: 'United States' },
        { code: 'CDG', city: 'Paris', name: 'Charles de Gaulle Airport', country: 'France' },
        { code: 'DXB', city: 'Dubai', name: 'Dubai International Airport', country: 'United Arab Emirates' },
        { code: 'SIN', city: 'Singapore', name: 'Changi Airport', country: 'Singapore' },
        { code: 'HKG', city: 'Hong Kong', name: 'Hong Kong International Airport', country: 'Hong Kong' },
        { code: 'NRT', city: 'Tokyo', name: 'Narita International Airport', country: 'Japan' },
        { code: 'SYD', city: 'Sydney', name: 'Kingsford Smith Airport', country: 'Australia' },
        { code: 'FRA', city: 'Frankfurt', name: 'Frankfurt Airport', country: 'Germany' }
    ];
    
    // State for dropdowns
    const [showFromDropdown, setShowFromDropdown] = useState(false);
    const [showToDropdown, setShowToDropdown] = useState(false);
    const [showCalendar, setShowCalendar] = useState(false);
    const [showTravelersClassDropdown, setShowTravelersClassDropdown] = useState(false);
    const [dateSelectionState, setDateSelectionState] = useState<'departure' | 'return' | null>(null);

    // Airport search state - separate for From and To
    const [fromAirportSearchTerm, setFromAirportSearchTerm] = useState<string>("");
    const [toAirportSearchTerm, setToAirportSearchTerm] = useState<string>("");
    const [backupAirportList, setBackupAirportList] = useState<Airport[]>([]);
    const [fromAirportList, setFromAirportList] = useState<Airport[]>([]);
    const [toAirportList, setToAirportList] = useState<Airport[]>([]);
    const [isFromAirportShimmer, setIsFromAirportShimmer] = useState<boolean>(false);
    const [isToAirportShimmer, setIsToAirportShimmer] = useState<boolean>(false);

    // Calendar state
    const [currentMonth, setCurrentMonth] = useState(new Date());
    const [nextMonth, setNextMonth] = useState(new Date(new Date().setMonth(new Date().getMonth() + 1)));
    const [tripDays, setTripDays] = useState<string>('');
    
    // Refs for positioning dropdowns
    const fromRef = useRef<HTMLDivElement>(null);
    const toRef = useRef<HTMLDivElement>(null);
    const dateRef = useRef<HTMLDivElement>(null);
    const travelersRef = useRef<HTMLDivElement>(null);
    const calendarRef = useRef<HTMLDivElement>(null);
    
    // Get current values from context
    const fromAirport = currentSearch?.from ? formatAirportDisplay(currentSearch.from) : '';
    const toAirport = currentSearch?.to ? formatAirportDisplay(currentSearch.to) : '';
    const departureDate = currentSearch?.depart ? currentSearch.depart.toLocaleDateString('en-US', { 
        weekday: 'short', 
        month: 'short', 
        day: 'numeric' 
    }) : '';
    const returnDate = currentSearch?.return ? currentSearch.return.toLocaleDateString('en-US', { 
        weekday: 'short', 
        month: 'short', 
        day: 'numeric' 
    }) : '';
    const passengers = currentSearch?.passengers || { adults: 1, children: 0, infants: 0 };
    const selectedClass = currentSearch?.class || 'economy';

    // Convert passengers to TravelClassPicker format
    const travelClassPassengers = {
        adult: passengers.adults,
        child: passengers.children,
        infant: passengers.infants
    };

    // Convert class to TravelClassPicker format
    const classToTravelClassMap = {
        'economy': 'E' as const,
        'premiumEconomy': 'PE' as const,
        'business': 'B' as const,
        'first': 'F' as const
    };
    const travelClassSelectedClass = classToTravelClassMap[selectedClass];

    // Calculate total passengers
    const totalPassengers = passengers.adults + passengers.children + passengers.infants;
    const passengersText = `${totalPassengers} Traveler${totalPassengers > 1 ? 's' : ''}`;

    // Class display mapping
    const classDisplayMap = {
        economy: 'Economy',
        premiumEconomy: 'Premium Economy',
        business: 'Business',
        first: 'First'
    };

    // Handle airport selection
    const handleFromAirportSelect = (airport: Airport) => {
        const airportData = {
            city: airport.city_name || airport.city,
            airport: airport.name,
            iata: airport.code,
            country: airport.country,
            airportOpen: false,
        };
        updateSearch({ from: airportData });
        setShowFromDropdown(false);
        setFromAirportSearchTerm("");
    };

    const handleToAirportSelect = (airport: Airport) => {
        const airportData = {
            city: airport.city_name || airport.city,
            airport: airport.name,
            iata: airport.code,
            country: airport.country,
            airportOpen: false,
        };
        updateSearch({ to: airportData });
        setShowToDropdown(false);
        setToAirportSearchTerm("");
    };

    // Calendar helper functions
    const generateCalendarDays = (year: number, month: number) => {
        const firstDay = new Date(year, month, 1);
        const lastDay = new Date(year, month + 1, 0);
        const daysInMonth = lastDay.getDate();
        const startingDayOfWeek = (firstDay.getDay() + 6) % 7; // Monday = 0

        const days = [];

        // Add empty cells for days before the first day of the month
        for (let i = 0; i < startingDayOfWeek; i++) {
            days.push(null);
        }

        // Add days of the month
        for (let day = 1; day <= daysInMonth; day++) {
            const date = new Date(year, month, day);
            const dateString = `${year}-${String(month + 1).padStart(2, '0')}-${String(day).padStart(2, '0')}`;
            days.push({
                day,
                date: dateString,
                price: Math.floor(Math.random() * 500) + 200, // Mock price
                uniqueKey: `${year}-${month}-${day}` // Add unique key for React
            });
        }

        return days;
    };

    const isDateInPast = (dateString: string) => {
        const today = new Date();
        today.setHours(0, 0, 0, 0);
        const [year, month, day] = dateString.split('-').map(Number);
        const date = new Date(year, month - 1, day);
        return date < today;
    };

    const isDateSelected = (dateString: string) => {
        const selectedDates = {
            departure: currentSearch?.depart ? currentSearch.depart.toISOString().split('T')[0] : '',
            return: currentSearch?.return ? currentSearch.return.toISOString().split('T')[0] : ''
        };
        return dateString === selectedDates.departure || dateString === selectedDates.return;
    };

    const isDateInRange = (dateString: string) => {
        const selectedDates = {
            departure: currentSearch?.depart ? currentSearch.depart.toISOString().split('T')[0] : '',
            return: currentSearch?.return ? currentSearch.return.toISOString().split('T')[0] : ''
        };

        if (!selectedDates.departure || !selectedDates.return) return false;

        const date = new Date(dateString);
        const departure = new Date(selectedDates.departure);
        const returnDate = new Date(selectedDates.return);

        return date > departure && date < returnDate;
    };

    // Handle date selection with improved round trip logic
    const handleDateSelect = (dateString: string) => {
        const [year, month, day] = dateString.split('-').map(Number);
        const date = new Date(year, month - 1, day);

        if (!dateSelectionState || dateSelectionState === 'departure') {
            // Clear any existing dates and set new departure
            updateSearch({ depart: date, return: null });

            if (currentSearch?.tripType === 'roundTrip') {
                // For round trip, move to return date selection
                setDateSelectionState('return');
                // Don't close calendar, wait for return date
            } else {
                // For one way, close calendar
                setShowCalendar(false);
                setDateSelectionState(null);
            }
        } else if (dateSelectionState === 'return') {
            // Validate return date is after departure
            const departureDate = currentSearch?.depart;
            if (departureDate && date <= departureDate) {
                // If return date is not after departure, show error and don't close
                alert('Return date must be after departure date. Please select a valid return date.');
                return;
            }

            updateSearch({ return: date });
            setShowCalendar(false);
            setDateSelectionState(null);
        }
    };

    // Handle calendar close with validation
    const handleCalendarClose = () => {
        if (currentSearch?.tripType === 'roundTrip' && dateSelectionState === 'return' && !currentSearch?.return) {
            // For round trip, if we're waiting for return date and none is selected, ask user
            const confirmClose = window.confirm(
                'You haven\'t selected a return date yet. Do you want to close the calendar without selecting a return date? This will change your trip to one-way.'
            );

            if (confirmClose) {
                // Convert to one-way trip
                updateSearch({ tripType: 'oneWay', return: null });
                setShowCalendar(false);
                setDateSelectionState(null);
            }
            // If they don't confirm, keep calendar open
        } else {
            // Normal close
            setShowCalendar(false);
            setDateSelectionState(null);
        }
    };

    // Calendar navigation
    const handlePrevMonth = () => {
        const newMonth = new Date(currentMonth);
        newMonth.setMonth(newMonth.getMonth() - 1);
        setCurrentMonth(newMonth);
    };

    const handleNextMonth = () => {
        const newMonth = new Date(currentMonth);
        newMonth.setMonth(newMonth.getMonth() + 1);
        setCurrentMonth(newMonth);
    };

    const handleDaysInput = (days: string) => {
        setTripDays(days);
        if (days && currentSearch?.depart) {
            const returnDate = new Date(currentSearch.depart);
            returnDate.setDate(returnDate.getDate() + parseInt(days));
            updateSearch({ return: returnDate });
        }
    };

    // Handle travelers and class changes
    const handlePassengerChange = (type: 'adult' | 'child' | 'infant', value: number) => {
        const newPassengers = { ...passengers };
        // Map TravellerType to context passenger types
        const typeMap = {
            'adult': 'adults' as const,
            'child': 'children' as const,
            'infant': 'infants' as const
        };

        updateSearch({
            passengers: {
                ...newPassengers,
                [typeMap[type]]: value
            }
        });
    };

    const handleClassChange = (newClass: 'E' | 'PE' | 'B' | 'F') => {
        // Map ClassType to context class types
        const classMap = {
            'E': 'economy' as const,
            'PE': 'premiumEconomy' as const,
            'B': 'business' as const,
            'F': 'first' as const
        };
        updateSearch({ class: classMap[newClass] });
    };

    // Airport search functions
    const getPopularAirportList = useCallback(async () => {
        setIsFromAirportShimmer(true);
        setIsToAirportShimmer(true);
        try {
            const response = await getPopularAirportApi();
            setFromAirportList(response.airports);
            setToAirportList(response.airports);
            setBackupAirportList(response.airports);
            localStorage.setItem("popularAirport", JSON.stringify(response.airports));
            setIsFromAirportShimmer(false);
            setIsToAirportShimmer(false);
        } catch (error) {
            console.log(error);
            setIsFromAirportShimmer(false);
            setIsToAirportShimmer(false);
        }
    }, []);

    const handleFromAirportSearch = useCallback(
        async (searchTerm: string) => {
            setIsFromAirportShimmer(true);
            try {
                const result = await getAirportApi(searchTerm);
                setFromAirportList(result.results);
                setIsFromAirportShimmer(false);

                // Filter new airports not already in backupAirportList
                const newAirports = result.results.filter(
                    (airport) =>
                        !backupAirportList.some(
                            (existing) => existing.code === airport.code
                        )
                );

                if (newAirports.length > 0) {
                    setBackupAirportList((prev) => [...prev, ...newAirports]);
                }
            } catch (error) {
                console.log(error);
                setIsFromAirportShimmer(false);
            }
        },
        [backupAirportList]
    );

    const handleToAirportSearch = useCallback(
        async (searchTerm: string) => {
            setIsToAirportShimmer(true);
            try {
                const result = await getAirportApi(searchTerm);
                setToAirportList(result.results);
                setIsToAirportShimmer(false);

                // Filter new airports not already in backupAirportList
                const newAirports = result.results.filter(
                    (airport) =>
                        !backupAirportList.some(
                            (existing) => existing.code === airport.code
                        )
                );

                if (newAirports.length > 0) {
                    setBackupAirportList((prev) => [...prev, ...newAirports]);
                }
            } catch (error) {
                console.log(error);
                setIsToAirportShimmer(false);
            }
        },
        [backupAirportList]
    );

    const handleFromAirportSearchChange = useCallback(
        async (e: React.ChangeEvent<HTMLInputElement>) => {
            const searchTerm = e.target.value;
            setFromAirportSearchTerm(searchTerm);

            if (!searchTerm.trim()) {
                // If no input, show all from backup list
                setFromAirportList(backupAirportList);
                return;
            }

            const filteredLocal = backupAirportList.filter(
                (airport) =>
                    airport.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                    airport.city_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                    airport.code.toLowerCase().includes(searchTerm.toLowerCase())
            );

            if (filteredLocal.length >= 5) {
                setFromAirportList(filteredLocal.slice(0, 10));
            } else {
                await handleFromAirportSearch(searchTerm);
            }
        },
        [backupAirportList, handleFromAirportSearch]
    );

    const handleToAirportSearchChange = useCallback(
        async (e: React.ChangeEvent<HTMLInputElement>) => {
            const searchTerm = e.target.value;
            setToAirportSearchTerm(searchTerm);

            if (!searchTerm.trim()) {
                // If no input, show all from backup list
                setToAirportList(backupAirportList);
                return;
            }

            const filteredLocal = backupAirportList.filter(
                (airport) =>
                    airport.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                    airport.city_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                    airport.code.toLowerCase().includes(searchTerm.toLowerCase())
            );

            if (filteredLocal.length >= 5) {
                setToAirportList(filteredLocal.slice(0, 10));
            } else {
                await handleToAirportSearch(searchTerm);
            }
        },
        [backupAirportList, handleToAirportSearch]
    );

    // Handle search
    const handleSearch = () => {
        if (currentSearch) {
            onSearch(currentSearch);
        }
        if (onClose) {
            onClose();
        }
    };

    // Update next month when current month changes
    useEffect(() => {
        const nextMonthDate = new Date(currentMonth);
        nextMonthDate.setMonth(nextMonthDate.getMonth() + 1);
        setNextMonth(nextMonthDate);
    }, [currentMonth]);

    // Close dropdowns when clicking outside
    useEffect(() => {
        const handleClickOutside = (event: MouseEvent) => {
            const target = event.target as Element;

            // Helper function to check if target is inside any dropdown
            const isInsideAnyDropdown = () => {
                // Check if target is inside any ref container
                const insideRefContainer = (
                    (fromRef.current && fromRef.current.contains(target)) ||
                    (toRef.current && toRef.current.contains(target)) ||
                    (dateRef.current && dateRef.current.contains(target)) ||
                    (travelersRef.current && travelersRef.current.contains(target))
                );

                // Also check if target is inside any dropdown menu (fallback)
                const insideDropdownMenu = target.closest('.dropdown-menu, .airport-dropdown, .calendar-dropdown, .travelers-dropdown');

                return insideRefContainer || !!insideDropdownMenu;
            };

            // If click is outside all dropdowns, close all open dropdowns
            if (!isInsideAnyDropdown()) {
                if (showFromDropdown) {
                    setShowFromDropdown(false);
                    setFromAirportSearchTerm("");
                }
                if (showToDropdown) {
                    setShowToDropdown(false);
                    setToAirportSearchTerm("");
                }
                if (showCalendar) {
                    setShowCalendar(false);
                    setDateSelectionState(null);
                }
                if (showTravelersClassDropdown) {
                    setShowTravelersClassDropdown(false);
                }
            }
        };

        // Only add listener if any dropdown is open
        if (showFromDropdown || showToDropdown || showCalendar || showTravelersClassDropdown) {
            document.addEventListener('mousedown', handleClickOutside);
            return () => document.removeEventListener('mousedown', handleClickOutside);
        }
    }, [showFromDropdown, showToDropdown, showCalendar, showTravelersClassDropdown]);

    // Load popular airports on component mount
    useEffect(() => {
        const popularAirport = getPopularAirportFromLocal();
        if (popularAirport && popularAirport.length > 0) {
            setFromAirportList(popularAirport);
            setToAirportList(popularAirport);
            setBackupAirportList(popularAirport);
        } else {
            getPopularAirportList();
        }
    }, [getPopularAirportList]);

    const modalContent = (
        <div className="modify-search-form">
            {/* Header - Only show in modal mode */}
            {isModal && (
                <div className="modal-header">
                    <h2 className="modal-title">Modify Search</h2>
                    {onClose && (
                        <button
                            onClick={onClose}
                            className="close-button"
                        >
                            <i className="fas fa-times"></i>
                        </button>
                    )}
                </div>
            )}

            {/* Search Fields in Horizontal Layout */}
            <div className="search-fields-container">
                {/* From Airport */}
                <div className="dropdown-container" ref={fromRef}>
                    <div className="relative">
                        <input
                            type="text"
                            className="search-field-button from-airport w-full text-left"
                            placeholder="From"
                            value={showFromDropdown ? fromAirportSearchTerm : (fromAirport || '')}
                            onChange={(e) => {
                                if (showFromDropdown) {
                                    handleFromAirportSearchChange(e);
                                }
                            }}
                            onClick={() => {
                                setShowFromDropdown(!showFromDropdown);
                                setShowToDropdown(false);
                                setShowCalendar(false);
                                setShowTravelersClassDropdown(false);
                                if (!showFromDropdown) {
                                    setFromAirportSearchTerm("");
                                }
                            }}
                            readOnly={!showFromDropdown}
                        />
                        <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                            <i className="fas fa-plane-departure icon"></i>
                        </div>
                        <div className="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                            <i className="fas fa-chevron-down chevron"></i>
                        </div>
                    </div>

                    {showFromDropdown && (
                        <div
                            className="dropdown-menu airport-dropdown max-h-80 overflow-y-auto"
                            onClick={(e) => e.stopPropagation()}
                            onMouseDown={(e) => e.stopPropagation()}
                        >
                            <AirportSearch
                                searchTerm={fromAirportSearchTerm}
                                airports={fromAirportList}
                                showDropdown={showFromDropdown}
                                onAirportSelect={handleFromAirportSelect}
                                icon="fas fa-plane-departure"
                                isLoading={isFromAirportShimmer}
                                disabledAirportCode={currentSearch?.to?.iata}
                            />
                        </div>
                    )}
                </div>

                {/* Swap Button */}
                <button
                    onClick={() => {
                        if (currentSearch?.from && currentSearch?.to) {
                            const temp = currentSearch.from;
                            updateSearch({ from: currentSearch.to, to: temp });
                        }
                    }}
                    className="swap-button"
                >
                    <i className="fas fa-exchange-alt"></i>
                </button>

                {/* To Airport */}
                <div className="dropdown-container" ref={toRef}>
                    <div className="relative">
                        <input
                            type="text"
                            className="search-field-button to-airport w-full text-left"
                            placeholder="To"
                            value={showToDropdown ? toAirportSearchTerm : (toAirport || '')}
                            onChange={(e) => {
                                if (showToDropdown) {
                                    handleToAirportSearchChange(e);
                                }
                            }}
                            onClick={() => {
                                setShowToDropdown(!showToDropdown);
                                setShowFromDropdown(false);
                                setShowCalendar(false);
                                setShowTravelersClassDropdown(false);
                                if (!showToDropdown) {
                                    setToAirportSearchTerm("");
                                }
                            }}
                            readOnly={!showToDropdown}
                        />
                        <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                            <i className="fas fa-plane-arrival icon"></i>
                        </div>
                        <div className="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                            <i className="fas fa-chevron-down chevron"></i>
                        </div>
                    </div>

                    {showToDropdown && (
                        <div
                            className="dropdown-menu airport-dropdown max-h-80 overflow-y-auto"
                            onClick={(e) => e.stopPropagation()}
                            onMouseDown={(e) => e.stopPropagation()}
                        >
                            <AirportSearch
                                searchTerm={toAirportSearchTerm}
                                airports={toAirportList}
                                showDropdown={showToDropdown}
                                onAirportSelect={handleToAirportSelect}
                                icon="fas fa-plane-arrival"
                                isLoading={isToAirportShimmer}
                                disabledAirportCode={currentSearch?.from?.iata}
                            />
                        </div>
                    )}
                </div>

                {/* Date Selection */}
                <div className="dropdown-container" ref={dateRef} data-calendar-dropdown>
                    <button
                        onClick={() => {
                            setShowCalendar(!showCalendar);
                            setDateSelectionState('departure');
                            setShowFromDropdown(false);
                            setShowToDropdown(false);
                            setShowTravelersClassDropdown(false);
                        }}
                        className="search-field-button date-selection"
                    >
                        <i className="fas fa-calendar icon"></i>
                        <span>
                            {departureDate && returnDate ? `${departureDate} - ${returnDate}` :
                             departureDate ? departureDate : 'Select Dates'}
                        </span>
                        <i className="fas fa-chevron-down chevron"></i>
                    </button>

                    {showCalendar && (
                        <div
                            className="dropdown-menu calendar-dropdown"
                            onClick={(e) => e.stopPropagation()}
                            onMouseDown={(e) => e.stopPropagation()}
                        >
                            <ModifyCalendarModal
                                showCalendar={showCalendar}
                                selectedDates={{
                                    departure: currentSearch?.depart ? currentSearch.depart.toISOString().split('T')[0] : '',
                                    return: currentSearch?.return ? currentSearch.return.toISOString().split('T')[0] : ''
                                }}
                                selectedTripType={currentSearch?.tripType || 'roundTrip'}
                                dateSelectionState={dateSelectionState}
                                currentMonth={currentMonth}
                                nextMonth={nextMonth}
                                tripDays={tripDays}
                                calendarRef={calendarRef}
                                onClose={handleCalendarClose}
                                onDateSelect={handleDateSelect}
                                onPrevMonth={handlePrevMonth}
                                onNextMonth={handleNextMonth}
                                onDaysInput={handleDaysInput}
                                generateCalendarDays={generateCalendarDays}
                                isDateInPast={isDateInPast}
                                isDateSelected={isDateSelected}
                                isDateInRange={isDateInRange}
                            />
                        </div>
                    )}
                </div>

                {/* Travelers & Class */}
                <div className="dropdown-container" ref={travelersRef} data-travelers-dropdown>
                    <button
                        onClick={() => {
                            setShowTravelersClassDropdown(!showTravelersClassDropdown);
                            setShowFromDropdown(false);
                            setShowToDropdown(false);
                            setShowCalendar(false);
                        }}
                        className="search-field-button travelers-class"
                    >
                        <i className="fas fa-user icon"></i>
                        <span>
                            {passengersText}, {classDisplayMap[selectedClass]}
                        </span>
                        <i className="fas fa-chevron-down chevron"></i>
                    </button>

                    {showTravelersClassDropdown && (
                        <div
                            className="dropdown-menu travelers-dropdown"
                            onClick={(e) => e.stopPropagation()}
                            onMouseDown={(e) => e.stopPropagation()}
                        >
                            <TravelClassPicker
                                passengers={travelClassPassengers}
                                selectedClass={travelClassSelectedClass}
                                classOptions={[
                                    { id: 'E', label: 'Economy' },
                                    { id: 'PE', label: 'Premium Economy' },
                                    { id: 'B', label: 'Business' },
                                    { id: 'F', label: 'First' }
                                ]}
                                showDropdown={true}
                                onPassengerChange={handlePassengerChange}
                                onClassChange={handleClassChange}
                                onApply={() => setShowTravelersClassDropdown(false)}
                            />
                        </div>
                    )}
                </div>

                {/* Modify Search Button */}
                <button
                    onClick={handleSearch}
                    className="modify-search-button"
                >
                    <i className="fas fa-search"></i>
                    <span>Modify Search</span>
                </button>
            </div>
        </div>
    );

    // Don't render if isOpen is false (for modal usage pattern)
    if (isModal && !isOpen) {
        return null;
    }

    if (isModal) {
        return (
            <div className="modal-overlay">
                <div className="modal-content">
                    {modalContent}
                </div>
            </div>
        );
    }

    return modalContent;
};

export default ModifySearchForm;
