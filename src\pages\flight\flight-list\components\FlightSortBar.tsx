import React from 'react';
import type { FlightSortBarProps } from './types';

const FlightSortBar: React.FC<FlightSortBarProps> = ({
  flightCount,
  sortBy,
  onSortChange,
  onShowFilters,
  sortOptions
}) => {
  return (
    <div className="flight-card-background rounded-lg shadow-sm mb-3 border flight-card-border">
      {/* Compact Header with Flight Count and Mobile Filter */}
      <div className="flex items-center justify-between px-4 py-3 border-b border-primary">
        <div className="flex items-center space-x-3">
          <h3 className="text-base font-semibold text-primary">
            {flightCount} Flights
          </h3>
          <div className="hidden md:flex items-center text-xs text-tertiary">
            <i className="fas fa-info-circle mr-1.5"></i>
            Corporate rates included
          </div>
        </div>
        <button
          className="!rounded-button lg:hidden primary-background-lightest primary-text px-3 py-1.5 text-xs font-medium hover-primary-background-lighter transition-colors duration-200 cursor-pointer whitespace-nowrap focus:outline-none focus:ring-2 focus:ring-primary-color focus:ring-offset-1"
          onClick={onShowFilters}
          aria-label="Show filters"
        >
          <i className="fas fa-filter mr-1.5"></i>
          Filters
        </button>
      </div>

      {/* Compact Sort Controls */}
      <div className="flex items-center justify-between px-4 py-2.5 neutral-background-50">
        <div className="flex items-center space-x-3">
          <span className="text-xs font-medium text-secondary">Sort:</span>
          <div className="flex items-center space-x-2 flex-wrap">
            {sortOptions.map((option) => (
              <button
                key={option.value}
                onClick={() => onSortChange(option.value)}
                className={`!rounded-button flex items-center px-3 py-1.5 text-xs transition-colors duration-200 whitespace-nowrap rounded-md border focus:outline-none focus:ring-2 focus:ring-primary-color focus:ring-offset-1 ${
                  sortBy === option.value
                    ? 'primary-background text-inverse font-medium primary-border'
                    : 'background-primary text-secondary hover-neutral-background-50 border-secondary hover:border-primary'
                }`}
                aria-pressed={sortBy === option.value}
                aria-label={`Sort by ${option.label}`}
              >
                <i className={`fas ${option.icon} ${sortBy === option.value ? 'text-inverse' : 'text-tertiary'} mr-1.5 text-xs`}></i>
                <span className="hidden sm:inline">{option.label}</span>
                <span className="sm:hidden">{option.label.charAt(0)}</span>
              </button>
            ))}
          </div>
        </div>

        <div className="flex items-center space-x-2">
          <button
            className="!rounded-button flex items-center text-xs text-secondary hover:text-primary px-2.5 py-1.5 background-primary rounded-md border border-secondary hover:border-primary transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-primary-color focus:ring-offset-1"
            aria-label="Export flight results"
          >
            <i className="fas fa-download mr-1.5 text-xs"></i>
            <span className="hidden lg:inline">Export</span>
            <span className="lg:hidden">Export</span>
          </button>
          <button
            className="!rounded-button flex items-center text-xs text-secondary hover:text-primary px-2.5 py-1.5 background-primary rounded-md border border-secondary hover:border-primary transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-primary-color focus:ring-offset-1"
            aria-label="Change view layout"
          >
            <i className="fas fa-th-list mr-1.5 text-xs"></i>
            <span className="hidden lg:inline">View</span>
          </button>
        </div>
      </div>
    </div>
  );
};

export default FlightSortBar;
