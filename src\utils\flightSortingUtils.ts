import type { FlightListItem, SortOption } from '../models/flight/flight-list-models';

/**
 * Parse duration string to minutes - exact implementation as specified
 * Handles formats like "2h 30m", "1h", "45m", "2h30m", etc.
 */
export const parseDurationToMinutes = (duration: string): number => {
  if (!duration || typeof duration !== 'string') {
    return 0;
  }

  // Remove extra spaces and normalize
  const normalized = duration.trim().toLowerCase();
  
  // Match patterns like "2h 30m", "1h", "45m", "2h30m"
  const match = normalized.match(/(?:(\d+)h)?(?:\s*(\d+)m)?/);
  
  if (!match) {
    return 0;
  }
  
  const hours = parseInt(match[1]) || 0;
  const minutes = parseInt(match[2]) || 0;
  
  return hours * 60 + minutes;
};

/**
 * Apply sorting to flight list - exact implementation as specified
 * External sort state management with applySorting function
 */
export const applySorting = (flights: FlightListItem[], sortOption: SortOption): FlightListItem[] => {
  if (!flights || flights.length === 0) {
    return [];
  }

  // Create a copy to avoid mutating the original array
  const sortedFlights = [...flights];

  switch (sortOption) {
    case 'price_low_high':
      return sortedFlights.sort((a, b) => {
        const priceA = a.price?.amount || 0;
        const priceB = b.price?.amount || 0;
        return priceA - priceB;
      });
    
    case 'price_high_low':
      return sortedFlights.sort((a, b) => {
        const priceA = a.price?.amount || 0;
        const priceB = b.price?.amount || 0;
        return priceB - priceA;
      });
    
    case 'departure_early_late':
      return sortedFlights.sort((a, b) => {
        const timeA = timeToMinutes(a.departure?.time || '00:00');
        const timeB = timeToMinutes(b.departure?.time || '00:00');
        return timeA - timeB;
      });
    
    case 'departure_late_early':
      return sortedFlights.sort((a, b) => {
        const timeA = timeToMinutes(a.departure?.time || '00:00');
        const timeB = timeToMinutes(b.departure?.time || '00:00');
        return timeB - timeA;
      });
    
    case 'arrival_early_late':
      return sortedFlights.sort((a, b) => {
        const timeA = timeToMinutes(a.arrival?.time || '00:00');
        const timeB = timeToMinutes(b.arrival?.time || '00:00');
        return timeA - timeB;
      });
    
    case 'arrival_late_early':
      return sortedFlights.sort((a, b) => {
        const timeA = timeToMinutes(a.arrival?.time || '00:00');
        const timeB = timeToMinutes(b.arrival?.time || '00:00');
        return timeB - timeA;
      });
    
    case 'duration_short_long':
      return sortedFlights.sort((a, b) => {
        const durationA = parseDurationToMinutes(a.duration || '0h 0m');
        const durationB = parseDurationToMinutes(b.duration || '0h 0m');
        return durationA - durationB;
      });
    
    case 'duration_long_short':
      return sortedFlights.sort((a, b) => {
        const durationA = parseDurationToMinutes(a.duration || '0h 0m');
        const durationB = parseDurationToMinutes(b.duration || '0h 0m');
        return durationB - durationA;
      });
    
    default:
      // Default to price low to high
      return sortedFlights.sort((a, b) => {
        const priceA = a.price?.amount || 0;
        const priceB = b.price?.amount || 0;
        return priceA - priceB;
      });
  }
};

/**
 * Convert time string (HH:MM) to minutes from midnight
 * Helper function for time-based sorting
 */
export const timeToMinutes = (timeStr: string): number => {
  if (!timeStr || typeof timeStr !== 'string') {
    return 0;
  }

  const parts = timeStr.split(':');
  if (parts.length !== 2) {
    return 0;
  }

  const hours = parseInt(parts[0]) || 0;
  const minutes = parseInt(parts[1]) || 0;
  
  return hours * 60 + minutes;
};

/**
 * Convert minutes from midnight to time string (HH:MM)
 * Helper function for time formatting
 */
export const minutesToTime = (minutes: number): string => {
  if (typeof minutes !== 'number' || minutes < 0) {
    return '00:00';
  }

  const hours = Math.floor(minutes / 60);
  const mins = minutes % 60;
  
  return `${hours.toString().padStart(2, '0')}:${mins.toString().padStart(2, '0')}`;
};

/**
 * Sort state management interface for external sort state
 */
export interface SortState {
  sortOption: SortOption;
  sortDirection: 'asc' | 'desc';
  lastSorted: number;
}

/**
 * Create default sort state
 */
export const createDefaultSortState = (): SortState => ({
  sortOption: 'price_low_high',
  sortDirection: 'asc',
  lastSorted: Date.now()
});

/**
 * Update sort state with new option
 */
export const updateSortState = (currentState: SortState, newOption: SortOption): SortState => {
  // If same option is selected, toggle direction
  if (currentState.sortOption === newOption) {
    const newDirection = currentState.sortDirection === 'asc' ? 'desc' : 'asc';
    
    // Convert to opposite sort option
    const oppositeOptions: Record<SortOption, SortOption> = {
      'price_low_high': 'price_high_low',
      'price_high_low': 'price_low_high',
      'departure_early_late': 'departure_late_early',
      'departure_late_early': 'departure_early_late',
      'arrival_early_late': 'arrival_late_early',
      'arrival_late_early': 'arrival_early_late',
      'duration_short_long': 'duration_long_short',
      'duration_long_short': 'duration_short_long'
    };

    return {
      sortOption: oppositeOptions[newOption] || newOption,
      sortDirection: newDirection,
      lastSorted: Date.now()
    };
  }

  // New option selected
  return {
    sortOption: newOption,
    sortDirection: newOption.includes('high') || newOption.includes('late') || newOption.includes('long') ? 'desc' : 'asc',
    lastSorted: Date.now()
  };
};

/**
 * Performance tracking for sorting operations
 */
export interface SortPerformanceMetrics {
  sortStartTime: number;
  sortEndTime: number;
  itemCount: number;
  sortOption: SortOption;
  executionTimeMs: number;
}

/**
 * Apply sorting with performance tracking
 */
export const applySortingWithMetrics = (
  flights: FlightListItem[], 
  sortOption: SortOption
): { sortedFlights: FlightListItem[]; metrics: SortPerformanceMetrics } => {
  const startTime = Date.now();
  
  const sortedFlights = applySorting(flights, sortOption);
  
  const endTime = Date.now();
  
  const metrics: SortPerformanceMetrics = {
    sortStartTime: startTime,
    sortEndTime: endTime,
    itemCount: flights.length,
    sortOption,
    executionTimeMs: endTime - startTime
  };

  return { sortedFlights, metrics };
};
