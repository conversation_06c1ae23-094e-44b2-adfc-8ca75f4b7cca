/* Enhanced shimmer animation */
@keyframes shimmer {
  0% {
    background-position: -200px 0;
  }
  100% {
    background-position: calc(200px + 100%) 0;
  }
}

.shimmer-enhanced {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200px 100%;
  animation: shimmer 1.5s infinite;
}

/* Pulse animation for fallback */
.shimmer-pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: .5;
  }
}

/* Specific shimmer styles for different elements */
.shimmer-text {
  @apply bg-gray-200 rounded;
  height: 1rem;
}

.shimmer-text-sm {
  @apply bg-gray-200 rounded;
  height: 0.75rem;
}

.shimmer-text-lg {
  @apply bg-gray-200 rounded;
  height: 1.25rem;
}

.shimmer-text-xl {
  @apply bg-gray-200 rounded;
  height: 1.5rem;
}

.shimmer-box {
  @apply bg-gray-200 rounded;
}

.shimmer-circle {
  @apply bg-gray-200 rounded-full;
}

.shimmer-button {
  @apply bg-gray-200 rounded;
  height: 2rem;
}

/* Flight card specific shimmer styles */
.flight-card-shimmer {
  @apply bg-white rounded-xl shadow-sm border border-gray-200;
}

.flight-card-shimmer .shimmer-airline-icon {
  width: 1.5rem;
  height: 1.5rem;
  @apply bg-gray-200 rounded-full;
}

.flight-card-shimmer .shimmer-price {
  width: 5rem;
  height: 1.5rem;
  @apply bg-gray-200 rounded;
}

.flight-card-shimmer .shimmer-time {
  width: 4rem;
  height: 1.25rem;
  @apply bg-gray-200 rounded;
}

.flight-card-shimmer .shimmer-city {
  width: 5rem;
  height: 1rem;
  @apply bg-gray-200 rounded;
}

.flight-card-shimmer .shimmer-duration {
  width: 3rem;
  height: 1rem;
  @apply bg-gray-200 rounded;
}

.flight-card-shimmer .shimmer-stops {
  width: 3rem;
  height: 1.5rem;
  @apply bg-gray-200 rounded-full;
}

.flight-card-shimmer .shimmer-feature {
  width: 4rem;
  height: 1.5rem;
  @apply bg-gray-200 rounded;
}

.flight-card-shimmer .shimmer-button-sm {
  width: 4rem;
  height: 1.75rem;
  @apply bg-gray-200 rounded;
}

/* Multi-city specific shimmer styles */
.multi-city-shimmer .shimmer-segment-header {
  @apply bg-white rounded-lg border border-gray-200 p-4;
}

.multi-city-shimmer .shimmer-filter-panel {
  @apply bg-white rounded-lg border border-gray-200 p-4;
}

/* Round-trip specific shimmer styles */
.round-trip-shimmer .shimmer-column-header {
  @apply bg-white rounded-lg border border-gray-200 p-4;
}

.round-trip-shimmer .shimmer-progress-bar {
  width: 100%;
  height: 0.5rem;
  @apply bg-gray-200 rounded-full;
}

/* Responsive shimmer adjustments */
@media (max-width: 768px) {
  .flight-card-shimmer {
    @apply p-3;
  }
  
  .shimmer-text {
    height: 0.875rem;
  }
  
  .shimmer-text-lg {
    height: 1.125rem;
  }
  
  .shimmer-text-xl {
    height: 1.25rem;
  }
}
