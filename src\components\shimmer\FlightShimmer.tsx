import React from 'react';
import './shimmer.css';

// Base shimmer animation component with improved animation
const ShimmerBase: React.FC<{ className?: string; children?: React.ReactNode }> = ({
  className = '',
  children
}) => (
  <div className={`animate-pulse ${className}`}>
    {children}
  </div>
);

// Individual shimmer elements
const ShimmerBox: React.FC<{ className?: string }> = ({ className = '' }) => (
  <div className={`bg-gray-200 rounded ${className}`}></div>
);

const ShimmerText: React.FC<{ className?: string }> = ({ className = '' }) => (
  <div className={`bg-gray-200 rounded h-4 ${className}`}></div>
);

const ShimmerCircle: React.FC<{ className?: string }> = ({ className = '' }) => (
  <div className={`bg-gray-200 rounded-full ${className}`}></div>
);

// Flight Card Shimmer - Exact replica of FlightCard structure
export const FlightCardShimmer: React.FC = () => (
  <ShimmerBase className="group rounded-xl shadow-sm border border-gray-200 overflow-hidden bg-white">
    <div className="p-3 lg:p-4">
      {/* Airline Info and Pricing - Compact Layout */}
      <div className="flex items-center justify-between mb-3">
        <div className="flex items-center flex-1 min-w-0">
          <ShimmerCircle className="w-6 h-6 mr-3 flex-shrink-0" />
          <div className="flex-1 min-w-0">
            <ShimmerText className="w-32 mb-1" />
            <ShimmerText className="w-24 h-3" />
          </div>
        </div>
        <div className="text-right flex-shrink-0 ml-4">
          <ShimmerText className="w-16 h-3 mb-1" />
          <ShimmerText className="w-20 h-6 mb-1" />
          <ShimmerText className="w-14 h-3" />
        </div>
      </div>

      {/* Flight Details - Compact Layout */}
      <div className="grid grid-cols-3 gap-3 lg:gap-4 mb-3 items-center">
        {/* Departure */}
        <div className="text-left">
          <ShimmerText className="w-16 h-5 mb-1" />
          <ShimmerText className="w-20 h-4 mb-1" />
          <ShimmerText className="w-16 h-3" />
        </div>

        {/* Duration and Stops */}
        <div className="text-center">
          <ShimmerText className="w-12 h-4 mb-1 mx-auto" />
          <div className="relative px-2">
            <div className="absolute w-full top-1/2 border-t border-gray-200 border-dashed left-0"></div>
            <div className="relative z-10 flex items-center justify-center">
              <ShimmerBox className="w-12 h-6 rounded-full" />
            </div>
          </div>
        </div>

        {/* Arrival */}
        <div className="text-right">
          <ShimmerText className="w-16 h-5 mb-1 ml-auto" />
          <ShimmerText className="w-20 h-4 mb-1 ml-auto" />
          <ShimmerText className="w-16 h-3 ml-auto" />
        </div>
      </div>

      {/* Flight Features - Compact Layout */}
      <div className="flex flex-wrap gap-1.5 lg:gap-2 mb-3">
        {[...Array(4)].map((_, index) => (
          <ShimmerBox key={index} className="w-16 h-6 rounded" />
        ))}
      </div>

      {/* Action Buttons - Compact Layout */}
      <div className="flex items-center justify-between pt-3 border-t border-gray-200">
        <div className="flex items-center space-x-3">
          <ShimmerText className="w-12 h-3" />
          <ShimmerText className="w-16 h-3" />
        </div>
        <div className="flex space-x-2">
          <ShimmerBox className="w-16 h-7 rounded" />
          <ShimmerBox className="w-16 h-7 rounded" />
        </div>
      </div>
    </div>
  </ShimmerBase>
);

// Multi-City Flight Card Shimmer
export const MultiCityFlightCardShimmer: React.FC = () => (
  <ShimmerBase className="border rounded-lg overflow-hidden bg-white border-gray-200">
    <div className="p-3">
      {/* Flight header */}
      <div className="flex justify-between items-center mb-2">
        <div className="flex items-center">
          <ShimmerCircle className="w-8 h-8" />
          <div className="ml-2">
            <ShimmerText className="w-24 h-4 mb-1" />
            <ShimmerText className="w-16 h-3" />
          </div>
        </div>
        <div className="text-right">
          <ShimmerText className="w-16 h-5 mb-1" />
          <ShimmerText className="w-12 h-3" />
        </div>
      </div>

      {/* Flight details */}
      <div className="flex justify-between items-center mb-2">
        <div>
          <ShimmerText className="w-16 h-5 mb-1" />
          <ShimmerText className="w-20 h-4" />
        </div>
        <div className="text-center">
          <ShimmerText className="w-12 h-4 mb-1 mx-auto" />
          <ShimmerBox className="w-16 h-6 rounded-full mx-auto" />
        </div>
        <div className="text-right">
          <ShimmerText className="w-16 h-5 mb-1 ml-auto" />
          <ShimmerText className="w-20 h-4 ml-auto" />
        </div>
      </div>

      {/* Additional details */}
      <div className="flex justify-between items-center">
        <ShimmerText className="w-20 h-3" />
        <div className="flex space-x-2">
          <ShimmerBox className="w-12 h-6 rounded" />
          <ShimmerBox className="w-16 h-6 rounded" />
        </div>
      </div>
    </div>
  </ShimmerBase>
);

// Segment Header Shimmer for Multi-City
export const SegmentHeaderShimmer: React.FC = () => (
  <ShimmerBase className="bg-white rounded-lg border border-gray-200 p-4 mb-4">
    <div className="flex items-center justify-between">
      <div className="flex items-center space-x-4">
        <ShimmerText className="w-32 h-6" />
        <ShimmerText className="w-24 h-4" />
      </div>
      <div className="flex items-center space-x-2">
        <ShimmerBox className="w-8 h-8 rounded" />
        <ShimmerText className="w-16 h-4" />
      </div>
    </div>
  </ShimmerBase>
);

// Filter Bar Shimmer - Simple horizontal filters
export const FilterBarShimmer: React.FC = () => (
  <ShimmerBase className="bg-white rounded-lg border border-gray-200 p-4 mb-4">
    <div className="flex items-center space-x-4">
      {[...Array(5)].map((_, index) => (
        <ShimmerBox key={index} className="w-20 h-8 rounded" />
      ))}
    </div>
  </ShimmerBase>
);

// Comprehensive Filter Sidebar Shimmer - Exact replica of filter sidebar
export const FilterSidebarShimmer: React.FC = () => (
  <ShimmerBase className="w-80 flex-shrink-0">
    <div className="bg-white rounded-lg border border-gray-200 p-4 space-y-6">
      {/* Filter Header */}
      <div>
        <ShimmerText className="w-16 h-6 mb-4" />
      </div>

      {/* Price Range Filter */}
      <div>
        <ShimmerText className="w-20 h-4 mb-3" />
        <div className="space-y-2">
          <div className="flex justify-between">
            <ShimmerText className="w-12 h-3" />
            <ShimmerText className="w-16 h-3" />
          </div>
          <ShimmerBox className="w-full h-2 rounded-full" />
          <div className="flex justify-between">
            <ShimmerBox className="w-16 h-6 rounded" />
            <ShimmerBox className="w-16 h-6 rounded" />
          </div>
        </div>
      </div>

      {/* Airlines Filter */}
      <div>
        <ShimmerText className="w-16 h-4 mb-3" />
        <div className="space-y-2">
          {[...Array(6)].map((_, index) => (
            <div key={index} className="flex items-center space-x-2">
              <ShimmerBox className="w-4 h-4 rounded" />
              <ShimmerCircle className="w-6 h-6" />
              <ShimmerText className="w-24 h-3 flex-1" />
              <ShimmerText className="w-8 h-3" />
            </div>
          ))}
        </div>
      </div>

      {/* Departure Time Filter */}
      <div>
        <ShimmerText className="w-24 h-4 mb-3" />
        <div className="grid grid-cols-2 gap-2">
          {[...Array(6)].map((_, index) => (
            <ShimmerBox key={index} className="h-10 rounded" />
          ))}
        </div>
      </div>

      {/* Duration Filter */}
      <div>
        <ShimmerText className="w-16 h-4 mb-3" />
        <div className="space-y-2">
          <div className="flex justify-between">
            <ShimmerText className="w-8 h-3" />
            <ShimmerText className="w-12 h-3" />
          </div>
          <ShimmerBox className="w-full h-2 rounded-full" />
        </div>
      </div>

      {/* Stops Filter */}
      <div>
        <ShimmerText className="w-12 h-4 mb-3" />
        <div className="space-y-2">
          {[...Array(3)].map((_, index) => (
            <div key={index} className="flex items-center space-x-2">
              <ShimmerBox className="w-4 h-4 rounded" />
              <ShimmerText className="w-20 h-3 flex-1" />
              <ShimmerText className="w-8 h-3" />
            </div>
          ))}
        </div>
      </div>

      {/* Aircraft Type Filter */}
      <div>
        <ShimmerText className="w-20 h-4 mb-3" />
        <div className="space-y-2">
          {[...Array(4)].map((_, index) => (
            <div key={index} className="flex items-center space-x-2">
              <ShimmerBox className="w-4 h-4 rounded" />
              <ShimmerText className="w-16 h-3 flex-1" />
              <ShimmerText className="w-6 h-3" />
            </div>
          ))}
        </div>
      </div>

      {/* Filter Actions */}
      <div className="pt-4 border-t border-gray-200">
        <div className="flex space-x-2">
          <ShimmerBox className="flex-1 h-8 rounded" />
          <ShimmerBox className="w-16 h-8 rounded" />
        </div>
      </div>
    </div>
  </ShimmerBase>
);

// Compact Filter Shimmer for Multi-City (side by side with flights)
export const CompactFilterShimmer: React.FC = () => (
  <ShimmerBase className="w-64 flex-shrink-0">
    <div className="bg-white rounded-lg border border-gray-200 p-4 space-y-4">
      {/* Price Range */}
      <div>
        <ShimmerText className="w-20 h-4 mb-2" />
        <ShimmerBox className="w-full h-8 rounded" />
      </div>

      {/* Airlines */}
      <div>
        <ShimmerText className="w-16 h-4 mb-2" />
        <div className="space-y-2">
          {[...Array(4)].map((_, index) => (
            <div key={index} className="flex items-center space-x-2">
              <ShimmerBox className="w-4 h-4 rounded" />
              <ShimmerText className="w-24 h-3" />
            </div>
          ))}
        </div>
      </div>

      {/* Departure Times */}
      <div>
        <ShimmerText className="w-24 h-4 mb-2" />
        <div className="grid grid-cols-2 gap-2">
          {[...Array(4)].map((_, index) => (
            <ShimmerBox key={index} className="h-8 rounded" />
          ))}
        </div>
      </div>

      {/* Stops */}
      <div>
        <ShimmerText className="w-12 h-4 mb-2" />
        <div className="space-y-2">
          {[...Array(3)].map((_, index) => (
            <div key={index} className="flex items-center space-x-2">
              <ShimmerBox className="w-4 h-4 rounded" />
              <ShimmerText className="w-16 h-3" />
            </div>
          ))}
        </div>
      </div>
    </div>
  </ShimmerBase>
);

// Trip Summary Shimmer
export const TripSummaryShimmer: React.FC = () => (
  <ShimmerBase className="bg-white rounded-lg border border-gray-200 p-6">
    <ShimmerText className="w-32 h-6 mb-4" />
    
    {/* Selected flights */}
    <div className="space-y-3 mb-6">
      {[...Array(2)].map((_, index) => (
        <div key={index} className="border border-gray-200 rounded p-3">
          <ShimmerText className="w-24 h-4 mb-2" />
          <ShimmerText className="w-32 h-3 mb-1" />
          <ShimmerText className="w-20 h-3" />
        </div>
      ))}
    </div>

    {/* Price breakdown */}
    <div className="border-t border-gray-200 pt-4">
      <div className="space-y-2 mb-4">
        {[...Array(3)].map((_, index) => (
          <div key={index} className="flex justify-between">
            <ShimmerText className="w-20 h-4" />
            <ShimmerText className="w-16 h-4" />
          </div>
        ))}
      </div>
      
      {/* Total */}
      <div className="flex justify-between items-center border-t border-gray-200 pt-2 mb-4">
        <ShimmerText className="w-16 h-5" />
        <ShimmerText className="w-20 h-6" />
      </div>

      {/* Action buttons */}
      <div className="space-y-2">
        <ShimmerBox className="w-full h-10 rounded" />
        <ShimmerBox className="w-full h-8 rounded" />
      </div>
    </div>
  </ShimmerBase>
);
