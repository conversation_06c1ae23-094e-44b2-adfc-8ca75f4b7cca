import React from 'react';
import type { FlightSearchData } from '../../../../contexts/FlightSearchContext';
import { FlightSearchProvider, useFlightSearch } from '../../../../contexts/FlightSearchContext';
import FlightSearchForm from '../../../../components/flight/flight-search-form/FlightSearchForm';
import { parseAirportString } from '../../../../utils/airportUtils';
import './InlineModifySearch.scss';

interface InlineModifySearchProps {
  isExpanded: boolean;
  onToggle: () => void;
  onSearchUpdate: (searchData: FlightSearchData) => void;
  currentSearchData: FlightSearchData;
}

// Inner component that uses the context
const InlineModifySearchContent: React.FC<InlineModifySearchProps> = ({
  isExpanded,
  onToggle,
  onSearchUpdate,
  currentSearchData
}) => {
  // No need to update context here - it should already have the correct data

  // Handle search submission from FlightSearchForm
  const handleSearch = (legacySearchData: any) => {
    console.log('📝 Modify search submitted with legacy data:', legacySearchData);

    // Convert legacy FlightSearchForm data to FlightSearchData format
    const popularAirports = [
      { code: 'JFK', city: 'New York', name: 'John F. Kennedy International Airport', country: 'United States' },
      { code: 'LHR', city: 'London', name: 'Heathrow Airport', country: 'United Kingdom' },
      { code: 'LAX', city: 'Los Angeles', name: 'Los Angeles International Airport', country: 'United States' },
      { code: 'CDG', city: 'Paris', name: 'Charles de Gaulle Airport', country: 'France' },
      { code: 'DXB', city: 'Dubai', name: 'Dubai International Airport', country: 'United Arab Emirates' },
      { code: 'SIN', city: 'Singapore', name: 'Changi Airport', country: 'Singapore' },
      { code: 'HKG', city: 'Hong Kong', name: 'Hong Kong International Airport', country: 'Hong Kong' },
      { code: 'FRA', city: 'Frankfurt', name: 'Frankfurt Airport', country: 'Germany' }
    ];

    const updatedSearchData: FlightSearchData = {
      ...currentSearchData,
      from: parseAirportString(legacySearchData.fromAirport, popularAirports) || currentSearchData.from,
      to: parseAirportString(legacySearchData.toAirport, popularAirports) || currentSearchData.to,
      depart: new Date(legacySearchData.selectedDates.departure),
      return: legacySearchData.selectedDates.return ? new Date(legacySearchData.selectedDates.return) : null,
      tripType: legacySearchData.selectedTripType,
      passengers: legacySearchData.passengers,
      class: legacySearchData.selectedClass,
      // Preserve advanced search settings
      advanced_search: currentSearchData.advanced_search
    };

    console.log('✅ Converted to FlightSearchData format:', updatedSearchData);

    // Close the modal first for better UX
    onToggle();

    // Then trigger the search update
    onSearchUpdate(updatedSearchData);
  };

  if (!isExpanded) {
    return null;
  }

  return (
    <div className="inline-modify-search-container">
      {/* Modal Overlay */}
      <div className="fixed inset-0 bg-black bg-opacity-50 z-[10000] flex items-center justify-center p-4">
        <div className="bg-white rounded-xl shadow-2xl max-w-4xl w-full max-h-[90vh] overflow-y-auto">
          {/* Header */}
          <div className="flex items-center justify-between p-4 border-b border-gray-200">
            <div className="flex items-center">
              <i className="fas fa-edit text-blue-600 mr-2 text-lg"></i>
              <h2 className="text-lg font-bold text-gray-900">Modify Search</h2>
            </div>
            <button
              onClick={onToggle}
              className="text-gray-400 hover:text-gray-600 transition-colors p-1 hover:bg-gray-100 rounded-lg"
              aria-label="Close modify search"
            >
              <i className="fas fa-times text-lg"></i>
            </button>
          </div>

          {/* Compact FlightSearchForm Content */}
          <div className="p-6 compact-search-form">
            <FlightSearchForm onSearch={handleSearch} compact={true} />
          </div>
        </div>
      </div>
    </div>
  );
};

// Main component with provider wrapper
const InlineModifySearch: React.FC<InlineModifySearchProps> = (props) => {
  return (
    <FlightSearchProvider>
      <InlineModifySearchContent {...props} />
    </FlightSearchProvider>
  );
};

export default InlineModifySearch;
