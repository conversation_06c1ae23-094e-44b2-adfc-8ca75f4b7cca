/**
 * Price formatting utilities for flight prices
 */

/**
 * Format price with proper currency symbol and no unnecessary decimals
 * @param amount - Price amount
 * @param currency - Currency code (default: 'INR')
 * @param showDecimals - Whether to show decimal places (default: false)
 * @returns Formatted price string
 */
export const formatPrice = (
  amount: number, 
  currency: string = 'INR', 
  showDecimals: boolean = false
): string => {
  // Round the amount to remove unnecessary decimals
  const roundedAmount = showDecimals ? Math.round(amount * 10) / 10 : Math.round(amount);
  
  // Currency symbol mapping
  const currencySymbols: Record<string, string> = {
    'INR': '₹',
    'USD': '$',
    'EUR': '€',
    'GBP': '£',
    'AED': 'AED ',
    'SGD': 'S$'
  };

  const symbol = currencySymbols[currency] || currency + ' ';
  
  // Format with proper thousand separators
  const formattedAmount = roundedAmount.toLocaleString('en-IN', {
    minimumFractionDigits: 0,
    maximumFractionDigits: showDecimals ? 1 : 0
  });

  return `${symbol}${formattedAmount}`;
};

/**
 * Format price for display in flight cards
 * @param amount - Price amount
 * @param currency - Currency code (default: 'INR')
 * @returns Formatted price string without decimals
 */
export const formatFlightPrice = (amount: number, currency: string = 'INR'): string => {
  return formatPrice(amount, currency, false);
};

/**
 * Calculate and format savings amount
 * @param originalPrice - Original price
 * @param currentPrice - Current price
 * @param currency - Currency code (default: 'INR')
 * @returns Formatted savings string
 */
export const formatSavings = (
  originalPrice: number, 
  currentPrice: number, 
  currency: string = 'INR'
): string => {
  const savings = originalPrice - currentPrice;
  if (savings <= 0) return '';
  
  return `Save ${formatPrice(savings, currency, false)}`;
};

/**
 * Parse price from string and return clean number
 * @param priceString - Price string that might contain currency symbols
 * @returns Clean number value
 */
export const parsePrice = (priceString: string | number): number => {
  if (typeof priceString === 'number') {
    return priceString;
  }
  
  // Remove currency symbols and parse
  const cleanString = priceString.replace(/[₹$€£,\s]/g, '');
  const parsed = parseFloat(cleanString);
  
  return isNaN(parsed) ? 0 : parsed;
};

/**
 * Format price range for filters
 * @param min - Minimum price
 * @param max - Maximum price
 * @param currency - Currency code (default: 'INR')
 * @returns Formatted price range string
 */
export const formatPriceRange = (
  min: number, 
  max: number, 
  currency: string = 'INR'
): string => {
  return `${formatPrice(min, currency)} - ${formatPrice(max, currency)}`;
};
