import React from 'react';

export interface Destination {
  id: string;
  name: string;
  country: string;
  imageUrl: string;
  startingPrice: number;
  hasDirectFlights: boolean;
  description?: string;
}

interface PopularDestinationsProps {
  destinations: Destination[];
  onDestinationClick?: (destination: Destination) => void;
}

const PopularDestinations: React.FC<PopularDestinationsProps> = ({ 
  destinations, 
  onDestinationClick 
}) => {
  const handleDestinationClick = (destination: Destination) => {
    if (onDestinationClick) {
      onDestinationClick(destination);
    }
  };

  return (
    <div className="max-w-7xl mx-auto px-4 py-8 md:py-12">
      <div className="text-center mb-8 md:mb-12">
        <div className="inline-block">
          <span className="inline-block bg-blue-100 text-blue-800 text-xs font-semibold px-3 py-1 rounded-full mb-3">
            Featured Destinations
          </span>
        </div>
        <h2 className="text-2xl md:text-3xl font-bold text-gray-900 mb-2 md:mb-4">
          Popular Business Destinations
        </h2>
        <p className="text-gray-600 max-w-2xl mx-auto">
          Explore top corporate travel routes with exclusive B2B rates
        </p>
      </div>
      
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6 lg:gap-8">
        {destinations.map((destination) => (
          <div 
            key={destination.id}
            className="bg-white rounded-xl sm:rounded-2xl shadow-lg overflow-hidden group hover:shadow-xl transition-all transform hover:-translate-y-1 cursor-pointer"
            onClick={() => handleDestinationClick(destination)}
          >
            <div className="h-40 md:h-48 overflow-hidden relative">
              <img
                src={destination.imageUrl}
                alt={destination.name}
                className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-500"
              />
              <div className="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent"></div>
              <div className="absolute bottom-4 left-4 text-white">
                <h3 className="text-lg md:text-xl font-bold">{destination.name}</h3>
                <p className="text-sm">{destination.country}</p>
              </div>
            </div>
            
            <div className="p-4 md:p-6">
              <div className="flex justify-between items-center mb-4">
                <div className="flex items-center space-x-2 bg-blue-50 px-3 py-1.5 rounded-full">
                  <i className="fas fa-plane text-blue-600 transform -rotate-45"></i>
                  <span className="text-sm text-blue-700">
                    {destination.hasDirectFlights ? 'Direct flights available' : 'Connecting flights'}
                  </span>
                </div>
                <span className="text-sm font-medium text-gray-900">
                  from ${destination.startingPrice}
                </span>
              </div>
              
              {destination.description && (
                <p className="text-sm text-gray-600 mb-4">
                  {destination.description}
                </p>
              )}
              
              <button 
                className="!rounded-button w-full bg-gray-100 text-gray-800 py-2 px-4 text-sm font-medium hover:bg-gray-200 transition-colors cursor-pointer whitespace-nowrap"
                onClick={(e) => {
                  e.stopPropagation();
                  handleDestinationClick(destination);
                }}
              >
                View Details
              </button>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default PopularDestinations;
