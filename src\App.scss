/* Import SCSS variables */
@use './styles/variables' as *;

/* Global Reset and Base Styles */
* {
  box-sizing: border-box;
}

#root {
  width: 100%;
  min-height: 100vh;
  margin: 0;
  padding: 0;
}

body {
  margin: 0;
  padding: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background-color: $background_secondary;
}

/* App Layout */
.app {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

/* Main Content */
.main-content {
  flex: 1;
  width: 100%;
}

/* Container Utilities */
.container {
  width: 100%;
  margin-left: auto;
  margin-right: auto;
  padding-left: 1rem;
  padding-right: 1rem;
}

.container-sm {
  max-width: 640px;
}

.container-md {
  max-width: 768px;
}

.container-lg {
  max-width: 1024px;
}

.container-xl {
  max-width: 1280px;
}

.container-2xl {
  max-width: 1536px;
}

/* Responsive Container Padding */
@media (min-width: 640px) {
  .container {
    padding-left: 1.5rem;
    padding-right: 1.5rem;
  }
}

@media (min-width: 1024px) {
  .container {
    padding-left: 2rem;
    padding-right: 2rem;
  }
}

/* Header Styles */
.header {
  background-color: $background_tertiary;
  border-bottom: 1px solid $border_primary;
  padding: 1rem 0;
}

.nav {
  max-width: 1200px;
  margin: 0 auto;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 2rem;
}

.nav-brand a {
  font-size: 1.5rem;
  font-weight: bold;
  text-decoration: none;
  color: $text_primary;
}

.nav-links {
  display: flex;
  list-style: none;
  margin: 0;
  padding: 0;
  gap: 2rem;
}

.nav-links a {
  text-decoration: none;
  color: $text_secondary;
  font-weight: 500;
  transition: color 0.3s;
}

.nav-links a:hover {
  color: $primary_color;
}

/* Footer Styles */
.footer {
  background-color: $footer_background;
  color: $text_inverse;
  padding: 2rem 0;
  margin-top: auto;
}

.footer-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.footer-links {
  display: flex;
  gap: 2rem;
}

.footer-links a {
  color: $text_tertiary;
  text-decoration: none;
  transition: color 0.3s;
}

.footer-links a:hover {
  color: $text_inverse;
}

/* Page Styles */
.landing-page,
.flight-search {
  text-align: center;
  padding: 3rem 0;
}

.landing-page h1,
.flight-search h1 {
  font-size: 2.5rem;
  margin-bottom: 1rem;
  color: $text_primary;
}

.landing-page p,
.flight-search p {
  font-size: 1.2rem;
  color: $text_secondary;
  margin-bottom: 2rem;
}

/* Global Reset and Utilities */
* {
  box-sizing: border-box;
}

#root {
  width: 100%;
  min-height: 100vh;
  margin: 0;
  padding: 0;
}

body {
  margin: 0;
  padding: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background-color: $background_secondary;
}

/* Container Utilities */
.container {
  width: 100%;
  margin-left: auto;
  margin-right: auto;
  padding-left: 1rem;
  padding-right: 1rem;
}

.container-sm {
  max-width: 640px;
}

.container-md {
  max-width: 768px;
}

.container-lg {
  max-width: 1024px;
}

.container-xl {
  max-width: 1280px;
}

.container-2xl {
  max-width: 1536px;
}

/* Responsive Container Padding */
@media (min-width: 640px) {
  .container {
    padding-left: 1.5rem;
    padding-right: 1.5rem;
  }
}

@media (min-width: 1024px) {
  .container {
    padding-left: 2rem;
    padding-right: 2rem;
  }
}

/* Button Utilities */
.!rounded-button {
  border-radius: 0.5rem !important;
}

/* Scrollbar Styling */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: $neutral_100;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb {
  background: $neutral_300;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: $neutral_400;
}

/* Focus Styles */
.focus-ring:focus {
  outline: 2px solid $primary_color;
  outline-offset: 2px;
}

/* Animation Utilities */
.transition-all {
  transition: all 0.3s ease;
}

.transition-colors {
  transition: color 0.3s ease, background-color 0.3s ease, border-color 0.3s ease;
}

/* Responsive Breakpoints */
/* Mobile First Approach */
/* xs: 0px - 639px (default) */
/* sm: 640px and up */
/* md: 768px and up */
/* lg: 1024px and up */
/* xl: 1280px and up */
/* 2xl: 1536px and up */

/* Layout Spacing */
.page-padding {
  padding: 1rem;
}

@media (min-width: 640px) {
  .page-padding {
    padding: 1.5rem;
  }
}

@media (min-width: 1024px) {
  .page-padding {
    padding: 2rem;
  }
}

/* Component Spacing */
.section-spacing {
  margin-bottom: 2rem;
}

@media (min-width: 768px) {
  .section-spacing {
    margin-bottom: 3rem;
  }
}

@media (min-width: 1024px) {
  .section-spacing {
    margin-bottom: 4rem;
  }
}

/* Grid Responsive */
.responsive-grid {
  display: grid;
  gap: 1rem;
  grid-template-columns: 1fr;
}

@media (min-width: 640px) {
  .responsive-grid {
    gap: 1.5rem;
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (min-width: 1024px) {
  .responsive-grid {
    gap: 2rem;
    grid-template-columns: repeat(3, 1fr);
  }
}

@media (min-width: 1280px) {
  .responsive-grid {
    grid-template-columns: repeat(4, 1fr);
  }
}

/* Print Styles */
@media print {
  .no-print {
    display: none !important;
  }

  .print-break {
    page-break-before: always;
  }

  .print-avoid-break {
    page-break-inside: avoid;
  }
}
