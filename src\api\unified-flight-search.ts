import { callSeamlessRefreshSearch, callPollingSearch, getExpressSearch } from './api/flight-api.service';
import type { FLightSearchListBody, ScheduleBody } from '../models/flight/flight-search.model';
import type { FlightResponse } from '../models/flight/flight-list.model';
import type { FlightSearchData } from '../contexts/FlightSearchContext';
import { scheduleBodySet } from '../utils/flightPayloadUtils';

// Types for the unified search function
export type FlightType = 'oneWay' | 'roundTrip' | 'multiCity';

export interface SearchParameters extends FlightSearchData {
  // FlightSearchData already contains all the search parameters we need
}

export interface SearchMetadata {
  cacheHit: boolean;
  dataSource: string;
  responseTimeMS: number;
  usedCachedResults: boolean;
  requiresPolling: boolean;
  tuiFromSession: boolean;
}

export interface UnifiedSearchResult {
  data: FlightResponse;
  metadata: SearchMetadata;
}

// Session storage key for TUI - must match flight-api.service.ts
const TUI_SESSION_KEY = 'dySearchTuiID';

/**
 * Convert FlightSearchData to FormSearch structure for scheduleBodySet
 */
const convertToFormSearch = (
  searchParams: SearchParameters,
  flightType: FlightType
): any => {
  const trips = [];

  if (flightType === 'multiCity') {
    // For multi-city, use the trips array
    searchParams.trips?.forEach((trip) => {
      trips.push({
        from: {
          city: trip.from.city,
          airport: trip.from.airport,
          iata: trip.from.iata,
          country: trip.from.country,
          airportOpen: false
        },
        to: {
          city: trip.to.city,
          airport: trip.to.airport,
          iata: trip.to.iata,
          country: trip.to.country,
          airportOpen: false
        },
        depart: trip.depart,
        return: undefined // Multi-city doesn't have return dates per trip
      });
    });
  } else {
    // For one-way and round-trip, use the main from/to fields
    trips.push({
      from: {
        city: searchParams.from.city,
        airport: searchParams.from.airport,
        iata: searchParams.from.iata,
        country: searchParams.from.country,
        airportOpen: false
      },
      to: {
        city: searchParams.to.city,
        airport: searchParams.to.airport,
        iata: searchParams.to.iata,
        country: searchParams.to.country,
        airportOpen: false
      },
      depart: searchParams.depart,
      return: flightType === 'roundTrip' ? searchParams.return : undefined
    });
  }

  // Map class to cabin - API expects single letter codes
  const cabinMap = {
    'economy': 'E',
    'premiumEconomy': 'PE',
    'business': 'B',
    'first': 'F'
  };

  return {
    SecType: 'Flight',
    travellers: {
      adult: searchParams.passengers.adults,
      child: searchParams.passengers.children,
      infant: searchParams.passengers.infants
    },
    cabin: cabinMap[searchParams.class] || 'E',
    trips: trips
  };
};

/**
 * Create search body using scheduleBodySet function
 */
const createSearchBody = (
  searchParams: SearchParameters,
  flightType: FlightType
): ScheduleBody => {
  const formValue = convertToFormSearch(searchParams, flightType);
  const fareType = flightType === 'oneWay' ? 'ON' : flightType === 'roundTrip' ? 'RT' : 'MC';

  return scheduleBodySet(formValue, fareType);
};

/**
 * Get TUI from session storage
 */
const getTUIFromSession = (): string | null => {
  try {
    return sessionStorage.getItem(TUI_SESSION_KEY);
  } catch {
    return null;
  }
};

/**
 * Store TUI in session storage
 */
const storeTUIInSession = (tui: string): void => {
  try {
    sessionStorage.setItem(TUI_SESSION_KEY, tui);
  } catch {
    // Silently fail if session storage is not available
  }
};

/**
 * Unified flight search function that handles ONLY API calls
 * 
 * @param searchParams - Search parameters for the flight search
 * @param flightType - Type of flight search (OneWay/RoundTrip/MultiCity)
 * @returns Promise<UnifiedSearchResult> - API response data with metadata
 */
export const unifiedFlightSearch = async (
  searchParams: SearchParameters,
  flightType: FlightType
): Promise<UnifiedSearchResult> => {
  // 1. Check for existing TUI in session storage
  const existingTUI = getTUIFromSession();
  const tuiFromSession = !!existingTUI;

  console.log('🔍 TUI Session Storage Check:', {
    existingTUI,
    tuiFromSession,
    sessionStorageKey: TUI_SESSION_KEY,
    allSessionStorageKeys: Object.keys(sessionStorage),
    sessionStorageValues: Object.fromEntries(Object.entries(sessionStorage))
  });

  // If we have an existing TUI, we should use search_list directly
  if (existingTUI) {
    console.log('🚀 Found existing TUI, will use search_list API directly:', existingTUI);
  } else {
    console.log('🆕 No existing TUI found, will use full search flow');
  }
  
  // 2. Prepare search body using placeholder function
  const searchBody = createSearchBody(searchParams, flightType);

  // If we have an existing TUI, add it to the search body
  if (existingTUI) {
    searchBody.TUI = existingTUI;
  }

  console.log('📤 API Request Body:', JSON.stringify(searchBody, null, 2));

  let metadata: SearchMetadata = {
    cacheHit: false,
    dataSource: 'unknown',
    responseTimeMS: 0,
    usedCachedResults: false,
    requiresPolling: false,
    tuiFromSession
  };

  try {
    // EXACT SEQUENCE AS SPECIFIED:
    // 1. First call callSeamlessRefreshSearch to try getting cached results
    console.log('🔄 Step 1: Calling callSeamlessRefreshSearch for cached results');
    const refreshResponse = await callSeamlessRefreshSearch(searchBody);

    console.log('✅ Step 1 completed, refresh response received:', {
      completed: refreshResponse?.Completed,
      tripsCount: refreshResponse?.Trips?.length || 0,
      hasTrips: !!(refreshResponse?.Trips && refreshResponse.Trips.length > 0),
      tui: refreshResponse?.TUI,
      responseKeys: Object.keys(refreshResponse || {})
    });

    // Track cache metadata with exact checks: cache_hit === true || CacheHit === true || sh_price === false
    const isCacheHit = refreshResponse.cache_hit === true ||
                      refreshResponse.CacheHit === true ||
                      refreshResponse.sh_price === false;

    metadata.cacheHit = isCacheHit;
    metadata.dataSource = refreshResponse.DataSource || 'refresh';
    metadata.responseTimeMS = refreshResponse.ResponseTimeMS || 0;
    metadata.usedCachedResults = isCacheHit;

    // Store TUI in sessionStorage using the helper function
    if (refreshResponse.TUI) {
      storeTUIInSession(refreshResponse.TUI);
      console.log('✅ TUI stored in sessionStorage:', refreshResponse.TUI);
    }

    // 2. Check if data.Completed is true
    console.log('🔍 Step 2: Checking if data.Completed is true:', refreshResponse.Completed);

    // callSeamlessRefreshSearch now handles search_list calls internally
    // So we should get complete data if available, or incomplete data that needs polling
    console.log('✅ Returning data from callSeamlessRefreshSearch (completed:', refreshResponse.Completed, ')');

    // Update metadata based on the final response
    metadata.cacheHit = refreshResponse.CacheHit || false;
    metadata.dataSource = refreshResponse.DataSource || 'refresh';
    metadata.responseTimeMS = refreshResponse.ResponseTimeMS || 0;

    if (refreshResponse.Completed === true) {
      console.log('✅ Data is complete from callSeamlessRefreshSearch (includes search_list if needed)');
      metadata.requiresPolling = false;
      metadata.usedCachedResults = metadata.cacheHit;

      return {
        data: refreshResponse,
        metadata
      };
    } else {
      console.log('🔄 Data still incomplete after callSeamlessRefreshSearch, will need background polling');
      metadata.requiresPolling = true;
      metadata.usedCachedResults = false;

      // Return incomplete data immediately so UI can show flights with shimmer prices
      return {
        data: refreshResponse,
        metadata
      };
    }

  } catch (error) {
    console.error('❌ Error in unifiedFlightSearch:', error);

    // If refresh search fails and we have incomplete cached results with TUI
    // Fall back to getExpressSearch for polling
    if (existingTUI) {
      console.log('🔄 Trying fallback with existing TUI:', existingTUI);
      try {
        const expressSearchBody: FLightSearchListBody = {
          ClientID: '',
          TUI: existingTUI
        };

        const expressResponse = await getExpressSearch(expressSearchBody);

        console.log('✅ Fallback express search succeeded');
        metadata.usedCachedResults = false;
        metadata.requiresPolling = true;
        metadata.dataSource = 'express_fallback';

        return {
          data: expressResponse,
          metadata
        };
      } catch (expressError) {
        console.error('❌ Fallback express search also failed:', expressError);
        // If express search also fails, throw the original error
        throw error;
      }
    }

    console.error('❌ No fallback available, throwing error');
    throw error;
  }
};

/**
 * Background polling function to get complete flight data with pricing
 * This should be called after the initial search to get updated prices
 */
export const pollForCompleteData = async (
  searchBody: ScheduleBody,
  maxAttempts: number = 10,
  pollInterval: number = 1000
): Promise<UnifiedSearchResult> => {
  console.log('🔄 Starting background polling for complete data');

  try {
    const pollingResponse = await callPollingSearch(searchBody, maxAttempts, pollInterval);

    const metadata: SearchMetadata = {
      cacheHit: pollingResponse.CacheHit || false,
      dataSource: pollingResponse.DataSource || 'polling',
      responseTimeMS: pollingResponse.ResponseTimeMS || 0,
      usedCachedResults: false,
      requiresPolling: false, // Polling is now complete
      tuiFromSession: false
    };

    // Store TUI from polling response using the helper function
    if (pollingResponse.TUI) {
      storeTUIInSession(pollingResponse.TUI);
      console.log('✅ Updated TUI stored in sessionStorage from polling:', pollingResponse.TUI);
    }

    console.log('✅ Background polling completed successfully');
    return {
      data: pollingResponse,
      metadata
    };
  } catch (error) {
    console.error('❌ Background polling failed:', error);
    throw error;
  }
};

/**
 * Alternative search function for when you have an existing TUI
 * and want to poll for updated results
 */
export const searchWithExistingTUI = async (tui: string): Promise<UnifiedSearchResult> => {
  const expressSearchBody: FLightSearchListBody = {
    ClientID: '',
    TUI: tui
  };

  const response = await getExpressSearch(expressSearchBody);

  const metadata: SearchMetadata = {
    cacheHit: false,
    dataSource: 'express_direct',
    responseTimeMS: 0,
    usedCachedResults: false,
    requiresPolling: false,
    tuiFromSession: false
  };

  return {
    data: response,
    metadata
  };
};

/**
 * Clear stored TUI from session storage
 */
export const clearStoredTUI = (): void => {
  try {
    sessionStorage.removeItem(TUI_SESSION_KEY);
  } catch {
    // Silently fail if session storage is not available
  }
};
