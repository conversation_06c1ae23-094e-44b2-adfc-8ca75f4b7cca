import React, { useState, useEffect, useMemo } from 'react';
import { FlightCard } from './';
import EnhancedFlightFilters from './EnhancedFlightFilters';
import { applySorting } from '../../../../utils/flightSortingUtils';
import type { FlightResponse } from '../../../../models/flight/flight-list.model';
import type {
  FlightListItem,
  FlightFilterState,
  SortOption,
  PaginationState
} from '../../../../models/flight/flight-list-models';
import {
  DEFAULT_FILTER_STATE,
  DEFAULT_PAGINATION_STATE
} from '../../../../models/flight/flight-list-models';
import { transformFlightResponse } from '../../../../utils/flightDataTransformer';
import {
  applyFilters,
  paginateFlights,
  updateAirlineFilters,
  updateConnectionAirportFilters,
  updatePriceRangeFilter
} from '../../../../utils/flightFilterUtils';
import {
  extractAirlinesFromFlights,
  extractConnectionAirportsFromFlights
} from '../../../../utils/flightDataTransformer';

interface RoundTripFlightListProps {
  apiData: FlightResponse | null;
  apiMetadata: any;
  isLoading: boolean;
  searchData?: any; // Add search data to detect domestic flights
  onShowFlightDetails?: (outboundFlight: FlightListItem, returnFlight?: FlightListItem) => void;
}

const RoundTripFlightList: React.FC<RoundTripFlightListProps> = ({
  apiData,
  apiMetadata,
  isLoading,
  searchData,
  onShowFlightDetails
}) => {
  // Component state for data processing
  const [outboundFlights, setOutboundFlights] = useState<FlightListItem[]>([]);
  const [returnFlights, setReturnFlights] = useState<FlightListItem[]>([]);
  const [outboundFilters, setOutboundFilters] = useState<FlightFilterState>(DEFAULT_FILTER_STATE);
  const [returnFilters, setReturnFilters] = useState<FlightFilterState>(DEFAULT_FILTER_STATE);
  const [outboundSortOption, setOutboundSortOption] = useState<SortOption>('price_low_high');
  const [returnSortOption, setReturnSortOption] = useState<SortOption>('price_low_high');
  const [pagination, setPagination] = useState<PaginationState>(DEFAULT_PAGINATION_STATE);

  // Selection state for domestic round trip flights
  const [selectedOutboundFlight, setSelectedOutboundFlight] = useState<FlightListItem | null>(null);
  const [selectedReturnFlight, setSelectedReturnFlight] = useState<FlightListItem | null>(null);

  // Detect if this is a domestic flight
  const isDomesticFlight = searchData?.from?.country === searchData?.to?.country;

  // Process API data when it changes
  useEffect(() => {
    if (apiData) {
      console.log('🔄 RoundTripFlightList: Processing API data:', {
        completed: apiData.Completed,
        tripsCount: apiData.Trips?.length || 0,
        firstTripJourneyCount: apiData.Trips?.[0]?.Journey?.length || 0,
        sampleJourney: apiData.Trips?.[0]?.Journey?.[0]
      });

      const transformedData = transformFlightResponse(apiData, {
        cacheHit: apiMetadata?.cacheHit || false,
        responseTimeMs: apiMetadata?.responseTimeMS || 0
      });

      console.log('🔄 RoundTripFlightList: Transformation result:', {
        outboundFlights: transformedData.outboundFlights.length,
        returnFlights: transformedData.returnFlights.length,
        totalResults: transformedData.totalResults
      });

      // For round-trip flights, use both outbound and return flights
      setOutboundFlights(transformedData.outboundFlights);
      setReturnFlights(transformedData.returnFlights);

      // Update filters with new flight data for outbound flights
      const outboundAirlines = extractAirlinesFromFlights(transformedData.outboundFlights);
      const outboundAirports = extractConnectionAirportsFromFlights(transformedData.outboundFlights);

      setOutboundFilters(currentFilters => {
        let updatedFilters = updateAirlineFilters(currentFilters, outboundAirlines);
        updatedFilters = updateConnectionAirportFilters(updatedFilters, outboundAirports);
        updatedFilters = updatePriceRangeFilter(updatedFilters, transformedData.outboundFlights);
        return updatedFilters;
      });

      // Update filters with new flight data for return flights
      const returnAirlines = extractAirlinesFromFlights(transformedData.returnFlights);
      const returnAirports = extractConnectionAirportsFromFlights(transformedData.returnFlights);

      setReturnFilters(currentFilters => {
        let updatedFilters = updateAirlineFilters(currentFilters, returnAirlines);
        updatedFilters = updateConnectionAirportFilters(updatedFilters, returnAirports);
        updatedFilters = updatePriceRangeFilter(updatedFilters, transformedData.returnFlights);
        return updatedFilters;
      });

      console.log('✅ RoundTripFlightList: Processed flights:',
        transformedData.outboundFlights.length, 'outbound,',
        transformedData.returnFlights.length, 'return, Complete:', apiData.Completed);
    }
  }, [apiData, apiMetadata]);

  // Restore selections from localStorage when flights are loaded
  useEffect(() => {
    if (outboundFlights.length > 0 && returnFlights.length > 0) {
      try {
        const savedOutbound = localStorage.getItem('selectedOutboundFlight');
        const savedReturn = localStorage.getItem('selectedReturnFlight');

        if (savedOutbound) {
          const parsedOutbound = JSON.parse(savedOutbound);
          const matchingOutbound = outboundFlights.find(flight =>
            flight.id === parsedOutbound.id || flight.flightNumber === parsedOutbound.flightNumber
          );
          if (matchingOutbound) {
            setSelectedOutboundFlight(matchingOutbound);
          }
        }

        if (savedReturn) {
          const parsedReturn = JSON.parse(savedReturn);
          const matchingReturn = returnFlights.find(flight =>
            flight.id === parsedReturn.id || flight.flightNumber === parsedReturn.flightNumber
          );
          if (matchingReturn) {
            setSelectedReturnFlight(matchingReturn);
          }
        }
      } catch (error) {
        console.error('Error restoring flight selections:', error);
      }
    }
  }, [outboundFlights, returnFlights]);

  // Process outbound flights with filters and sorting
  const processedOutboundFlights = useMemo(() => {
    const filteredFlights = applyFilters(outboundFlights, outboundFilters);
    const sortedFlights = applySorting(filteredFlights, outboundSortOption);
    const { paginatedFlights, updatedPagination } = paginateFlights(sortedFlights, pagination);

    return {
      flights: paginatedFlights,
      pagination: updatedPagination,
      totalFiltered: filteredFlights.length
    };
  }, [outboundFlights, outboundFilters, outboundSortOption, pagination]);

  // Process return flights with filters and sorting
  const processedReturnFlights = useMemo(() => {
    const filteredFlights = applyFilters(returnFlights, returnFilters);
    const sortedFlights = applySorting(filteredFlights, returnSortOption);
    const { paginatedFlights, updatedPagination } = paginateFlights(sortedFlights, pagination);

    return {
      flights: paginatedFlights,
      pagination: updatedPagination,
      totalFiltered: filteredFlights.length
    };
  }, [returnFlights, returnFilters, returnSortOption, pagination]);

  // Handle filter changes
  const handleOutboundFilterChange = (newFilters: FlightFilterState) => {
    setOutboundFilters(newFilters);
    setPagination(prev => ({ ...prev, currentPage: 1 }));
  };

  const handleReturnFilterChange = (newFilters: FlightFilterState) => {
    setReturnFilters(newFilters);
    setPagination(prev => ({ ...prev, currentPage: 1 }));
  };

  // Handle page changes
  const handlePageChange = (page: number) => {
    setPagination(prev => ({ ...prev, currentPage: page }));
  };

  // Selection handlers for domestic round trip flights
  const handleOutboundFlightSelect = (flight: FlightListItem) => {
    setSelectedOutboundFlight(flight);
    console.log('Selected outbound flight:', flight);

    // Store selection in localStorage for persistence
    localStorage.setItem('selectedOutboundFlight', JSON.stringify({
      id: flight.id,
      flightNumber: flight.flightNumber,
      airline: flight.airline,
      departure: flight.departure,
      arrival: flight.arrival,
      price: flight.price,
      duration: flight.duration,
      stops: flight.stops
    }));
  };

  const handleReturnFlightSelect = (flight: FlightListItem) => {
    setSelectedReturnFlight(flight);
    console.log('Selected return flight:', flight);

    // Store selection in localStorage for persistence
    localStorage.setItem('selectedReturnFlight', JSON.stringify({
      id: flight.id,
      flightNumber: flight.flightNumber,
      airline: flight.airline,
      departure: flight.departure,
      arrival: flight.arrival,
      price: flight.price,
      duration: flight.duration,
      stops: flight.stops
    }));
  };

  // Calculate total price when both flights are selected
  const getTotalPrice = () => {
    if (selectedOutboundFlight && selectedReturnFlight) {
      return selectedOutboundFlight.price.amount + selectedReturnFlight.price.amount;
    }
    return 0;
  };

  // Check if flight is selected
  const isFlightSelected = (flight: FlightListItem, type: 'outbound' | 'return') => {
    if (type === 'outbound') {
      return selectedOutboundFlight?.id === flight.id;
    }
    return selectedReturnFlight?.id === flight.id;
  };

  if (isLoading) {
    return (
      <div className="flex gap-4 lg:gap-6 xl:gap-8">
        {/* Enhanced Filters Sidebar Shimmer */}
        <div className="w-full max-w-xs flex-shrink-0">
          <div className="bg-white rounded-lg border border-gray-200 p-4 sticky top-4">
            <div className="space-y-4">
              {/* Filter Header Shimmer */}
              <div className="flex justify-between items-center">
                <div className="h-5 bg-gray-200 rounded w-16 animate-pulse"></div>
                <div className="h-4 bg-gray-200 rounded w-20 animate-pulse"></div>
              </div>

              {/* Stops Filter Shimmer */}
              <div className="space-y-2">
                <div className="h-4 bg-gray-200 rounded w-12 animate-pulse"></div>
                <div className="flex gap-2">
                  {[...Array(3)].map((_, index) => (
                    <div key={index} className="h-8 bg-gray-200 rounded w-16 animate-pulse"></div>
                  ))}
                </div>
              </div>

              {/* Price Range Shimmer */}
              <div className="space-y-2">
                <div className="h-4 bg-gray-200 rounded w-20 animate-pulse"></div>
                <div className="h-8 bg-gray-200 rounded w-full animate-pulse"></div>
              </div>

              {/* Airlines Shimmer */}
              <div className="space-y-2">
                <div className="h-4 bg-gray-200 rounded w-16 animate-pulse"></div>
                <div className="space-y-2">
                  {[...Array(4)].map((_, index) => (
                    <div key={index} className="flex items-center space-x-2">
                      <div className="w-4 h-4 bg-gray-200 rounded animate-pulse"></div>
                      <div className="h-3 bg-gray-200 rounded w-24 animate-pulse"></div>
                    </div>
                  ))}
                </div>
              </div>

              {/* Departure Time Filters Shimmer */}
              <div className="space-y-2">
                <div className="h-4 bg-gray-200 rounded w-28 animate-pulse"></div>
                <div className="grid grid-cols-2 gap-2">
                  {[...Array(6)].map((_, index) => (
                    <div key={index} className="h-8 bg-gray-200 rounded animate-pulse"></div>
                  ))}
                </div>
              </div>

              {/* Return Time Filters Shimmer */}
              <div className="space-y-2">
                <div className="h-4 bg-gray-200 rounded w-24 animate-pulse"></div>
                <div className="grid grid-cols-2 gap-2">
                  {[...Array(6)].map((_, index) => (
                    <div key={index} className="h-8 bg-gray-200 rounded animate-pulse"></div>
                  ))}
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Flight List Shimmer */}
        <div className="flex-1 space-y-4">
          {/* Sort Bar Shimmer */}
          <div className="flex justify-between items-center bg-white p-4 rounded-lg border border-gray-200">
            <div className="h-4 bg-gray-200 rounded w-32 animate-pulse"></div>
            <div className="h-8 bg-gray-200 rounded w-40 animate-pulse"></div>
          </div>

          {/* Check if it's domestic flight for layout */}
          {isDomesticFlight ? (
            /* Domestic Round Trip - Side by Side Layout Shimmer */
            <div className="flex gap-6">
              {/* Outbound Flights */}
              <div className="flex-1 space-y-3">
                <div className="h-6 bg-gray-200 rounded w-32 animate-pulse mb-4"></div>
                {[...Array(4)].map((_, index) => (
                  <div key={index} className="bg-white rounded-xl border border-gray-200 p-4">
                    <div className="flex items-center justify-between mb-3">
                      <div className="flex items-center flex-1">
                        <div className="w-6 h-6 bg-gray-200 rounded-full animate-pulse mr-3"></div>
                        <div className="space-y-1">
                          <div className="h-4 bg-gray-200 rounded w-32 animate-pulse"></div>
                          <div className="h-3 bg-gray-200 rounded w-24 animate-pulse"></div>
                        </div>
                      </div>
                      <div className="text-right space-y-1">
                        <div className="h-6 bg-gray-200 rounded w-20 animate-pulse"></div>
                      </div>
                    </div>
                    <div className="flex justify-between items-center">
                      <div className="flex space-x-8">
                        <div className="space-y-1">
                          <div className="h-5 bg-gray-200 rounded w-16 animate-pulse"></div>
                          <div className="h-3 bg-gray-200 rounded w-12 animate-pulse"></div>
                        </div>
                        <div className="space-y-1">
                          <div className="h-5 bg-gray-200 rounded w-16 animate-pulse"></div>
                          <div className="h-3 bg-gray-200 rounded w-12 animate-pulse"></div>
                        </div>
                      </div>
                      <div className="h-8 bg-gray-200 rounded w-24 animate-pulse"></div>
                    </div>
                  </div>
                ))}
              </div>

              {/* Return Flights */}
              <div className="flex-1 space-y-3">
                <div className="h-6 bg-gray-200 rounded w-32 animate-pulse mb-4"></div>
                {[...Array(4)].map((_, index) => (
                  <div key={index} className="bg-white rounded-xl border border-gray-200 p-4">
                    <div className="flex items-center justify-between mb-3">
                      <div className="flex items-center flex-1">
                        <div className="w-6 h-6 bg-gray-200 rounded-full animate-pulse mr-3"></div>
                        <div className="space-y-1">
                          <div className="h-4 bg-gray-200 rounded w-32 animate-pulse"></div>
                          <div className="h-3 bg-gray-200 rounded w-24 animate-pulse"></div>
                        </div>
                      </div>
                      <div className="text-right space-y-1">
                        <div className="h-6 bg-gray-200 rounded w-20 animate-pulse"></div>
                      </div>
                    </div>
                    <div className="flex justify-between items-center">
                      <div className="flex space-x-8">
                        <div className="space-y-1">
                          <div className="h-5 bg-gray-200 rounded w-16 animate-pulse"></div>
                          <div className="h-3 bg-gray-200 rounded w-12 animate-pulse"></div>
                        </div>
                        <div className="space-y-1">
                          <div className="h-5 bg-gray-200 rounded w-16 animate-pulse"></div>
                          <div className="h-3 bg-gray-200 rounded w-12 animate-pulse"></div>
                        </div>
                      </div>
                      <div className="h-8 bg-gray-200 rounded w-24 animate-pulse"></div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          ) : (
            /* International Round Trip - Stacked Layout Shimmer */
            <div className="space-y-6">
              {/* Outbound Section */}
              <div className="space-y-3">
                <div className="h-6 bg-gray-200 rounded w-32 animate-pulse"></div>
                {[...Array(3)].map((_, index) => (
                  <div key={index} className="bg-white rounded-xl border border-gray-200 p-4">
                    <div className="flex items-center justify-between mb-3">
                      <div className="flex items-center flex-1">
                        <div className="w-6 h-6 bg-gray-200 rounded-full animate-pulse mr-3"></div>
                        <div className="space-y-1">
                          <div className="h-4 bg-gray-200 rounded w-32 animate-pulse"></div>
                          <div className="h-3 bg-gray-200 rounded w-24 animate-pulse"></div>
                        </div>
                      </div>
                      <div className="text-right space-y-1">
                        <div className="h-3 bg-gray-200 rounded w-16 animate-pulse"></div>
                        <div className="h-6 bg-gray-200 rounded w-20 animate-pulse"></div>
                        <div className="h-3 bg-gray-200 rounded w-14 animate-pulse"></div>
                      </div>
                    </div>
                    <div className="flex justify-between items-center">
                      <div className="flex space-x-8">
                        <div className="space-y-1">
                          <div className="h-5 bg-gray-200 rounded w-16 animate-pulse"></div>
                          <div className="h-3 bg-gray-200 rounded w-12 animate-pulse"></div>
                        </div>
                        <div className="space-y-1">
                          <div className="h-5 bg-gray-200 rounded w-16 animate-pulse"></div>
                          <div className="h-3 bg-gray-200 rounded w-12 animate-pulse"></div>
                        </div>
                      </div>
                      <div className="h-8 bg-gray-200 rounded w-24 animate-pulse"></div>
                    </div>
                  </div>
                ))}
              </div>

              {/* Return Section */}
              <div className="space-y-3">
                <div className="h-6 bg-gray-200 rounded w-32 animate-pulse"></div>
                {[...Array(3)].map((_, index) => (
                  <div key={index} className="bg-white rounded-xl border border-gray-200 p-4">
                    <div className="flex items-center justify-between mb-3">
                      <div className="flex items-center flex-1">
                        <div className="w-6 h-6 bg-gray-200 rounded-full animate-pulse mr-3"></div>
                        <div className="space-y-1">
                          <div className="h-4 bg-gray-200 rounded w-32 animate-pulse"></div>
                          <div className="h-3 bg-gray-200 rounded w-24 animate-pulse"></div>
                        </div>
                      </div>
                      <div className="text-right space-y-1">
                        <div className="h-3 bg-gray-200 rounded w-16 animate-pulse"></div>
                        <div className="h-6 bg-gray-200 rounded w-20 animate-pulse"></div>
                        <div className="h-3 bg-gray-200 rounded w-14 animate-pulse"></div>
                      </div>
                    </div>
                    <div className="flex justify-between items-center">
                      <div className="flex space-x-8">
                        <div className="space-y-1">
                          <div className="h-5 bg-gray-200 rounded w-16 animate-pulse"></div>
                          <div className="h-3 bg-gray-200 rounded w-12 animate-pulse"></div>
                        </div>
                        <div className="space-y-1">
                          <div className="h-5 bg-gray-200 rounded w-16 animate-pulse"></div>
                          <div className="h-3 bg-gray-200 rounded w-12 animate-pulse"></div>
                        </div>
                      </div>
                      <div className="h-8 bg-gray-200 rounded w-24 animate-pulse"></div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>
      </div>
    );
  }

  console.log('🔍 RoundTripFlightList RENDER:', {
    outboundFlightsCount: processedOutboundFlights.flights.length,
    returnFlightsCount: processedReturnFlights.flights.length,
    isLoading,
    hasApiData: !!apiData,
    isDomesticFlight,
    fromCountry: searchData?.from?.country,
    toCountry: searchData?.to?.country
  });

  // Use domestic comparison layout for domestic flights
  if (isDomesticFlight) {
    return (
      <div className="domestic-flight-list">
        {/* Horizontal Layout: Filters | Outbound List | Return List */}
        <div className={`flex flex-col lg:flex-row gap-4 h-auto lg:h-[calc(100vh-200px)] min-h-[600px] lg:overflow-hidden ${
          (selectedOutboundFlight || selectedReturnFlight) ? 'pb-32 lg:pb-20' : ''
        }`}>

          {/* 1. Filters Section - Left Side (responsive width) */}
          <div className="w-full lg:w-80 lg:max-w-sm flex-shrink-0 overflow-hidden" style={{ minWidth: '280px' }}>
            <EnhancedFlightFilters
              filters={outboundFilters}
              flights={[...outboundFlights, ...returnFlights]}
              onFiltersChange={(newFilters) => {
                setOutboundFilters(newFilters);
                setReturnFilters(newFilters);
                setPagination(prev => ({ ...prev, currentPage: 1 }));
              }}
              totalFiltered={processedOutboundFlights.totalFiltered + processedReturnFlights.totalFiltered}
              showReturnTimeFilters={false}
            />
          </div>

          {/* 2. Outbound Flight Column - Center (responsive width) */}
          <div className="flex-1 min-w-0 flex flex-col h-auto lg:h-full">
            {/* Column Header */}
            <div className="bg-white rounded-lg border border-gray-200 p-4 mb-4 flex-shrink-0">
              <div className="flex items-center justify-between">
                <div className="min-w-0 flex-1">
                  <h2 className="text-lg font-semibold text-gray-900 truncate">Outbound Flights</h2>
                  <p className="text-sm text-gray-600 truncate">{searchData?.from?.city} → {searchData?.to?.city}</p>
                </div>
                <div className="bg-blue-50 text-blue-700 px-3 py-1 rounded-full text-sm font-medium flex-shrink-0 ml-2">
                  {processedOutboundFlights.flights.length} flights
                </div>
              </div>
            </div>

            {/* Outbound Sort Control */}
            <div className="bg-white rounded-lg border border-gray-200 p-3 mb-4 flex-shrink-0">
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium text-gray-700">Sort by:</span>
                <select
                  value={outboundSortOption}
                  onChange={(e) => setOutboundSortOption(e.target.value as SortOption)}
                  className="border border-gray-300 rounded-md px-3 py-1 text-sm focus:ring-blue-500 focus:border-blue-500"
                >
                  <option value="price_low_high">Price: Low to High</option>
                  <option value="price_high_low">Price: High to Low</option>
                  <option value="duration_short_long">Duration: Short to Long</option>
                  <option value="duration_long_short">Duration: Long to Short</option>
                  <option value="departure_early_late">Departure: Early to Late</option>
                  <option value="departure_late_early">Departure: Late to Early</option>
                </select>
              </div>
            </div>

            {/* Outbound Flight List */}
            <div className="flex-1 overflow-hidden h-96 lg:h-auto">
              <div className="h-full overflow-y-auto pr-2 custom-scrollbar">
                <div className="space-y-2">
                  {processedOutboundFlights.flights.length === 0 ? (
                    <div className="text-center py-12">
                      <div className="text-gray-400 mb-4">
                        <i className="fas fa-plane text-4xl"></i>
                      </div>
                      <h3 className="text-lg font-medium text-gray-900 mb-2">No outbound flights found</h3>
                      <p className="text-gray-500">Try adjusting your filters</p>
                    </div>
                  ) : (
                    processedOutboundFlights.flights.map((flight: FlightListItem) => {
                      const isSelected = isFlightSelected(flight, 'outbound');
                      return (
                        <div
                          key={flight.id}
                          onClick={() => handleOutboundFlightSelect(flight)}
                          className={`cursor-pointer transition-all duration-300 relative overflow-hidden ${
                            isSelected
                              ? 'shadow-xl'
                              : 'hover:shadow-md hover:scale-[1.01]'
                          }`}
                        >
                          {/* Selected overlay with gradient */}
                          {isSelected && (
                            <div className="absolute inset-0 bg-gradient-to-r from-blue-50 to-transparent border-l-4 border-blue-500 z-0"></div>
                          )}

                          {/* Selected checkmark badge */}
                          {isSelected && (
                            <div className="absolute top-3 right-3 bg-blue-500 text-white rounded-full w-7 h-7 flex items-center justify-center text-sm font-bold z-20 shadow-lg animate-pulse">
                              <i className="fas fa-check"></i>
                            </div>
                          )}

                          {/* Selected label */}
                          {isSelected && (
                            <div className="absolute top-0 left-0 bg-blue-500 text-white px-3 py-1 text-xs font-semibold z-20 rounded-br-lg">
                              SELECTED
                            </div>
                          )}
                          <div className="relative z-10">
                            <FlightCard
                              flight={{
                              id: flight.flightNumber || flight.id,
                              airline: flight.airline.name,
                              alliance: flight.airline.code,
                              corporateRate: flight.fareType === 'CORPORATE',
                              departure: {
                                city: flight.departure.city,
                                time: flight.departure.time,
                                terminal: flight.departure.terminal || 'N/A'
                              },
                              arrival: {
                                city: flight.arrival.city,
                                time: flight.arrival.time,
                                terminal: flight.arrival.terminal || 'N/A'
                              },
                              duration: flight.duration,
                              stops: flight.stops,
                              price: Math.round(flight.price.amount),
                              originalPrice: Math.round(flight.price.amount * 1.15),
                              seatsAvailable: flight.availability.seats,
                              aircraft: 'Unknown',
                              amenities: [],
                              fareTypes: [flight.fareType],
                              carbonEmission: '1.5 tons',
                              baggageAllowance: flight.baggage.checked
                            }}
                            onFlightSelect={() => handleOutboundFlightSelect(flight)}
                            onShowDetails={() => onShowFlightDetails?.(flight)}
                            showPriceShimmer={!flight.sh_price}
                            isSelected={isSelected}
                            hideDetailsButton={true}
                          />
                          </div>
                        </div>
                      );
                    })
                  )}
                </div>
              </div>
            </div>
          </div>

          {/* 3. Return Flight Column - Right (responsive width) */}
          <div className="flex-1 min-w-0 flex flex-col h-auto lg:h-full">
            {/* Column Header */}
            <div className="bg-white rounded-lg border border-gray-200 p-4 mb-4 flex-shrink-0">
              <div className="flex items-center justify-between">
                <div className="min-w-0 flex-1">
                  <h2 className="text-lg font-semibold text-gray-900 truncate">Return Flights</h2>
                  <p className="text-sm text-gray-600 truncate">{searchData?.to?.city} → {searchData?.from?.city}</p>
                </div>
                <div className="bg-green-50 text-green-700 px-3 py-1 rounded-full text-sm font-medium flex-shrink-0 ml-2">
                  {processedReturnFlights.flights.length} flights
                </div>
              </div>
            </div>

            {/* Return Sort Control */}
            <div className="bg-white rounded-lg border border-gray-200 p-3 mb-4 flex-shrink-0">
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium text-gray-700">Sort by:</span>
                <select
                  value={returnSortOption}
                  onChange={(e) => setReturnSortOption(e.target.value as SortOption)}
                  className="border border-gray-300 rounded-md px-3 py-1 text-sm focus:ring-blue-500 focus:border-blue-500"
                >
                  <option value="price_low_high">Price: Low to High</option>
                  <option value="price_high_low">Price: High to Low</option>
                  <option value="duration_short_long">Duration: Short to Long</option>
                  <option value="duration_long_short">Duration: Long to Short</option>
                  <option value="departure_early_late">Departure: Early to Late</option>
                  <option value="departure_late_early">Departure: Late to Early</option>
                </select>
              </div>
            </div>

            {/* Return Flight List */}
            <div className="flex-1 overflow-hidden h-96 lg:h-auto">
              <div className="h-full overflow-y-auto pr-2 custom-scrollbar">
                <div className="space-y-2">
                  {processedReturnFlights.flights.length === 0 ? (
                    <div className="text-center py-12">
                      <div className="text-gray-400 mb-4">
                        <i className="fas fa-plane text-4xl"></i>
                      </div>
                      <h3 className="text-lg font-medium text-gray-900 mb-2">No return flights found</h3>
                      <p className="text-gray-500">Try adjusting your filters</p>
                    </div>
                  ) : (
                    processedReturnFlights.flights.map((flight: FlightListItem) => {
                      const isSelected = isFlightSelected(flight, 'return');
                      return (
                        <div
                          key={flight.id}
                          onClick={() => handleReturnFlightSelect(flight)}
                          className={`cursor-pointer transition-all duration-300 relative overflow-hidden ${
                            isSelected
                              ? 'shadow-xl'
                              : 'hover:shadow-md hover:scale-[1.01]'
                          }`}
                        >
                          {/* Selected overlay with gradient */}
                          {isSelected && (
                            <div className="absolute inset-0 bg-gradient-to-r from-green-50 to-transparent border-l-4 border-green-500 z-0"></div>
                          )}

                          {/* Selected checkmark badge */}
                          {isSelected && (
                            <div className="absolute top-3 right-3 bg-green-500 text-white rounded-full w-7 h-7 flex items-center justify-center text-sm font-bold z-20 shadow-lg animate-pulse">
                              <i className="fas fa-check"></i>
                            </div>
                          )}

                          {/* Selected label */}
                          {isSelected && (
                            <div className="absolute top-0 left-0 bg-green-500 text-white px-3 py-1 text-xs font-semibold z-20 rounded-br-lg">
                              SELECTED
                            </div>
                          )}
                          <div className="relative z-10">
                            <FlightCard
                              flight={{
                              id: flight.flightNumber || flight.id,
                              airline: flight.airline.name,
                              alliance: flight.airline.code,
                              corporateRate: flight.fareType === 'CORPORATE',
                              departure: {
                                city: flight.departure.city,
                                time: flight.departure.time,
                                terminal: flight.departure.terminal || 'N/A'
                              },
                              arrival: {
                                city: flight.arrival.city,
                                time: flight.arrival.time,
                                terminal: flight.arrival.terminal || 'N/A'
                              },
                              duration: flight.duration,
                              stops: flight.stops,
                              price: Math.round(flight.price.amount),
                              originalPrice: Math.round(flight.price.amount * 1.15),
                              seatsAvailable: flight.availability.seats,
                              aircraft: 'Unknown',
                              amenities: [],
                              fareTypes: [flight.fareType],
                              carbonEmission: '1.5 tons',
                              baggageAllowance: flight.baggage.checked
                            }}
                            onFlightSelect={() => handleReturnFlightSelect(flight)}
                            onShowDetails={() => onShowFlightDetails?.(flight)}
                            showPriceShimmer={!flight.sh_price}
                            isSelected={isSelected}
                            hideDetailsButton={true}
                          />
                          </div>
                        </div>
                      );
                    })
                  )}
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Sticky Selection Summary - Compact Bottom Bar */}
        {(selectedOutboundFlight || selectedReturnFlight) && (
          <div className="fixed bottom-0 left-0 right-0 bg-white border-t border-gray-200 shadow-lg z-50">
            <div className="max-w-7xl mx-auto px-4 py-3">
              {/* Desktop Layout */}
              <div className="hidden lg:flex items-center justify-between">
                {/* Left side - Flight selections */}
                <div className="flex items-center space-x-6 flex-1">
                  {/* Outbound Flight */}
                  <div className="flex items-center space-x-3">
                    <div className="flex items-center">
                      <i className="fas fa-plane-departure text-blue-500 mr-2"></i>
                      <span className="text-sm font-medium text-gray-700">Outbound:</span>
                    </div>
                    {selectedOutboundFlight ? (
                      <div className="flex items-center space-x-2">
                        <span className="text-sm font-medium text-gray-900">
                          {selectedOutboundFlight.airline.name} {selectedOutboundFlight.flightNumber}
                        </span>
                        <span className="text-sm text-gray-600">
                          {selectedOutboundFlight.departure.time}
                        </span>
                        <span className="text-sm font-bold text-blue-600">
                          ₹{Math.round(selectedOutboundFlight.price.amount).toLocaleString()}
                        </span>
                        <button
                          onClick={() => onShowFlightDetails?.(selectedOutboundFlight)}
                          className="text-blue-600 hover:text-blue-800 text-xs"
                          title="View Details"
                        >
                          <i className="fas fa-info-circle"></i>
                        </button>
                      </div>
                    ) : (
                      <span className="text-sm text-gray-500">Not selected</span>
                    )}
                  </div>

                  {/* Return Flight */}
                  <div className="flex items-center space-x-3">
                    <div className="flex items-center">
                      <i className="fas fa-plane-arrival text-green-500 mr-2"></i>
                      <span className="text-sm font-medium text-gray-700">Return:</span>
                    </div>
                    {selectedReturnFlight ? (
                      <div className="flex items-center space-x-2">
                        <span className="text-sm font-medium text-gray-900">
                          {selectedReturnFlight.airline.name} {selectedReturnFlight.flightNumber}
                        </span>
                        <span className="text-sm text-gray-600">
                          {selectedReturnFlight.departure.time}
                        </span>
                        <span className="text-sm font-bold text-green-600">
                          ₹{Math.round(selectedReturnFlight.price.amount).toLocaleString()}
                        </span>
                        <button
                          onClick={() => onShowFlightDetails?.(selectedReturnFlight)}
                          className="text-green-600 hover:text-green-800 text-xs"
                          title="View Details"
                        >
                          <i className="fas fa-info-circle"></i>
                        </button>
                      </div>
                    ) : (
                      <span className="text-sm text-gray-500">Not selected</span>
                    )}
                  </div>
                </div>

                {/* Right side - Total and actions */}
                <div className="flex items-center space-x-4">
                  {selectedOutboundFlight && selectedReturnFlight && (
                    <div className="text-right">
                      <div className="text-xs text-gray-500">Total</div>
                      <div className="text-lg font-bold text-gray-900">
                        ₹{Math.round(getTotalPrice()).toLocaleString()}
                      </div>
                    </div>
                  )}

                  <div className="flex items-center space-x-2">
                    <button
                      onClick={() => {
                        setSelectedOutboundFlight(null);
                        setSelectedReturnFlight(null);
                        localStorage.removeItem('selectedOutboundFlight');
                        localStorage.removeItem('selectedReturnFlight');
                      }}
                      className="text-gray-400 hover:text-red-600 transition-colors p-1"
                      title="Clear All"
                    >
                      <i className="fas fa-times"></i>
                    </button>

                    {selectedOutboundFlight && selectedReturnFlight && (
                      <button
                        className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-lg font-medium transition-colors text-sm"
                        onClick={() => {
                          console.log('Proceeding with selected flights:', {
                            outbound: selectedOutboundFlight,
                            return: selectedReturnFlight,
                            totalPrice: getTotalPrice()
                          });
                          // Here you would typically navigate to booking page
                        }}
                      >
                        Continue to Book
                      </button>
                    )}
                  </div>
                </div>
              </div>

              {/* Mobile Layout */}
              <div className="lg:hidden space-y-3">
                {/* Flight selections */}
                <div className="space-y-2">
                  {/* Outbound Flight */}
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-2">
                      <i className="fas fa-plane-departure text-blue-500"></i>
                      <span className="text-xs font-medium text-gray-700">Outbound</span>
                    </div>
                    {selectedOutboundFlight ? (
                      <div className="flex items-center space-x-2">
                        <span className="text-xs font-medium text-gray-900">
                          {selectedOutboundFlight.airline.name}
                        </span>
                        <span className="text-xs font-bold text-blue-600">
                          ₹{Math.round(selectedOutboundFlight.price.amount).toLocaleString()}
                        </span>
                        <button
                          onClick={() => onShowFlightDetails?.(selectedOutboundFlight)}
                          className="text-blue-600 hover:text-blue-800 text-xs"
                        >
                          <i className="fas fa-info-circle"></i>
                        </button>
                      </div>
                    ) : (
                      <span className="text-xs text-gray-500">Not selected</span>
                    )}
                  </div>

                  {/* Return Flight */}
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-2">
                      <i className="fas fa-plane-arrival text-green-500"></i>
                      <span className="text-xs font-medium text-gray-700">Return</span>
                    </div>
                    {selectedReturnFlight ? (
                      <div className="flex items-center space-x-2">
                        <span className="text-xs font-medium text-gray-900">
                          {selectedReturnFlight.airline.name}
                        </span>
                        <span className="text-xs font-bold text-green-600">
                          ₹{Math.round(selectedReturnFlight.price.amount).toLocaleString()}
                        </span>
                        <button
                          onClick={() => onShowFlightDetails?.(selectedReturnFlight)}
                          className="text-green-600 hover:text-green-800 text-xs"
                        >
                          <i className="fas fa-info-circle"></i>
                        </button>
                      </div>
                    ) : (
                      <span className="text-xs text-gray-500">Not selected</span>
                    )}
                  </div>
                </div>

                {/* Actions */}
                <div className="flex items-center justify-between pt-2 border-t border-gray-200">
                  <div className="flex items-center space-x-3">
                    {selectedOutboundFlight && selectedReturnFlight && (
                      <div>
                        <span className="text-xs text-gray-500">Total: </span>
                        <span className="text-sm font-bold text-gray-900">
                          ₹{Math.round(getTotalPrice()).toLocaleString()}
                        </span>
                      </div>
                    )}
                  </div>

                  <div className="flex items-center space-x-2">
                    <button
                      onClick={() => {
                        setSelectedOutboundFlight(null);
                        setSelectedReturnFlight(null);
                        localStorage.removeItem('selectedOutboundFlight');
                        localStorage.removeItem('selectedReturnFlight');
                      }}
                      className="text-gray-400 hover:text-red-600 transition-colors p-1"
                      title="Clear All"
                    >
                      <i className="fas fa-times"></i>
                    </button>

                    {selectedOutboundFlight && selectedReturnFlight && (
                      <button
                        className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg font-medium transition-colors text-xs"
                        onClick={() => {
                          console.log('Proceeding with selected flights:', {
                            outbound: selectedOutboundFlight,
                            return: selectedReturnFlight,
                            totalPrice: getTotalPrice()
                          });
                          // Here you would typically navigate to booking page
                        }}
                      >
                        Continue
                      </button>
                    )}
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    );
  }

  // International flights - use the existing vertical layout
  return (
    <div className="space-y-8">
      {/* Outbound Flights Section */}
      <div>
        <h2 className="text-xl font-semibold text-gray-900 mb-4">Outbound Flights</h2>
        <div className="flex gap-4 lg:gap-6 xl:gap-8">
          {/* Outbound Filters */}
          <EnhancedFlightFilters
            filters={outboundFilters}
            flights={outboundFlights}
            onFiltersChange={handleOutboundFilterChange}
            totalFiltered={processedOutboundFlights.totalFiltered}
            showReturnTimeFilters={false}
          />

          {/* Outbound Flight Results */}
          <div className="flex-1 min-w-0">
            {/* Sort and Results Header */}
            <div className="flex items-center justify-between mb-4 p-4 bg-white rounded-lg border border-gray-200">
              <div>
                <h3 className="text-lg font-semibold text-gray-900">
                  {processedOutboundFlights.totalFiltered} outbound flights found
                </h3>
                <p className="text-sm text-gray-500">
                  Showing {processedOutboundFlights.flights.length} of {processedOutboundFlights.totalFiltered} flights
                </p>
              </div>

              <div className="flex items-center space-x-4">
                <label className="text-sm font-medium text-gray-700">Sort by:</label>
                <select
                  value={outboundSortOption}
                  onChange={(e) => setOutboundSortOption(e.target.value as SortOption)}
                  className="border border-gray-300 rounded-md px-3 py-2 text-sm focus:ring-blue-500 focus:border-blue-500"
                >
                  <option value="price_low_high">Price: Low to High</option>
                  <option value="price_high_low">Price: High to Low</option>
                  <option value="duration_short_long">Duration: Short to Long</option>
                  <option value="duration_long_short">Duration: Long to Short</option>
                  <option value="departure_early_late">Departure: Early to Late</option>
                  <option value="departure_late_early">Departure: Late to Early</option>
                  <option value="arrival_early_late">Arrival: Early to Late</option>
                  <option value="arrival_late_early">Arrival: Late to Early</option>
                </select>
              </div>
            </div>

            {/* Outbound Flight Cards */}
            <div className="space-y-2 lg:space-y-3 pb-8">
              {processedOutboundFlights.flights.length === 0 ? (
                <div className="text-center py-12">
                  <div className="text-gray-400 mb-4">
                    <i className="fas fa-plane text-4xl"></i>
                  </div>
                  <h3 className="text-lg font-medium text-gray-900 mb-2">No outbound flights found</h3>
                  <p className="text-gray-500">Try adjusting your filters or search criteria</p>
                </div>
              ) : (
                processedOutboundFlights.flights.map((flight: FlightListItem) => (
                  <div key={flight.id} onClick={() => console.log('Selected outbound flight:', flight)} className="cursor-pointer">
                    <FlightCard
                      flight={{
                        id: flight.flightNumber || flight.id,
                        airline: flight.airline.name,
                        alliance: flight.airline.code,
                        corporateRate: flight.fareType === 'CORPORATE',
                        departure: {
                          city: flight.departure.city,
                          time: flight.departure.time,
                          terminal: flight.departure.terminal || 'N/A'
                        },
                        arrival: {
                          city: flight.arrival.city,
                          time: flight.arrival.time,
                          terminal: flight.arrival.terminal || 'N/A'
                        },
                        duration: flight.duration,
                        stops: flight.stops,
                        price: Math.round(flight.price.amount),
                        originalPrice: Math.round(flight.price.amount * 1.15),
                        seatsAvailable: flight.availability.seats,
                        aircraft: 'Unknown',
                        amenities: [],
                        fareTypes: [flight.fareType],
                        carbonEmission: '1.5 tons',
                        baggageAllowance: flight.baggage.checked
                      }}
                      onFlightSelect={() => console.log('Selected outbound flight:', flight)}
                      onShowDetails={() => onShowFlightDetails?.(flight)}
                      showPriceShimmer={!flight.sh_price} // Show shimmer when sh_price is false
                      hideDetailsButton={false}
                    />
                  </div>
                ))
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Return Flights Section */}
      <div>
        <h2 className="text-xl font-semibold text-gray-900 mb-4">Return Flights</h2>
        <div className="flex gap-4 lg:gap-6 xl:gap-8">
          {/* Return Filters */}
          <EnhancedFlightFilters
            filters={returnFilters}
            flights={returnFlights}
            onFiltersChange={handleReturnFilterChange}
            totalFiltered={processedReturnFlights.totalFiltered}
            showReturnTimeFilters={true}
          />

          {/* Return Flight Results */}
          <div className="flex-1 min-w-0">
            {/* Sort and Results Header */}
            <div className="flex items-center justify-between mb-4 p-4 bg-white rounded-lg border border-gray-200">
              <div>
                <h3 className="text-lg font-semibold text-gray-900">
                  {processedReturnFlights.totalFiltered} return flights found
                </h3>
                <p className="text-sm text-gray-500">
                  Showing {processedReturnFlights.flights.length} of {processedReturnFlights.totalFiltered} flights
                </p>
              </div>

              <div className="flex items-center space-x-4">
                <label className="text-sm font-medium text-gray-700">Sort by:</label>
                <select
                  value={returnSortOption}
                  onChange={(e) => setReturnSortOption(e.target.value as SortOption)}
                  className="border border-gray-300 rounded-md px-3 py-2 text-sm focus:ring-blue-500 focus:border-blue-500"
                >
                  <option value="price_low_high">Price: Low to High</option>
                  <option value="price_high_low">Price: High to Low</option>
                  <option value="duration_short_long">Duration: Short to Long</option>
                  <option value="duration_long_short">Duration: Long to Short</option>
                  <option value="departure_early_late">Departure: Early to Late</option>
                  <option value="departure_late_early">Departure: Late to Early</option>
                  <option value="arrival_early_late">Arrival: Early to Late</option>
                  <option value="arrival_late_early">Arrival: Late to Early</option>
                </select>
              </div>
            </div>

            {/* Return Flight Cards */}
            <div className="space-y-2 lg:space-y-3 pb-8">
              {processedReturnFlights.flights.length === 0 ? (
                <div className="text-center py-12">
                  <div className="text-gray-400 mb-4">
                    <i className="fas fa-plane text-4xl"></i>
                  </div>
                  <h3 className="text-lg font-medium text-gray-900 mb-2">No return flights found</h3>
                  <p className="text-gray-500">Try adjusting your filters or search criteria</p>
                </div>
              ) : (
                processedReturnFlights.flights.map((flight: FlightListItem) => (
                  <div key={flight.id} onClick={() => console.log('Selected return flight:', flight)} className="cursor-pointer">
                    <FlightCard
                      flight={{
                        id: flight.flightNumber || flight.id,
                        airline: flight.airline.name,
                        alliance: flight.airline.code,
                        corporateRate: flight.fareType === 'CORPORATE',
                        departure: {
                          city: flight.departure.city,
                          time: flight.departure.time,
                          terminal: flight.departure.terminal || 'N/A'
                        },
                        arrival: {
                          city: flight.arrival.city,
                          time: flight.arrival.time,
                          terminal: flight.arrival.terminal || 'N/A'
                        },
                        duration: flight.duration,
                        stops: flight.stops,
                        price: Math.round(flight.price.amount),
                        originalPrice: Math.round(flight.price.amount * 1.15),
                        seatsAvailable: flight.availability.seats,
                        aircraft: 'Unknown',
                        amenities: [],
                        fareTypes: [flight.fareType],
                        carbonEmission: '1.5 tons',
                        baggageAllowance: flight.baggage.checked
                      }}
                      onFlightSelect={() => console.log('Selected return flight:', flight)}
                      onShowDetails={() => onShowFlightDetails?.(flight)}
                      showPriceShimmer={!flight.sh_price} // Show shimmer when sh_price is false
                      hideDetailsButton={false}
                    />
                  </div>
                ))
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Performance Metrics */}
      {apiMetadata && (
        <div className="mt-4 p-3 bg-gray-100 rounded-lg text-xs text-gray-600">
          <div>Response Time: {apiMetadata.responseTimeMS || 0}ms</div>
          <div>Cache Hit: {apiMetadata.cacheHit ? 'Yes' : 'No'}</div>
          <div>Data Source: {apiMetadata.dataSource || 'Unknown'}</div>
          <div>Total Outbound Flights: {processedOutboundFlights.totalFiltered}</div>
          <div>Total Return Flights: {processedReturnFlights.totalFiltered}</div>
        </div>
      )}
    </div>
  );
};

export default RoundTripFlightList;
