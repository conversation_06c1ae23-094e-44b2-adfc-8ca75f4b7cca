// InlineModifySearch.scss - Modal styling for the modify search component

.inline-modify-search-container {
  // Animation for modal appearance
  .modal-content {
    animation: modalSlideIn 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  }

  @keyframes modalSlideIn {
    from {
      opacity: 0;
      transform: scale(0.95) translateY(-20px);
    }
    to {
      opacity: 1;
      transform: scale(1) translateY(0);
    }
  }

  // Compact styling for FlightSearchForm when used in modal
  .compact-search-form {
    // Target the compact form specifically
    .compact-form {
      // Make all inputs smaller and more compact
      input[type="text"] {
        padding: 0.375rem 2.25rem 0.375rem 2.25rem !important;
        font-size: 0.8125rem !important;
        height: 2.25rem !important;
        border-radius: 0.5rem !important;
      }

      // Reduce label sizes
      label {
        font-size: 0.75rem !important;
        margin-bottom: 0.25rem !important;

        i {
          font-size: 0.625rem !important;
        }
      }

      // Make buttons smaller
      button {
        padding: 0.25rem 0.5rem !important;
        font-size: 0.75rem !important;
        border-radius: 0.375rem !important;

        &.w-8.h-8 {
          width: 1.5rem !important;
          height: 1.5rem !important;
        }
      }

      // Make specific buttons more compact
      .flex.items-end button {
        height: 2.25rem !important;
        padding: 0.375rem 0.75rem !important;
      }

      // Reduce spacing in grid layouts
      .grid {
        gap: 0.5rem !important;
      }

      .gap-4, .gap-6 {
        gap: 0.5rem !important;
      }

      .space-x-4 > * + * {
        margin-left: 0.375rem !important;
      }

      .mb-4, .mb-6 {
        margin-bottom: 0.5rem !important;
      }

      .mb-2 {
        margin-bottom: 0.25rem !important;
      }

      // Make dropdowns more compact
      .max-h-80 {
        max-height: 10rem !important;
      }

      // Compact advanced search
      .px-4 {
        padding-left: 0.375rem !important;
        padding-right: 0.375rem !important;
      }

      .py-3 {
        padding-top: 0.375rem !important;
        padding-bottom: 0.375rem !important;
      }

      // Reduce icon sizes
      i {
        font-size: 0.625rem !important;
      }

      // Make the search button more compact
      .bg-blue-600 {
        padding: 0.375rem 0.75rem !important;
        font-size: 0.8125rem !important;
        height: 2.25rem !important;
      }

      // Compact trip type selector
      .trip-type-selector {
        margin-bottom: 0.375rem !important;

        button {
          padding: 0.25rem 0.5rem !important;
          font-size: 0.75rem !important;
          height: 1.75rem !important;
        }
      }

      // Reduce text sizes
      .text-sm {
        font-size: 0.75rem !important;
      }

      .text-xs {
        font-size: 0.625rem !important;
      }

      // Compact error messages
      .text-red-600 {
        font-size: 0.625rem !important;
      }

      // Make the form more compact overall
      .p-4, .p-6 {
        padding: 0.375rem !important;
      }

      .py-8 {
        padding-top: 0 !important;
        padding-bottom: 0 !important;
      }

      // Compact the date/travelers section
      .md\\:grid-cols-2 {
        gap: 0.5rem !important;
      }

      // Make the advanced search toggle smaller
      .flex.items-end button {
        height: 2.25rem !important;
        padding: 0.375rem 0.75rem !important;
        font-size: 0.75rem !important;
      }
    }

    // Remove scaling for calendar modal to match FlightSearchForm style
    .calendar-modal {
      // Use same size as FlightSearchForm calendar
      transform: none !important;
    }

    .travel-class-picker {
      transform: scale(0.85) !important;
      transform-origin: top left !important;
    }

    // Make dropdown items more compact
    .dropdown-item {
      padding: 0.375rem 0.5rem !important;
      font-size: 0.75rem !important;
    }
  }
}
