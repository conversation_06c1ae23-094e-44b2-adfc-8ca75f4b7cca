import React, { createContext, useContext, useState, useEffect, useCallback } from 'react';
import type { ReactNode } from 'react';

// Types for flight search data
export interface Airport {
  city: string;
  airport: string;
  iata: string;
  country: string;
  airportOpen: boolean;
}

export interface AdvancedSearch {
  selectedAirlines: string[];
  flightOptions: {
    directFlights: boolean;
    refundableFares: boolean;
    corporateRates: boolean;
  };
  services: {
    airportLounge: boolean;
    extraBaggage: boolean;
    travelInsurance: boolean;
  };
  priceRange?: {
    min: number;
    max: number;
  };
  departureTime?: {
    earliest: string;
    latest: string;
  };
  arrivalTime?: {
    earliest: string;
    latest: string;
  };
  stops?: 'any' | 'nonstop' | '1stop' | '2+stops';
  duration?: {
    max: number; // in hours
  };
  baggage?: {
    carryOn: boolean;
    checked: boolean;
  };
}

export interface FlightTrip {
  from: Airport;
  to: Airport;
  depart: Date;
  return: Date | null;
}

export interface FlightSearchData {
  // For backward compatibility, keep single trip fields for one-way/round-trip
  from: Airport;
  to: Airport;
  depart: Date;
  return: Date | null;
  // New field for multi-city support
  trips?: FlightTrip[];
  advanced_search: AdvancedSearch;
  tripType: 'roundTrip' | 'oneWay' | 'multiCity';
  passengers: {
    adults: number;
    children: number;
    infants: number;
  };
  class: 'economy' | 'premiumEconomy' | 'business' | 'first';
}

export interface FlightSearchHistory {
  id: string;
  searchData: FlightSearchData;
  timestamp: Date;
  searchCount: number;
}

interface FlightSearchContextType {
  currentSearch: FlightSearchData | null;
  searchHistory: FlightSearchHistory[];
  isLoading: boolean;
  updateSearch: (searchData: Partial<FlightSearchData>) => void;
  saveSearch: () => void;
  loadSearch: (searchId: string) => void;
  clearSearch: () => void;
  deleteFromHistory: (searchId: string) => void;
  isSearchValid: () => boolean;
  getRecentSearches: (limit?: number) => FlightSearchHistory[];
}

const FlightSearchContext = createContext<FlightSearchContextType | undefined>(undefined);

const STORAGE_KEY = 'flightSearchData';
const HISTORY_KEY = 'flightSearchHistory';

// Default values
const defaultAirport: Airport = {
  city: '',
  airport: '',
  iata: '',
  country: '',
  airportOpen: false,
};

const defaultAdvancedSearch: AdvancedSearch = {
  selectedAirlines: [],
  flightOptions: {
    directFlights: false,
    refundableFares: false,
    corporateRates: false,
  },
  services: {
    airportLounge: false,
    extraBaggage: false,
    travelInsurance: false,
  },
  stops: 'any',
  baggage: {
    carryOn: false,
    checked: false,
  },
};

const defaultSearchData: FlightSearchData = {
  from: { ...defaultAirport },
  to: { ...defaultAirport },
  depart: new Date(),
  return: null,
  advanced_search: { ...defaultAdvancedSearch },
  tripType: 'roundTrip',
  passengers: {
    adults: 1,
    children: 0,
    infants: 0,
  },
  class: 'economy',
};

export const FlightSearchProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  const [currentSearch, setCurrentSearch] = useState<FlightSearchData | null>(null);
  const [searchHistory, setSearchHistory] = useState<FlightSearchHistory[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  // Load data from localStorage on mount
  useEffect(() => {
    loadFromStorage();
  }, []);

  // Save to localStorage whenever currentSearch changes
  useEffect(() => {
    if (currentSearch) {
      localStorage.setItem(STORAGE_KEY, JSON.stringify(currentSearch));
    }
  }, [currentSearch]);

  // Save search history to localStorage
  useEffect(() => {
    localStorage.setItem(HISTORY_KEY, JSON.stringify(searchHistory));
  }, [searchHistory]);

  const loadFromStorage = () => {
    console.log('🔄 Loading flight search data from localStorage...');
    setIsLoading(true);

    try {
      // Load current search
      const savedSearch = localStorage.getItem(STORAGE_KEY);
      if (savedSearch) {
        console.log('📦 Found saved search data in localStorage:', savedSearch);
        const parsedSearch = JSON.parse(savedSearch);

        // Convert date strings back to Date objects
        parsedSearch.depart = new Date(parsedSearch.depart);
        if (parsedSearch.return) {
          parsedSearch.return = new Date(parsedSearch.return);
        }

        // Convert dates in trips array if it exists
        if (parsedSearch.trips && Array.isArray(parsedSearch.trips)) {
          parsedSearch.trips = parsedSearch.trips.map((trip: any) => ({
            ...trip,
            depart: new Date(trip.depart),
            return: trip.return ? new Date(trip.return) : null,
          }));
        }

        console.log('✅ Loaded search data from localStorage:', {
          from: parsedSearch.from,
          to: parsedSearch.to,
          depart: parsedSearch.depart,
          return: parsedSearch.return,
          tripType: parsedSearch.tripType,
          passengers: parsedSearch.passengers,
          class: parsedSearch.class,
          trips: parsedSearch.trips
        });

        setCurrentSearch(parsedSearch);
      } else {
        // Check for legacy data and migrate it
        const legacySearch = localStorage.getItem('recentFlightSearch');
        if (legacySearch) {
          console.log('🔄 Found legacy search data, migrating...');
          try {
            const parsedLegacy = JSON.parse(legacySearch);
            const firstTrip = parsedLegacy.trips?.[0];

            if (firstTrip) {
              const tripType = parsedLegacy.FareType === 'RT' ? 'roundTrip' as const :
                              parsedLegacy.FareType === 'ON' ? 'oneWay' as const : 'multiCity' as const;

              const migratedData: FlightSearchData = {
                // Legacy fields for backward compatibility
                from: {
                  city: firstTrip.from.city,
                  airport: firstTrip.from.airport,
                  iata: firstTrip.from.iata,
                  country: firstTrip.from.country,
                  airportOpen: false,
                },
                to: {
                  city: firstTrip.to.city,
                  airport: firstTrip.to.airport,
                  iata: firstTrip.to.iata,
                  country: firstTrip.to.country,
                  airportOpen: false,
                },
                depart: new Date(firstTrip.depart),
                return: firstTrip.return ? new Date(firstTrip.return) : null,
                tripType,
                passengers: {
                  adults: parsedLegacy.travellers.adult,
                  children: parsedLegacy.travellers.child,
                  infants: parsedLegacy.travellers.infant,
                },
                class: parsedLegacy.cabin === 'E' ? 'economy' as const :
                       parsedLegacy.cabin === 'PE' ? 'premiumEconomy' as const :
                       parsedLegacy.cabin === 'B' ? 'business' as const : 'first' as const,
                advanced_search: { ...defaultAdvancedSearch },
              };

              // Add trips array for multi-city searches
              if (tripType === 'multiCity' && parsedLegacy.trips.length > 1) {
                migratedData.trips = parsedLegacy.trips.map((trip: any) => ({
                  from: {
                    city: trip.from.city,
                    airport: trip.from.airport,
                    iata: trip.from.iata,
                    country: trip.from.country,
                    airportOpen: false,
                  },
                  to: {
                    city: trip.to.city,
                    airport: trip.to.airport,
                    iata: trip.to.iata,
                    country: trip.to.country,
                    airportOpen: false,
                  },
                  depart: new Date(trip.depart),
                  return: trip.return ? new Date(trip.return) : null,
                }));
              }

              setCurrentSearch(migratedData);
              localStorage.setItem(STORAGE_KEY, JSON.stringify(migratedData));
              localStorage.removeItem('recentFlightSearch'); // Clean up legacy data
              console.log('✅ Successfully migrated legacy search data');
            } else {
              setCurrentSearch({ ...defaultSearchData });
            }
          } catch (error) {
            console.error('❌ Error migrating legacy search data:', error);
            setCurrentSearch({ ...defaultSearchData });
          }
        } else {
          console.log('📝 No saved search data found, using defaults');
          setCurrentSearch({ ...defaultSearchData });
        }
      }

      // Load search history
      const savedHistory = localStorage.getItem(HISTORY_KEY);
      if (savedHistory) {
        const parsedHistory = JSON.parse(savedHistory);
        // Convert date strings back to Date objects
        const historyWithDates = parsedHistory.map((item: any) => {
          const searchData = {
            ...item.searchData,
            depart: new Date(item.searchData.depart),
            return: item.searchData.return ? new Date(item.searchData.return) : null,
          };

          // Convert dates in trips array if it exists
          if (searchData.trips && Array.isArray(searchData.trips)) {
            searchData.trips = searchData.trips.map((trip: any) => ({
              ...trip,
              depart: new Date(trip.depart),
              return: trip.return ? new Date(trip.return) : null,
            }));
          }

          return {
            ...item,
            timestamp: new Date(item.timestamp),
            searchData,
          };
        });
        setSearchHistory(historyWithDates);
        console.log(`📚 Loaded ${historyWithDates.length} items from search history`);
      }
    } catch (error) {
      console.error('❌ Error loading flight search data from localStorage:', error);
      setCurrentSearch({ ...defaultSearchData });
      setSearchHistory([]);
    } finally {
      setIsLoading(false);
      console.log('✅ localStorage loading completed');
    }
  };

  const updateSearch = useCallback((searchData: Partial<FlightSearchData>) => {
    setCurrentSearch(prev => prev ? { ...prev, ...searchData } : { ...defaultSearchData, ...searchData });
  }, []);

  const isSearchValid = (): boolean => {
    if (!currentSearch) return false;
    
    return (
      currentSearch.from.iata !== '' &&
      currentSearch.to.iata !== '' &&
      currentSearch.from.iata !== currentSearch.to.iata &&
      currentSearch.depart instanceof Date &&
      !isNaN(currentSearch.depart.getTime()) &&
      (currentSearch.tripType === 'oneWay' || 
       (currentSearch.return instanceof Date && !isNaN(currentSearch.return.getTime()))) &&
      currentSearch.passengers.adults > 0
    );
  };

  const saveSearch = () => {
    if (!currentSearch || !isSearchValid()) {
      console.warn('Cannot save invalid search data');
      return;
    }

    const searchId = `search_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
    
    // Check if similar search exists
    const existingSearchIndex = searchHistory.findIndex(item => 
      item.searchData.from.iata === currentSearch.from.iata &&
      item.searchData.to.iata === currentSearch.to.iata &&
      item.searchData.depart.toDateString() === currentSearch.depart.toDateString() &&
      item.searchData.tripType === currentSearch.tripType
    );

    if (existingSearchIndex !== -1) {
      // Update existing search count
      const updatedHistory = [...searchHistory];
      updatedHistory[existingSearchIndex] = {
        ...updatedHistory[existingSearchIndex],
        searchCount: updatedHistory[existingSearchIndex].searchCount + 1,
        timestamp: new Date(),
      };
      setSearchHistory(updatedHistory);
    } else {
      // Add new search to history
      const newHistoryItem: FlightSearchHistory = {
        id: searchId,
        searchData: { ...currentSearch },
        timestamp: new Date(),
        searchCount: 1,
      };

      setSearchHistory(prev => [newHistoryItem, ...prev.slice(0, 19)]); // Keep only last 20 searches
    }
  };

  const loadSearch = (searchId: string) => {
    const searchItem = searchHistory.find(item => item.id === searchId);
    if (searchItem) {
      setCurrentSearch({ ...searchItem.searchData });
    }
  };

  const clearSearch = () => {
    setCurrentSearch({ ...defaultSearchData });
    localStorage.removeItem(STORAGE_KEY);
  };

  const deleteFromHistory = (searchId: string) => {
    setSearchHistory(prev => prev.filter(item => item.id !== searchId));
  };

  const getRecentSearches = (limit: number = 5): FlightSearchHistory[] => {
    return searchHistory
      .sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime())
      .slice(0, limit);
  };

  const value: FlightSearchContextType = {
    currentSearch,
    searchHistory,
    isLoading,
    updateSearch,
    saveSearch,
    loadSearch,
    clearSearch,
    deleteFromHistory,
    isSearchValid,
    getRecentSearches,
  };

  return (
    <FlightSearchContext.Provider value={value}>
      {children}
    </FlightSearchContext.Provider>
  );
};

export const useFlightSearch = (): FlightSearchContextType => {
  const context = useContext(FlightSearchContext);
  if (context === undefined) {
    throw new Error('useFlightSearch must be used within a FlightSearchProvider');
  }
  return context;
};

export default FlightSearchContext;
